const axios = require('axios');
const fs = require('fs');

const LANG_LIST = ['zh_TW','en'];
const DEFAULT_LANG = 'zh_TW';

const LIST_LANG_FOLDERS = [__dirname + '/src/i18n/locales'];

function sortObject(obj) {
  const sortedObj = {};
  Object.keys(obj)
    .sort((a, b) => a.localeCompare(b))
    .forEach((key) => {
      sortedObj[key] = obj[key];
    });
  return sortedObj;
}

async function getSubFolders(folder) {
  return new Promise((resolve, reject) => {
    fs.readdir(folder, (err, files) => {
      if (err) {
        reject(err);
      } else {
        resolve(
          files.filter((file) => {
            return fs.statSync(`${folder}/${file}`).isDirectory();
          })
        );
      }
    });
  });
}

async function generateTranslationForFolder(folderRoot, lang) {
  console.log(`Generating translation for ${folderRoot} ${lang}`);
  // get all subfolders of the current folder
  const subFolders = await getSubFolders(folderRoot);

  const allFolders = subFolders.map((subFolder) => `${folderRoot}/${subFolder}`);

  for (let k = 0; k < allFolders.length; k++) {
    const folder = allFolders[k];
    await generateTranslationForFolder(folder, lang);
  }

  const targetLangPath = `${folderRoot}/${lang}.json`;
  const defaultLangPath = `${folderRoot}/${DEFAULT_LANG}.json`;

  // Check if the default lang file exists
  if (!fs.existsSync(defaultLangPath)) {
    console.error(`Missing ${defaultLangPath}, skipping...`);
    return;
  }

  // Ensure the target lang file exists
  if (!fs.existsSync(targetLangPath)) {
    console.log(`Missing ${targetLangPath}, creating new file...`);
    fs.writeFileSync(targetLangPath, '');
  }
  const targetLangObj = JSON.parse(fs.readFileSync(targetLangPath, 'utf8')) || {};
  const defaultLangObj = JSON.parse(fs.readFileSync(defaultLangPath, 'utf8')) || {};

  await fixTranslationForLang(lang, targetLangObj, defaultLangObj);

  // Sort the object
  const sortedLangObj = sortObject(targetLangObj);
  // Override the original file
  const sortedLangObjString = JSON.stringify(sortedLangObj, null, 2);
  fs.writeFileSync(targetLangPath, sortedLangObjString);
  console.log(`Updated ${targetLangPath}`);
}

async function generateTranslation() {
  for (let j = 0; j < LANG_LIST.length; j++) {
    const lang = LANG_LIST[j];
    for (let i = 0; i < LIST_LANG_FOLDERS.length; i++) {
      const folderRoot = LIST_LANG_FOLDERS[i];
      await generateTranslationForFolder(folderRoot, lang);
    }
  }
}

async function fixTranslationForLang(lang, targetLangObj, defaultLangObj, level = 0) {
  if (level > 5) return;
  const targetLangKeys = Object.keys(targetLangObj);
  const defaultLangKeys = Object.keys(defaultLangObj);

  const missingKeys = defaultLangKeys.filter((key) => {
    if (typeof defaultLangObj[key] === 'string') {
      return !targetLangKeys.includes(key);
    } else if (typeof defaultLangObj[key] === 'object') {
      if (defaultLangObj[key] != null) {
        if (targetLangObj[key] == null) return true;
        console.log(`Checking ${key}`);
        return Object.keys(defaultLangObj[key]).some((childKey) => {
          return !targetLangObj[key][childKey];
        });
      }
      return false;
    } else {
      throw new Error(`Unhandled type ${typeof defaultLangObj[key]}`);
    }
  });

  if (missingKeys.length > 0) {
    for (let j = 0; j < missingKeys.length; j++) {
      const key = missingKeys[j];
      const text = defaultLangObj[key];

      if (typeof text === 'string') {
        const translatedText = await translateText(text, DEFAULT_LANG, lang);
        console.log(`Translated ${key} from ${DEFAULT_LANG} to ${lang}: ${translatedText}`);
        targetLangObj[key] = translatedText || '';
      } else if (typeof text === 'object' && text != null) {
        if (targetLangObj[key] == null) targetLangObj[key] = {};
        await fixTranslationForLang(lang, targetLangObj[key], defaultLangObj[key], level + 1);
      } else {
        throw new Error(`Unhandled type ${typeof text}`);
      }
    }
  }
}

const findHtmlBlocks = (htmlString) => {
  const regex = /<(\w+)([^>]*)>(.*?)<\/\1>/gs;
  const htmlBlocks = htmlString.match(regex) || [];

  return htmlBlocks;
};

const findCurlyBracesVariables = (str) => {
  return str.match(/{([^}]+)}/g); // Matches {variable}
};

const findDoubleQuotesVariables = (str) => {
  return str.match(/"([^"]+)"/g); // Matches "variable"
};

const findPatterns = (str) => {
  const htmlTags = findHtmlBlocks(str);
  const curlyBracesVariables = findCurlyBracesVariables(str);
  const doubleQuotesVariables = findDoubleQuotesVariables(str);

  return (htmlTags || []).concat(curlyBracesVariables || [], doubleQuotesVariables || []);
};

const translateText = async (text, srcLang, targetLang) => {
  if (typeof text !== 'string') throw new Error(`text ${text} is not string`);
  // Replace all the patterns with unique keys to avoid translation
  // e.g. <span>Hi</span> => __html_tag_1__
  // e.g. {variable} => __curly_braces_1__
  // e.g. "variable" => __double_quotes_1__
  // e.g. "Hi {variable}" => __double_quotes_1__ __curly_braces_1__
  // After translation, replace back the unique keys with the original patterns
  const patterns = findPatterns(text);
  const uniqueKeys = patterns.map((pattern, index) => `{{${index}}}`);
  const uniqueText = patterns.reduce((acc, pattern, index) => {
    return acc.replace(pattern, uniqueKeys[index]);
  }, text);

  const encodedText = encodeURI(uniqueText);

  const config = {
    method: 'get',
    maxBodyLength: Infinity,
    url: `https://translate.googleapis.com/translate_a/single?client=gtx&sl=auto&tl=${targetLang}&hl=${srcLang}&dt=t&dt=bd&dj=1&source=input&tk=800461.800461&q=${encodedText}`,
    headers: {
      authority: 'translate.googleapis.com',
      accept: '*/*',
      'accept-language': 'vi,pt-BR;q=0.9,pt;q=0.8',
      'cache-control': 'no-cache',
      pragma: 'no-cache',
      'sec-ch-ua': '"Google Chrome";v="117", "Not;A=Brand";v="8", "Chromium";v="117"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"macOS"',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'none',
      'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36',
    },
  };

  const res = await axios.request(config);
  // const translatedText = res.data?.sentences?.[0]?.trans;
  const translatedText = res.data?.sentences?.map((sentence) => sentence.trans)?.join('');
  if (!translatedText) {
    return '';
  }
  // Replace back the unique keys with the original patterns
  const translatedTextWithPatterns = uniqueKeys.reduce((acc, key, index) => {
    return acc.replace(key, patterns[index]);
  }, translatedText);

  // Replace all %20 with space
  return translatedTextWithPatterns.replace(/%20/g, ' ');
};

const migrateTranslationToYml = async () => {
  for (let j = 0; j < LANG_LIST.length; j++) {
    const lang = LANG_LIST[j];
    for (let i = 0; i < LIST_LANG_FOLDERS.length; i++) {
      const folder = LIST_LANG_FOLDERS[i];

      const targetLangPath = `${folder}/${lang}.yml`;
      const defaultLangPath = `${folder}/${lang}.json`;
      if (!fs.existsSync(defaultLangPath)) {
        continue;
      }

      // Ensure the target lang file exists
      if (!fs.existsSync(targetLangPath)) {
        console.log(`Missing ${targetLangPath}, creating new file...`);
        fs.writeFileSync(targetLangPath, '');
      }

      // const targetLangObj = require(targetLangPath);
      const defaultLangObj = require(defaultLangPath);
      const yamlDump = yaml.dump(defaultLangObj, { noRefs: true });
      fs.writeFileSync(targetLangPath, yamlDump);
    }
  }
};

generateTranslation()
  .then(() => {
    console.log('Done generating translation');
  })
  .catch((err) => {
    console.error(err);
  });

// migrateTranslationToYml().then(() => {
//     console.log('Done migrating translation')
// }).catch((err) => {
//     console.error('Error migrating translation', err)
// })
