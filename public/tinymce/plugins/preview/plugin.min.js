!(function () {
  'use strict';
  var e = tinymce.util.Tools.resolve('tinymce.PluginManager');
  const t = (e) => undefined === e;
  const r = (e) => () => e,
    n = r(!1);
  class s {
    constructor(e, t) {
      (this.tag = e), (this.value = t);
    }
    static some(e) {
      return new s(!0, e);
    }
    static none() {
      return s.singletonNone;
    }
    fold(e, t) {
      return this.tag ? t(this.value) : e();
    }
    isSome() {
      return this.tag;
    }
    isNone() {
      return !this.tag;
    }
    map(e) {
      return this.tag ? s.some(e(this.value)) : s.none();
    }
    bind(e) {
      return this.tag ? e(this.value) : s.none();
    }
    exists(e) {
      return this.tag && e(this.value);
    }
    forall(e) {
      return !this.tag || e(this.value);
    }
    filter(e) {
      return !this.tag || e(this.value) ? this : s.none();
    }
    getOr(e) {
      return this.tag ? this.value : e;
    }
    or(e) {
      return this.tag ? this : e;
    }
    getOrThunk(e) {
      return this.tag ? this.value : e();
    }
    orThunk(e) {
      return this.tag ? this : e();
    }
    getOrDie(e) {
      if (this.tag) return this.value;
      throw new Error(null != e ? e : 'Called getOrDie on None');
    }
    static from(e) {
      return null == e ? s.none() : s.some(e);
    }
    getOrNull() {
      return this.tag ? this.value : null;
    }
    getOrUndefined() {
      return this.value;
    }
    each(e) {
      this.tag && e(this.value);
    }
    toArray() {
      return this.tag ? [this.value] : [];
    }
    toString() {
      return this.tag ? `some(${this.value})` : 'none()';
    }
  }
  s.singletonNone = new s(!1);
  const i = (e, t) =>
      ((e, t, r) => {
        for (let n = 0, i = e.length; n < i; n++) {
          const i = e[n];
          if (t(i, n)) return s.some(i);
          if (r(i, n)) break;
        }
        return s.none();
      })(e, t, n),
    o = (e, r, n = 0, s) => {
      const i = e.indexOf(r, n);
      return -1 !== i && (!!t(s) || i + r.length <= s);
    },
    a = () => c(0, 0),
    c = (e, t) => ({ major: e, minor: t }),
    u = {
      nu: c,
      detect: (e, t) => {
        const r = String(t).toLowerCase();
        return 0 === e.length
          ? a()
          : ((e, t) => {
              const r = ((e, t) => {
                for (let r = 0; r < e.length; r++) {
                  const n = e[r];
                  if (n.test(t)) return n;
                }
              })(e, t);
              if (!r) return { major: 0, minor: 0 };
              const n = (e) => Number(t.replace(r, '$' + e));
              return c(n(1), n(2));
            })(e, r);
      },
      unknown: a
    },
    l = (e, t) => {
      const r = String(t).toLowerCase();
      return i(e, (e) => e.search(r));
    },
    d = /.*?version\/\ ?([0-9]+)\.([0-9]+).*/,
    h = (e) => (t) => o(t, e),
    m = [
      {
        name: 'Edge',
        versionRegexes: [/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],
        search: (e) => o(e, 'edge/') && o(e, 'chrome') && o(e, 'safari') && o(e, 'applewebkit')
      },
      {
        name: 'Chromium',
        brand: 'Chromium',
        versionRegexes: [/.*?chrome\/([0-9]+)\.([0-9]+).*/, d],
        search: (e) => o(e, 'chrome') && !o(e, 'chromeframe')
      },
      {
        name: 'IE',
        versionRegexes: [/.*?msie\ ?([0-9]+)\.([0-9]+).*/, /.*?rv:([0-9]+)\.([0-9]+).*/],
        search: (e) => o(e, 'msie') || o(e, 'trident')
      },
      { name: 'Opera', versionRegexes: [d, /.*?opera\/([0-9]+)\.([0-9]+).*/], search: h('opera') },
      {
        name: 'Firefox',
        versionRegexes: [/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],
        search: h('firefox')
      },
      {
        name: 'Safari',
        versionRegexes: [d, /.*?cpu os ([0-9]+)_([0-9]+).*/],
        search: (e) => (o(e, 'safari') || o(e, 'mobile/')) && o(e, 'applewebkit')
      }
    ],
    v = [
      {
        name: 'Windows',
        search: h('win'),
        versionRegexes: [/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]
      },
      {
        name: 'iOS',
        search: (e) => o(e, 'iphone') || o(e, 'ipad'),
        versionRegexes: [
          /.*?version\/\ ?([0-9]+)\.([0-9]+).*/,
          /.*cpu os ([0-9]+)_([0-9]+).*/,
          /.*cpu iphone os ([0-9]+)_([0-9]+).*/
        ]
      },
      {
        name: 'Android',
        search: h('android'),
        versionRegexes: [/.*?android\ ?([0-9]+)\.([0-9]+).*/]
      },
      {
        name: 'macOS',
        search: h('mac os x'),
        versionRegexes: [/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]
      },
      { name: 'Linux', search: h('linux'), versionRegexes: [] },
      { name: 'Solaris', search: h('sunos'), versionRegexes: [] },
      { name: 'FreeBSD', search: h('freebsd'), versionRegexes: [] },
      { name: 'ChromeOS', search: h('cros'), versionRegexes: [/.*?chrome\/([0-9]+)\.([0-9]+).*/] }
    ],
    g = { browsers: r(m), oses: r(v) },
    p = 'Edge',
    f = 'Chromium',
    w = 'Opera',
    x = 'Firefox',
    S = 'Safari',
    y = (e) => {
      const t = e.current,
        r = e.version,
        n = (e) => () => t === e;
      return {
        current: t,
        version: r,
        isEdge: n(p),
        isChromium: n(f),
        isIE: n('IE'),
        isOpera: n(w),
        isFirefox: n(x),
        isSafari: n(S)
      };
    },
    b = () => y({ current: void 0, version: u.unknown() }),
    O = y,
    R = (r(p), r(f), r('IE'), r(w), r(x), r(S), 'Windows'),
    C = 'Android',
    A = 'Linux',
    k = 'macOS',
    D = 'Solaris',
    E = 'FreeBSD',
    I = 'ChromeOS',
    P = (e) => {
      const t = e.current,
        r = e.version,
        n = (e) => () => t === e;
      return {
        current: t,
        version: r,
        isWindows: n(R),
        isiOS: n('iOS'),
        isAndroid: n(C),
        isMacOS: n(k),
        isLinux: n(A),
        isSolaris: n(D),
        isFreeBSD: n(E),
        isChromeOS: n(I)
      };
    },
    T = () => P({ current: void 0, version: u.unknown() }),
    _ = P,
    B =
      (r(R),
      r('iOS'),
      r(C),
      r(A),
      r(k),
      r(D),
      r(E),
      r(I),
      (e, t, n) => {
        const o = g.browsers(),
          a = g.oses(),
          c = t
            .bind((e) =>
              ((e, t) =>
                ((e, t) => {
                  for (let r = 0; r < e.length; r++) {
                    const n = t(e[r]);
                    if (n.isSome()) return n;
                  }
                  return s.none();
                })(t.brands, (t) => {
                  const r = t.brand.toLowerCase();
                  return i(e, (e) => {
                    var t;
                    return (
                      r === (null === (t = e.brand) || void 0 === t ? void 0 : t.toLowerCase())
                    );
                  }).map((e) => ({ current: e.name, version: u.nu(parseInt(t.version, 10), 0) }));
                }))(o, e)
            )
            .orThunk(() =>
              ((e, t) =>
                l(e, t).map((e) => {
                  const r = u.detect(e.versionRegexes, t);
                  return { current: e.name, version: r };
                }))(o, e)
            )
            .fold(b, O),
          d = ((e, t) =>
            l(e, t).map((e) => {
              const r = u.detect(e.versionRegexes, t);
              return { current: e.name, version: r };
            }))(a, e).fold(T, _),
          h = ((e, t, n, s) => {
            const i = e.isiOS() && !0 === /ipad/i.test(n),
              o = e.isiOS() && !i,
              a = e.isiOS() || e.isAndroid(),
              c = a || s('(pointer:coarse)'),
              u = i || (!o && a && s('(min-device-width:768px)')),
              l = o || (a && !u),
              d = t.isSafari() && e.isiOS() && !1 === /safari/i.test(n),
              h = !l && !u && !d;
            return {
              isiPad: r(i),
              isiPhone: r(o),
              isTablet: r(u),
              isPhone: r(l),
              isTouch: r(c),
              isAndroid: e.isAndroid,
              isiOS: e.isiOS,
              isWebView: r(d),
              isDesktop: r(h)
            };
          })(d, c, e, n);
        return { browser: c, os: d, deviceType: h };
      }),
    L = (e) => window.matchMedia(e).matches;
  let N = ((e) => {
    let t,
      r = !1;
    return (...n) => (r || ((r = !0), (t = e.apply(null, n))), t);
  })(() => B(window.navigator.userAgent, s.from(window.navigator.userAgentData), L));
  const F = () => N();
  var M = tinymce.util.Tools.resolve('tinymce.util.Tools');
  const $ = (e) => (t) => t.options.get(e),
    W = $('content_style'),
    U = $('content_css_cors'),
    K = $('body_class'),
    j = $('body_id'),
    V = (e) => {
      const t = ((e) => {
        var t;
        let r = '';
        const n = e.dom.encode,
          s = null !== (t = W(e)) && void 0 !== t ? t : '';
        r += `<base href="${n(e.documentBaseURI.getURI())}">`;
        const i = U(e) ? ' crossorigin="anonymous"' : '';
        M.each(e.contentCSS, (t) => {
          r +=
            '<link type="text/css" rel="stylesheet" href="' +
            n(e.documentBaseURI.toAbsolute(t)) +
            '"' +
            i +
            '>';
        }),
          s && (r += '<style type="text/css">' + s + '</style>');
        const o = j(e),
          a = K(e),
          c = e.getBody().dir,
          u = c ? ' dir="' + n(c) + '"' : '';
        return (
          '<!DOCTYPE html><html><head>' +
          r +
          '</head><body id="' +
          n(o) +
          '" class="mce-content-body ' +
          n(a) +
          '"' +
          u +
          '>' +
          e.getContent() +
          (() => {
            const e = F().os.isMacOS() || F().os.isiOS();
            return `<script>(${((e) => {
              document.addEventListener(
                'click',
                (t) => {
                  for (let r = t.target; r; r = r.parentNode)
                    if ('A' === r.nodeName) {
                      const n = r.getAttribute('href');
                      if (n && n.startsWith('#')) {
                        t.preventDefault();
                        const e = document.getElementById(n.substring(1));
                        return void (e && e.scrollIntoView({ behavior: 'smooth' }));
                      }
                      (e ? t.metaKey : t.ctrlKey && !t.altKey) || t.preventDefault();
                    }
                },
                !1
              );
            }).toString()})(${e})<\/script>`;
          })() +
          '</body></html>'
        );
      })(e);
      e.windowManager
        .open({
          title: 'Preview',
          size: 'large',
          body: {
            type: 'panel',
            items: [{ name: 'preview', type: 'iframe', sandboxed: !0, transparent: !1 }]
          },
          buttons: [{ type: 'cancel', name: 'close', text: 'Close', primary: !0 }],
          initialData: { preview: t }
        })
        .focus('close');
    };
  e.add('preview', (e) => {
    ((e) => {
      e.addCommand('mcePreview', () => {
        V(e);
      });
    })(e),
      ((e) => {
        const t = () => e.execCommand('mcePreview');
        e.ui.registry.addButton('preview', {
          icon: 'preview',
          tooltip: 'Preview',
          onAction: t,
          context: 'any'
        }),
          e.ui.registry.addMenuItem('preview', {
            icon: 'preview',
            text: 'Preview',
            onAction: t,
            context: 'any'
          });
      })(e);
  });
})();
