!(function () {
  'use strict';
  const t = (t) => (e) =>
      ((t) => {
        const e = typeof t;
        return null === t
          ? 'null'
          : 'object' === e && Array.isArray(t)
            ? 'array'
            : 'object' === e &&
                ((n = o = t),
                (r = String).prototype.isPrototypeOf(n) ||
                  (null === (s = o.constructor) || void 0 === s ? void 0 : s.name) === r.name)
              ? 'string'
              : e;
        var n, o, r, s;
      })(e) === t,
    e = (t) => (e) => typeof e === t,
    n = t('string'),
    o = t('object'),
    r = (t) => null === t;
  const s = e('boolean'),
    a = e('function'),
    l = e('number');
  class i {
    constructor(t, e) {
      (this.tag = t), (this.value = e);
    }
    static some(t) {
      return new i(!0, t);
    }
    static none() {
      return i.singletonNone;
    }
    fold(t, e) {
      return this.tag ? e(this.value) : t();
    }
    isSome() {
      return this.tag;
    }
    isNone() {
      return !this.tag;
    }
    map(t) {
      return this.tag ? i.some(t(this.value)) : i.none();
    }
    bind(t) {
      return this.tag ? t(this.value) : i.none();
    }
    exists(t) {
      return this.tag && t(this.value);
    }
    forall(t) {
      return !this.tag || t(this.value);
    }
    filter(t) {
      return !this.tag || t(this.value) ? this : i.none();
    }
    getOr(t) {
      return this.tag ? this.value : t;
    }
    or(t) {
      return this.tag ? this : t;
    }
    getOrThunk(t) {
      return this.tag ? this.value : t();
    }
    orThunk(t) {
      return this.tag ? this : t();
    }
    getOrDie(t) {
      if (this.tag) return this.value;
      throw new Error(null != t ? t : 'Called getOrDie on None');
    }
    static from(t) {
      return null == t ? i.none() : i.some(t);
    }
    getOrNull() {
      return this.tag ? this.value : null;
    }
    getOrUndefined() {
      return this.value;
    }
    each(t) {
      this.tag && t(this.value);
    }
    toArray() {
      return this.tag ? [this.value] : [];
    }
    toString() {
      return this.tag ? `some(${this.value})` : 'none()';
    }
  }
  (i.singletonNone = new i(!1)), Array.prototype.slice;
  const u = (t, e) => {
    for (let n = 0, o = t.length; n < o; n++) e(t[n], n);
  };
  a(Array.from) && Array.from;
  const c = Object.keys,
    d = (t, e) => {
      const n = c(t);
      for (let o = 0, r = n.length; o < r; o++) {
        const r = n[o];
        e(t[r], r);
      }
    },
    h = 'undefined' != typeof window ? window : Function('return this;')(),
    m = (t, e) =>
      ((t, e) => {
        let n = null != e ? e : h;
        for (let e = 0; e < t.length && null != n; ++e) n = n[t[e]];
        return n;
      })(t.split('.'), e);
  var g = tinymce.util.Tools.resolve('tinymce.PluginManager');
  const v = (t) => {
      if (null == t) throw new Error('Node cannot be null or undefined');
      return { dom: t };
    },
    f = v,
    p = Object.getPrototypeOf,
    y = (t) => {
      const e = m('ownerDocument.defaultView', t);
      return (
        o(t) &&
        (((t) =>
          ((t, e) => {
            const n = ((t, e) => m(t, e))(t, e);
            if (null == n) throw new Error(t + ' not available on this browser');
            return n;
          })('HTMLElement', t))(e).prototype.isPrototypeOf(t) ||
          /^HTML\w*Element$/.test(p(t).constructor.name))
      );
    },
    b = (t) => t.dom.nodeValue,
    w = (t) => (e) => ((t) => t.dom.nodeType)(e) === t,
    A = (t) => k(t) && y(t.dom),
    k = w(1),
    N = w(3),
    T = (t, e, o) => {
      ((t, e, o) => {
        if (!(n(o) || s(o) || l(o)))
          throw (
            (console.error(
              'Invalid call to Attribute.set. Key ',
              e,
              ':: Value ',
              o,
              ':: Element ',
              t
            ),
            new Error('Attribute value was not simple'))
          );
        t.setAttribute(e, o + '');
      })(t.dom, e, o);
    },
    C = (t, e) => {
      t.dom.removeAttribute(e);
    },
    E = (t, e) => {
      const n = ((t, e) => {
        const n = t.dom.getAttribute(e);
        return null === n ? void 0 : n;
      })(t, e);
      return void 0 === n || '' === n ? [] : n.split(' ');
    },
    O = (t) => void 0 !== t.dom.classList,
    L = (t) => t.dom.contentEditable,
    V = { '\xa0': 'nbsp', '\xad': 'shy' },
    j = (t, e) => {
      let n = '';
      return (
        d(t, (t, e) => {
          n += e;
        }),
        new RegExp('[' + n + ']', e ? 'g' : '')
      );
    },
    B = j(V),
    S = j(V, !0),
    x = ((t) => {
      let e = '';
      return (
        d(t, (t) => {
          e && (e += ','), (e += 'span.mce-' + t);
        }),
        e
      );
    })(V),
    M = 'mce-nbsp',
    P = (t) => '<span data-mce-bogus="1" class="mce-' + V[t] + '">' + t + '</span>',
    _ = (t) => 'span' === t.nodeName.toLowerCase() && t.classList.contains('mce-nbsp-wrap'),
    D = (t) => {
      const e = b(t);
      return N(t) && n(e) && B.test(e);
    },
    H = (t, e, n) => {
      let o = [];
      const r = ((t, e) => {
        const n = t.length,
          o = new Array(n);
        for (let r = 0; r < n; r++) {
          const n = t[r];
          o[r] = e(n, r);
        }
        return o;
      })(t.dom.childNodes, f);
      return (
        u(r, (t) => {
          var r;
          n &&
            (_((r = t).dom) || !((t) => A(t) && 'false' === L(t))(r)) &&
            e(t) &&
            (o = o.concat([t])),
            (o = o.concat(
              H(
                t,
                e,
                ((t, e) => {
                  if (A(t) && !_(t.dom)) {
                    const e = L(t);
                    if ('true' === e) return !0;
                    if ('false' === e) return !1;
                  }
                  return e;
                })(t, n)
              )
            ));
        }),
        o
      );
    },
    I = (t, e) => {
      const n = t.dom,
        o = H(f(e), D, t.dom.isEditable(e));
      u(o, (e) => {
        var o;
        const r = e.dom.parentNode;
        if (_(r))
          (s = f(r)),
            (a = M),
            O(s)
              ? s.dom.classList.add(a)
              : ((t, e) => {
                  ((t, e, n) => {
                    const o = E(t, e).concat([n]);
                    T(t, e, o.join(' '));
                  })(t, 'class', e);
                })(s, a);
        else {
          const r = n.encode(null !== (o = b(e)) && void 0 !== o ? o : '').replace(S, P),
            s = n.create('div', {}, r);
          let a;
          for (; (a = s.lastChild); ) n.insertAfter(a, e.dom);
          t.dom.remove(e.dom);
        }
        var s, a;
      });
    },
    $ = (t, e) => {
      const n = t.dom.select(x, e);
      u(n, (e) => {
        var n, o;
        _(e)
          ? ((n = f(e)),
            (o = M),
            O(n)
              ? n.dom.classList.remove(o)
              : ((t, e) => {
                  ((t, e, n) => {
                    const o = ((t) => {
                      const e = [];
                      for (let o = 0, r = t.length; o < r; o++) {
                        const r = t[o];
                        r !== n && e.push(r);
                      }
                      return e;
                    })(E(t, e));
                    o.length > 0 ? T(t, e, o.join(' ')) : C(t, e);
                  })(t, 'class', e);
                })(n, o),
            ((t) => {
              const e = O(t) ? t.dom.classList : ((t) => E(t, 'class'))(t);
              0 === e.length && C(t, 'class');
            })(n))
          : t.dom.remove(e, !0);
      });
    },
    F = (t) => {
      const e = t.getBody(),
        n = t.selection.getBookmark();
      let o = ((t, e) => {
        for (; t.parentNode; ) {
          if (t.parentNode === e) return e;
          t = t.parentNode;
        }
      })(t.selection.getNode(), e);
      (o = void 0 !== o ? o : e), $(t, o), I(t, o), t.selection.moveToBookmark(n);
    },
    K = (t, e) => {
      ((t, e) => {
        t.dispatch('VisualChars', { state: e });
      })(t, e.get());
      const n = t.getBody();
      !0 === e.get() ? I(t, n) : $(t, n);
    },
    R = (t) => t.options.get('visualchars_default_state');
  const U = (t, e) => {
      const n = ((t) => {
        let e = null;
        return {
          cancel: () => {
            r(e) || (clearTimeout(e), (e = null));
          },
          throttle: (...n) => {
            r(e) &&
              (e = setTimeout(() => {
                (e = null), t.apply(null, n);
              }, 300));
          }
        };
      })(() => {
        F(t);
      });
      t.on('keydown', (o) => {
        !0 === e.get() && (13 === o.keyCode ? F(t) : n.throttle());
      }),
        t.on('remove', n.cancel);
    },
    q = (t, e) => (n) => {
      n.setActive(e.get());
      const o = (t) => n.setActive(t.state);
      return t.on('VisualChars', o), () => t.off('VisualChars', o);
    };
  g.add('visualchars', (t) => {
    ((t) => {
      (0, t.options.register)('visualchars_default_state', { processor: 'boolean', default: !1 });
    })(t);
    const e = ((t) => {
      let e = t;
      return {
        get: () => e,
        set: (t) => {
          e = t;
        }
      };
    })(R(t));
    return (
      ((t, e) => {
        t.addCommand('mceVisualChars', () => {
          ((t, e) => {
            e.set(!e.get());
            const n = t.selection.getBookmark();
            K(t, e), t.selection.moveToBookmark(n);
          })(t, e);
        });
      })(t, e),
      ((t, e) => {
        const n = () => t.execCommand('mceVisualChars');
        t.ui.registry.addToggleButton('visualchars', {
          tooltip: 'Show invisible characters',
          icon: 'visualchars',
          onAction: n,
          onSetup: q(t, e),
          context: 'any'
        }),
          t.ui.registry.addToggleMenuItem('visualchars', {
            text: 'Show invisible characters',
            icon: 'visualchars',
            onAction: n,
            onSetup: q(t, e),
            context: 'any'
          });
      })(t, e),
      U(t, e),
      ((t, e) => {
        t.on('init', () => {
          K(t, e);
        });
      })(t, e),
      ((t) => ({ isEnabled: () => t.get() }))(e)
    );
  });
})();
