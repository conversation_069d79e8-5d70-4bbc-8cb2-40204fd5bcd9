!(function () {
  'use strict';
  var e = tinymce.util.Tools.resolve('tinymce.PluginManager');
  const t = (e) => (t) => typeof t === e,
    o = (e) =>
      'string' ===
      ((e) => {
        const t = typeof e;
        return null === e
          ? 'null'
          : 'object' === t && Array.isArray(e)
            ? 'array'
            : 'object' === t &&
                ((o = n = e),
                (r = String).prototype.isPrototypeOf(o) ||
                  (null === (s = n.constructor) || void 0 === s ? void 0 : s.name) === r.name)
              ? 'string'
              : t;
        var o, n, r, s;
      })(e),
    n = t('boolean'),
    r = (e) => null == e,
    s = (e) => !r(e),
    a = t('function'),
    i = t('number'),
    l = (e) => () => e,
    d = (e, t) => e === t,
    c = l(!1);
  class m {
    constructor(e, t) {
      (this.tag = e), (this.value = t);
    }
    static some(e) {
      return new m(!0, e);
    }
    static none() {
      return m.singletonNone;
    }
    fold(e, t) {
      return this.tag ? t(this.value) : e();
    }
    isSome() {
      return this.tag;
    }
    isNone() {
      return !this.tag;
    }
    map(e) {
      return this.tag ? m.some(e(this.value)) : m.none();
    }
    bind(e) {
      return this.tag ? e(this.value) : m.none();
    }
    exists(e) {
      return this.tag && e(this.value);
    }
    forall(e) {
      return !this.tag || e(this.value);
    }
    filter(e) {
      return !this.tag || e(this.value) ? this : m.none();
    }
    getOr(e) {
      return this.tag ? this.value : e;
    }
    or(e) {
      return this.tag ? this : e;
    }
    getOrThunk(e) {
      return this.tag ? this.value : e();
    }
    orThunk(e) {
      return this.tag ? this : e();
    }
    getOrDie(e) {
      if (this.tag) return this.value;
      throw new Error(null != e ? e : 'Called getOrDie on None');
    }
    static from(e) {
      return s(e) ? m.some(e) : m.none();
    }
    getOrNull() {
      return this.tag ? this.value : null;
    }
    getOrUndefined() {
      return this.value;
    }
    each(e) {
      this.tag && e(this.value);
    }
    toArray() {
      return this.tag ? [this.value] : [];
    }
    toString() {
      return this.tag ? `some(${this.value})` : 'none()';
    }
  }
  m.singletonNone = new m(!1);
  const u = Array.prototype.indexOf,
    g = (e, t) => {
      return (o = e), (n = t), u.call(o, n) > -1;
      var o, n;
    },
    p = (e, t) => {
      const o = e.length,
        n = new Array(o);
      for (let r = 0; r < o; r++) {
        const o = e[r];
        n[r] = t(o, r);
      }
      return n;
    },
    h = (e, t) => {
      for (let o = 0, n = e.length; o < n; o++) t(e[o], o);
    },
    f = Object.keys;
  let y = 0;
  const v = ((w = /^\s+|\s+$/g), (e) => e.replace(w, ''));
  var w;
  const b = (e, t) => ({ element: e, offset: t }),
    N = (e) => {
      if (null == e) throw new Error('Node cannot be null or undefined');
      return { dom: e };
    },
    T = {
      fromHtml: (e, t) => {
        const o = (t || document).createElement('div');
        if (((o.innerHTML = e), !o.hasChildNodes() || o.childNodes.length > 1)) {
          const t = 'HTML does not have a single root node';
          throw (console.error(t, e), new Error(t));
        }
        return N(o.childNodes[0]);
      },
      fromTag: (e, t) => {
        const o = (t || document).createElement(e);
        return N(o);
      },
      fromText: (e, t) => {
        const o = (t || document).createTextNode(e);
        return N(o);
      },
      fromDom: N,
      fromPoint: (e, t, o) => m.from(e.dom.elementFromPoint(t, o)).map(N)
    },
    A = (e, t) => {
      const o = e.dom;
      if (1 !== o.nodeType) return !1;
      {
        const e = o;
        if (void 0 !== e.matches) return e.matches(t);
        if (void 0 !== e.msMatchesSelector) return e.msMatchesSelector(t);
        if (void 0 !== e.webkitMatchesSelector) return e.webkitMatchesSelector(t);
        if (void 0 !== e.mozMatchesSelector) return e.mozMatchesSelector(t);
        throw new Error('Browser lacks native selectors');
      }
    },
    C = (e) =>
      (1 !== e.nodeType && 9 !== e.nodeType && 11 !== e.nodeType) || 0 === e.childElementCount,
    S = A,
    x = (e) => e.dom.nodeName.toLowerCase(),
    D = (e) => e.dom.nodeType,
    E = (e) => (t) => D(t) === e,
    O = E(1),
    M = E(3),
    P = E(9),
    R = E(11),
    k = (e) => m.from(e.dom.parentNode).map(T.fromDom),
    B = (e) => m.from(e.dom.nextSibling).map(T.fromDom),
    L = (e) => p(e.dom.childNodes, T.fromDom),
    $ = (e) => T.fromDom(e.dom.host),
    V = (e, t) => {
      k(e).each((o) => {
        o.dom.insertBefore(t.dom, e.dom);
      });
    },
    I = (e, t) => {
      B(e).fold(
        () => {
          k(e).each((e) => {
            j(e, t);
          });
        },
        (e) => {
          V(e, t);
        }
      );
    },
    j = (e, t) => {
      e.dom.appendChild(t.dom);
    },
    q = (e, t) => {
      h(t, (o, n) => {
        const r = 0 === n ? e : t[n - 1];
        I(r, o);
      });
    },
    H = (e, t, r) => {
      if (!(o(r) || n(r) || i(r)))
        throw (
          (console.error(
            'Invalid call to Attribute.set. Key ',
            t,
            ':: Value ',
            r,
            ':: Element ',
            e
          ),
          new Error('Attribute value was not simple'))
        );
      e.setAttribute(t, r + '');
    },
    z = (e, t) => {
      const o = e.dom.getAttribute(t);
      return null === o ? void 0 : o;
    },
    F = (e, t) => m.from(z(e, t)),
    K = (e, t) => {
      e.dom.removeAttribute(t);
    },
    U = (e) => {
      const t = e.dom;
      null !== t.parentNode && t.parentNode.removeChild(t);
    },
    Y = (e) => void 0 !== e.style && a(e.style.getPropertyValue),
    _ = (e) => {
      const t = M(e) ? e.dom.parentNode : e.dom;
      if (null == t || null === t.ownerDocument) return !1;
      const o = t.ownerDocument;
      return ((e) => {
        const t = ((e) => T.fromDom(e.dom.getRootNode()))(e);
        return R((o = t)) && s(o.dom.host) ? m.some(t) : m.none();
        var o;
      })(T.fromDom(t)).fold(() => o.body.contains(t), ((n = _), (r = $), (e) => n(r(e))));
      var n, r;
    },
    G = (e, t) => (Y(e) ? e.style.getPropertyValue(t) : ''),
    J = ((e) => {
      const t = (t) => (e(t) ? m.from(t.dom.nodeValue) : m.none());
      return {
        get: (o) => {
          if (!e(o)) throw new Error('Can only get text value of a text node');
          return t(o).getOr('');
        },
        getOption: t,
        set: (t, o) => {
          if (!e(t)) throw new Error('Can only set raw text value of a text node');
          t.dom.nodeValue = o;
        }
      };
    })(M),
    Q = (e, t, o) => {
      let n = e.dom;
      const r = a(o) ? o : c;
      for (; n.parentNode; ) {
        n = n.parentNode;
        const e = T.fromDom(n);
        if (t(e)) return m.some(e);
        if (r(e)) break;
      }
      return m.none();
    },
    W = (e, t, o) => Q(e, (e) => A(e, t), o),
    X = (e, t) =>
      ((e, t) => {
        const o = void 0 === t ? document : t.dom;
        return C(o) ? m.none() : m.from(o.querySelector(e)).map(T.fromDom);
      })(t, e),
    Z = (e, t) => {
      let o = [];
      return (
        h(L(e), (e) => {
          t(e) && (o = o.concat([e])), (o = o.concat(Z(e, t)));
        }),
        o
      );
    };
  var ee = [
    'body',
    'p',
    'div',
    'article',
    'aside',
    'figcaption',
    'figure',
    'footer',
    'header',
    'nav',
    'section',
    'ol',
    'ul',
    'li',
    'table',
    'thead',
    'tbody',
    'tfoot',
    'caption',
    'tr',
    'td',
    'th',
    'h1',
    'h2',
    'h3',
    'h4',
    'h5',
    'h6',
    'blockquote',
    'pre',
    'address'
  ];
  const te = (e, t, o) =>
      (e.property().isText(t) && 0 === e.property().getText(t).trim().length) ||
      e.property().isComment(t)
        ? o(t).bind((t) => te(e, t, o).orThunk(() => m.some(t)))
        : m.none(),
    oe = (e, t) =>
      e.property().isText(t) ? e.property().getText(t).length : e.property().children(t).length,
    ne = (e, t) => {
      const o = te(e, t, e.query().prevSibling).getOr(t);
      if (e.property().isText(o)) return b(o, oe(e, o));
      const n = e.property().children(o);
      return n.length > 0 ? ne(e, n[n.length - 1]) : b(o, oe(e, o));
    },
    re = ne,
    se = {
      up: l({
        selector: W,
        closest: (e, t, o) =>
          ((e, t, o, n, r) =>
            ((e, t) => A(e, t))(o, n) ? m.some(o) : a(r) && r(o) ? m.none() : t(o, n, r))(
            0,
            W,
            e,
            t,
            o
          ),
        predicate: Q,
        all: (e, t) => {
          const o = a(t) ? t : c;
          let n = e.dom;
          const r = [];
          for (; null !== n.parentNode && void 0 !== n.parentNode; ) {
            const e = n.parentNode,
              t = T.fromDom(e);
            if ((r.push(t), !0 === o(t))) break;
            n = e;
          }
          return r;
        }
      }),
      down: l({
        selector: (e, t) =>
          ((e, t) => {
            const o = void 0 === t ? document : t.dom;
            return C(o) ? [] : p(o.querySelectorAll(e), T.fromDom);
          })(t, e),
        predicate: Z
      }),
      styles: l({
        get: (e, t) => {
          const o = e.dom,
            n = window.getComputedStyle(o).getPropertyValue(t);
          return '' !== n || _(e) ? n : G(o, t);
        },
        getRaw: (e, t) => {
          const o = e.dom,
            n = G(o, t);
          return m.from(n).filter((e) => e.length > 0);
        },
        set: (e, t, n) => {
          ((e, t, n) => {
            if (!o(n))
              throw (
                (console.error(
                  'Invalid call to CSS.set. Property ',
                  t,
                  ':: Value ',
                  n,
                  ':: Element ',
                  e
                ),
                new Error('CSS value must be a string: ' + n))
              );
            Y(e) && e.style.setProperty(t, n);
          })(e.dom, t, n);
        },
        remove: (e, t) => {
          ((e, t) => {
            Y(e) && e.style.removeProperty(t);
          })(e.dom, t),
            ((e, t, o = d) => e.exists((e) => o(e, t)))(F(e, 'style').map(v), '') && K(e, 'style');
        }
      }),
      attrs: l({
        get: z,
        set: (e, t, o) => {
          H(e.dom, t, o);
        },
        remove: K,
        copyTo: (e, t) => {
          const o =
            ((n = e.dom.attributes),
            (r = (e, t) => ((e[t.name] = t.value), e)),
            (s = {}),
            h(n, (e, t) => {
              s = r(s, e);
            }),
            s);
          var n, r, s;
          ((e, t) => {
            const o = e.dom;
            ((e, t) => {
              const o = f(e);
              for (let n = 0, r = o.length; n < r; n++) {
                const r = o[n];
                t(e[r], r);
              }
            })(t, (e, t) => {
              H(o, t, e);
            });
          })(t, o);
        }
      }),
      insert: l({
        before: V,
        after: I,
        afterAll: q,
        append: j,
        appendAll: (e, t) => {
          h(t, (t) => {
            j(e, t);
          });
        },
        prepend: (e, t) => {
          ((e) =>
            ((e) => {
              const t = e.dom.childNodes;
              return m.from(t[0]).map(T.fromDom);
            })(e))(e).fold(
            () => {
              j(e, t);
            },
            (o) => {
              e.dom.insertBefore(t.dom, o.dom);
            }
          );
        },
        wrap: (e, t) => {
          V(e, t), j(t, e);
        }
      }),
      remove: l({
        unwrap: (e) => {
          const t = L(e);
          t.length > 0 && q(e, t), U(e);
        },
        remove: U
      }),
      create: l({ nu: T.fromTag, clone: (e) => T.fromDom(e.dom.cloneNode(!1)), text: T.fromText }),
      query: l({
        comparePosition: (e, t) => e.dom.compareDocumentPosition(t.dom),
        prevSibling: (e) => m.from(e.dom.previousSibling).map(T.fromDom),
        nextSibling: B
      }),
      property: l({
        children: L,
        name: x,
        parent: k,
        document: (e) => {
          return ((t = e), P(t) ? t : T.fromDom(t.dom.ownerDocument)).dom;
          var t;
        },
        isText: M,
        isComment: (e) => 8 === D(e) || '#comment' === x(e),
        isElement: O,
        isSpecial: (e) => {
          const t = x(e);
          return g(
            [
              'script',
              'noscript',
              'iframe',
              'noframes',
              'noembed',
              'title',
              'style',
              'textarea',
              'xmp'
            ],
            t
          );
        },
        getLanguage: (e) => (O(e) ? F(e, 'lang') : m.none()),
        getText: (e) => J.get(e),
        setText: (e, t) => J.set(e, t),
        isBoundary: (e) => !!O(e) && ('body' === x(e) || g(ee, x(e))),
        isEmptyTag: (e) => !!O(e) && g(['br', 'img', 'hr', 'input'], x(e)),
        isNonEditable: (e) => O(e) && 'false' === z(e, 'contenteditable')
      }),
      eq: (e, t) => e.dom === t.dom,
      is: S
    },
    ae = 'details',
    ie = 'mce-accordion',
    le = 'mce-accordion-summary',
    de = 'mce-accordion-body',
    ce = 'div';
  var me = tinymce.util.Tools.resolve('tinymce.util.Tools');
  const ue = (e) => 'SUMMARY' === (null == e ? void 0 : e.nodeName),
    ge = (e) => 'DETAILS' === (null == e ? void 0 : e.nodeName),
    pe = (e) => e.hasAttribute('open'),
    he = (e) => {
      const t = e.selection.getNode();
      return ue(t) || Boolean(e.dom.getParent(t, ue));
    },
    fe = (e) => !he(e) && e.dom.isEditable(e.selection.getNode()) && !e.mode.isReadOnly(),
    ye = (e) => m.from(e.dom.getParent(e.selection.getNode(), ge)),
    ve = (e) => ((e.innerHTML = '<br data-mce-bogus="1" />'), e),
    we = (e) => ve(e.dom.create('p')),
    be = (e) => (t) => {
      ((e, t) => {
        if (ue(null == t ? void 0 : t.lastChild)) {
          const o = we(e);
          t.appendChild(o), e.selection.setCursorLocation(o, 0);
        }
      })(e, t),
        ((e, t) => {
          if (!ue(null == t ? void 0 : t.firstChild)) {
            const o = ((e) => ve(e.dom.create('summary')))(e);
            t.prepend(o), e.selection.setCursorLocation(o, 0);
          }
        })(e, t);
    },
    Ne = (e) => {
      if (!fe(e)) return;
      const t = T.fromDom(e.getBody()),
        o = ((e) => {
          const t = new Date().getTime(),
            o = Math.floor(
              (window.crypto.getRandomValues(new Uint32Array(1))[0] / 4294967295) * 1e9
            );
          return y++, e + '_' + o + y + String(t);
        })('acc'),
        n = e.dom.encode(e.selection.getRng().toString() || e.translate('Accordion summary...')),
        r = e.dom.encode(e.translate('Accordion body...')),
        s = `<summary class="${le}">${n}</summary>`,
        a = `<${ce} class="${de}"><p>${r}</p></${ce}>`;
      e.undoManager.transact(() => {
        e.insertContent(
          [`<details data-mce-id="${o}" class="${ie}" open="open">`, s, a, '</details>'].join('')
        ),
          X(t, `[data-mce-id="${o}"]`).each((t) => {
            K(t, 'data-mce-id'),
              X(t, 'summary').each((t) => {
                const o = e.dom.createRng(),
                  n = re(se, t);
                o.setStart(n.element.dom, n.offset),
                  o.setEnd(n.element.dom, n.offset),
                  e.selection.setRng(o);
              });
          });
      });
    },
    Te = (e, t) => {
      const o = null != t ? t : !pe(e);
      return o ? e.setAttribute('open', 'open') : e.removeAttribute('open'), o;
    },
    Ae = (e) => {
      e.addCommand('InsertAccordion', () => Ne(e)),
        e.addCommand('ToggleAccordion', (t, o) =>
          ((e, t) => {
            ye(e).each((o) => {
              ((e, t, o) => {
                e.dispatch('ToggledAccordion', { element: t, state: o });
              })(e, o, Te(o, t));
            });
          })(e, o)
        ),
        e.addCommand('ToggleAllAccordions', (t, o) =>
          ((e, t) => {
            const o = Array.from(e.getBody().querySelectorAll('details'));
            0 !== o.length &&
              (h(o, (e) => Te(e, null != t ? t : !pe(e))),
              ((e, t, o) => {
                e.dispatch('ToggledAllAccordions', { elements: t, state: o });
              })(e, o, t));
          })(e, o)
        ),
        e.addCommand('RemoveAccordion', () =>
          ((e) => {
            e.mode.isReadOnly() ||
              ye(e).each((t) => {
                const { nextSibling: o } = t;
                o
                  ? (e.selection.select(o, !0), e.selection.collapse(!0))
                  : ((e, t) => {
                      const o = we(e);
                      t.insertAdjacentElement('afterend', o), e.selection.setCursorLocation(o, 0);
                    })(e, t),
                  t.remove();
              });
          })(e)
        );
    };
  var Ce = tinymce.util.Tools.resolve('tinymce.html.Node');
  const Se = (e) => {
      var t, o;
      return null !==
        (o = null === (t = e.attr('class')) || void 0 === t ? void 0 : t.split(' ')) && void 0 !== o
        ? o
        : [];
    },
    xe = (e, t) => {
      const o = new Set([...Se(e), ...t]),
        n = Array.from(o);
      n.length > 0 && e.attr('class', n.join(' '));
    },
    De = (e, t) => {
      const o = ((e) => {
        const o = [];
        for (let r = 0, s = e.length; r < s; r++) {
          const s = e[r];
          (n = s), !t.has(n) && o.push(s);
        }
        var n;
        return o;
      })(Se(e));
      e.attr('class', o.length > 0 ? o.join(' ') : null);
    },
    Ee = (e) => e.name === ae && g(Se(e), ie),
    Oe = (e) => {
      const t = e.children();
      let o, n;
      const s = [];
      for (let e = 0; e < t.length; e++) {
        const i = t[e];
        'summary' === i.name && r(o)
          ? (o = i)
          : (a = i).name === ce && g(Se(a), de) && r(n)
            ? (n = i)
            : s.push(i);
      }
      var a;
      return { summaryNode: o, wrapperNode: n, otherNodes: s };
    },
    Me = (e) => {
      const t = new Ce('br', 1);
      t.attr('data-mce-bogus', '1'), e.empty(), e.append(t);
    };
  var Pe = tinymce.util.Tools.resolve('tinymce.util.VK');
  const Re = (e) => {
    ((e) => {
      e.on('keydown', (t) => {
        ((!t.shiftKey && t.keyCode === Pe.ENTER && he(e)) ||
          ((e) => {
            const t = e.selection.getRng();
            return ge(t.startContainer) && t.collapsed && 0 === t.startOffset;
          })(e)) &&
          (t.preventDefault(), e.execCommand('ToggleAccordion'));
      });
    })(e),
      e.on('ExecCommand', (t) => {
        const o = t.command.toLowerCase();
        ('delete' !== o && 'forwarddelete' !== o) ||
          !((e) => ye(e).isSome())(e) ||
          ((e) => {
            me.each(me.grep(e.dom.select('details', e.getBody())), be(e));
          })(e);
      });
  };
  var ke = tinymce.util.Tools.resolve('tinymce.Env');
  const Be = (e) => (t) => {
    const o = () => t.setEnabled(fe(e));
    return e.on('NodeChange', o), () => e.off('NodeChange', o);
  };
  e.add('accordion', (e) => {
    ((e) => {
      const t = () => e.execCommand('InsertAccordion');
      e.ui.registry.addButton('accordion', {
        icon: 'accordion',
        tooltip: 'Insert accordion',
        onSetup: Be(e),
        onAction: t
      }),
        e.ui.registry.addMenuItem('accordion', {
          icon: 'accordion',
          text: 'Accordion',
          onSetup: Be(e),
          onAction: t
        }),
        e.ui.registry.addToggleButton('accordiontoggle', {
          icon: 'accordion-toggle',
          tooltip: 'Toggle accordion',
          onAction: () => e.execCommand('ToggleAccordion')
        }),
        e.ui.registry.addToggleButton('accordionremove', {
          icon: 'remove',
          tooltip: 'Delete accordion',
          onAction: () => e.execCommand('RemoveAccordion')
        }),
        e.ui.registry.addContextToolbar('accordion', {
          predicate: (t) =>
            e.dom.is(t, 'details') && e.getBody().contains(t) && e.dom.isEditable(t.parentNode),
          items: 'accordiontoggle accordionremove',
          scope: 'node',
          position: 'node'
        });
    })(e),
      Ae(e),
      Re(e),
      ((e) => {
        e.on('PreInit', () => {
          const { serializer: t, parser: o } = e;
          o.addNodeFilter(ae, (e) => {
            for (let t = 0; t < e.length; t++) {
              const o = e[t];
              if (Ee(o)) {
                const e = o,
                  { summaryNode: t, wrapperNode: n, otherNodes: a } = Oe(e),
                  i = s(t),
                  l = i ? t : new Ce('summary', 1);
                r(l.firstChild) && Me(l),
                  xe(l, [le]),
                  i || (s(e.firstChild) ? e.insert(l, e.firstChild, !0) : e.append(l));
                const d = s(n),
                  c = d ? n : new Ce(ce, 1);
                if ((c.attr('data-mce-bogus', '1'), xe(c, [de]), a.length > 0))
                  for (let e = 0; e < a.length; e++) {
                    const t = a[e];
                    c.append(t);
                  }
                if (r(c.firstChild)) {
                  const e = new Ce('p', 1);
                  Me(e), c.append(e);
                }
                d || e.append(c);
              }
            }
          }),
            t.addNodeFilter(ae, (e) => {
              const t = new Set([le]);
              for (let o = 0; o < e.length; o++) {
                const n = e[o];
                if (Ee(n)) {
                  const e = n,
                    { summaryNode: o, wrapperNode: r } = Oe(e);
                  s(o) && De(o, t), s(r) && r.unwrap();
                }
              }
            });
        });
      })(e),
      ((e) => {
        ke.browser.isSafari() &&
          e.on('click', (t) => {
            if (ue(t.target)) {
              const o = t.target,
                n = e.selection.getRng();
              n.collapsed &&
                n.startContainer === o.parentNode &&
                0 === n.startOffset &&
                e.selection.setCursorLocation(o, 0);
            }
          });
      })(e);
  });
})();
