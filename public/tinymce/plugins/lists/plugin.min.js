!(function () {
  'use strict';
  var e = tinymce.util.Tools.resolve('tinymce.PluginManager');
  const t = (e) => (t) =>
      ((e) => {
        const t = typeof e;
        return null === e
          ? 'null'
          : 'object' === t && Array.isArray(e)
            ? 'array'
            : 'object' === t &&
                ((n = o = e),
                (r = String).prototype.isPrototypeOf(n) ||
                  (null === (s = o.constructor) || void 0 === s ? void 0 : s.name) === r.name)
              ? 'string'
              : t;
        var n, o, r, s;
      })(t) === e,
    n = (e) => (t) => typeof t === e,
    o = t('string'),
    r = t('object'),
    s = t('array'),
    i = n('boolean'),
    l = (e) => !((e) => null == e)(e),
    a = n('function'),
    d = n('number'),
    c = () => {},
    m = (e) => () => e,
    u = (e, t) => e === t,
    p = (e) => (t) => !e(t),
    g = m(!1);
  class h {
    constructor(e, t) {
      (this.tag = e), (this.value = t);
    }
    static some(e) {
      return new h(!0, e);
    }
    static none() {
      return h.singletonNone;
    }
    fold(e, t) {
      return this.tag ? t(this.value) : e();
    }
    isSome() {
      return this.tag;
    }
    isNone() {
      return !this.tag;
    }
    map(e) {
      return this.tag ? h.some(e(this.value)) : h.none();
    }
    bind(e) {
      return this.tag ? e(this.value) : h.none();
    }
    exists(e) {
      return this.tag && e(this.value);
    }
    forall(e) {
      return !this.tag || e(this.value);
    }
    filter(e) {
      return !this.tag || e(this.value) ? this : h.none();
    }
    getOr(e) {
      return this.tag ? this.value : e;
    }
    or(e) {
      return this.tag ? this : e;
    }
    getOrThunk(e) {
      return this.tag ? this.value : e();
    }
    orThunk(e) {
      return this.tag ? this : e();
    }
    getOrDie(e) {
      if (this.tag) return this.value;
      throw new Error(null != e ? e : 'Called getOrDie on None');
    }
    static from(e) {
      return l(e) ? h.some(e) : h.none();
    }
    getOrNull() {
      return this.tag ? this.value : null;
    }
    getOrUndefined() {
      return this.value;
    }
    each(e) {
      this.tag && e(this.value);
    }
    toArray() {
      return this.tag ? [this.value] : [];
    }
    toString() {
      return this.tag ? `some(${this.value})` : 'none()';
    }
  }
  h.singletonNone = new h(!1);
  const f = Array.prototype.slice,
    y = Array.prototype.indexOf,
    v = Array.prototype.push,
    C = (e, t) => {
      return (n = e), (o = t), y.call(n, o) > -1;
      var n, o;
    },
    b = (e, t) => {
      for (let n = 0, o = e.length; n < o; n++) if (t(e[n], n)) return !0;
      return !1;
    },
    N = (e, t) => {
      const n = e.length,
        o = new Array(n);
      for (let r = 0; r < n; r++) {
        const n = e[r];
        o[r] = t(n, r);
      }
      return o;
    },
    S = (e, t) => {
      for (let n = 0, o = e.length; n < o; n++) t(e[n], n);
    },
    L = (e, t) => {
      const n = [];
      for (let o = 0, r = e.length; o < r; o++) {
        const r = e[o];
        t(r, o) && n.push(r);
      }
      return n;
    },
    O = (e, t, n) => (
      S(e, (e, o) => {
        n = t(n, e, o);
      }),
      n
    ),
    A = (e, t, n) => {
      for (let o = 0, r = e.length; o < r; o++) {
        const r = e[o];
        if (t(r, o)) return h.some(r);
        if (n(r, o)) break;
      }
      return h.none();
    },
    T = (e, t) => A(e, t, g),
    x = (e, t) =>
      ((e) => {
        const t = [];
        for (let n = 0, o = e.length; n < o; ++n) {
          if (!s(e[n])) throw new Error('Arr.flatten item ' + n + ' was not an array, input: ' + e);
          v.apply(t, e[n]);
        }
        return t;
      })(N(e, t)),
    k = (e) => {
      const t = f.call(e, 0);
      return t.reverse(), t;
    },
    E = (e, t) => (t >= 0 && t < e.length ? h.some(e[t]) : h.none()),
    w = (e) => E(e, 0),
    D = (e) => E(e, e.length - 1),
    B = (e, t) => {
      const n = [],
        o = a(t) ? (e) => b(n, (n) => t(n, e)) : (e) => C(n, e);
      for (let t = 0, r = e.length; t < r; t++) {
        const r = e[t];
        o(r) || n.push(r);
      }
      return n;
    },
    M = Object.keys,
    P = (e, t) => {
      const n = M(e);
      for (let o = 0, r = n.length; o < r; o++) {
        const r = n[o];
        t(e[r], r);
      }
    },
    I = 'undefined' != typeof window ? window : Function('return this;')(),
    R = (e, t, n = u) => e.exists((e) => n(e, t)),
    U = (e, t, n) => (e.isSome() && t.isSome() ? h.some(n(e.getOrDie(), t.getOrDie())) : h.none()),
    $ = (e, t) =>
      ((e, t) => {
        let n = null != t ? t : I;
        for (let t = 0; t < e.length && null != n; ++t) n = n[e[t]];
        return n;
      })(e.split('.'), t),
    _ = ((H = /^\s+|\s+$/g), (e) => e.replace(H, ''));
  var H;
  const F = (e) => '\ufeff' === e,
    V = (e) => {
      if (null == e) throw new Error('Node cannot be null or undefined');
      return { dom: e };
    },
    j = (e, t) => {
      const n = (t || document).createElement('div');
      if (((n.innerHTML = e), !n.hasChildNodes() || n.childNodes.length > 1)) {
        const t = 'HTML does not have a single root node';
        throw (console.error(t, e), new Error(t));
      }
      return V(n.childNodes[0]);
    },
    K = (e, t) => {
      const n = (t || document).createElement(e);
      return V(n);
    },
    z = V,
    Q = (e, t) => {
      const n = e.dom;
      if (1 !== n.nodeType) return !1;
      {
        const e = n;
        if (void 0 !== e.matches) return e.matches(t);
        if (void 0 !== e.msMatchesSelector) return e.msMatchesSelector(t);
        if (void 0 !== e.webkitMatchesSelector) return e.webkitMatchesSelector(t);
        if (void 0 !== e.mozMatchesSelector) return e.mozMatchesSelector(t);
        throw new Error('Browser lacks native selectors');
      }
    },
    W = (e, t) => e.dom === t.dom,
    q = Q,
    Z = Object.getPrototypeOf,
    G = (e) => {
      const t = $('ownerDocument.defaultView', e);
      return (
        r(e) &&
        (((e) =>
          ((e, t) => {
            const n = ((e, t) => $(e, t))(e, t);
            if (null == n) throw new Error(e + ' not available on this browser');
            return n;
          })('HTMLElement', e))(t).prototype.isPrototypeOf(e) ||
          /^HTML\w*Element$/.test(Z(e).constructor.name))
      );
    },
    J = (e) => e.dom.nodeName.toLowerCase(),
    X = (e) => e.dom.nodeType,
    Y = (e) => (t) => X(t) === e,
    ee = (e) => te(e) && G(e.dom),
    te = Y(1),
    ne = Y(3),
    oe = Y(11),
    re = (e) => (t) => te(t) && J(t) === e,
    se = (e) => h.from(e.dom.parentNode).map(z),
    ie = (e) => N(e.dom.childNodes, z),
    le = (e, t) => {
      const n = e.dom.childNodes;
      return h.from(n[t]).map(z);
    },
    ae = (e) => le(e, 0),
    de = (e) => le(e, e.dom.childNodes.length - 1),
    ce = (e) => z(e.dom.host),
    me = (e, t) => {
      se(e).each((n) => {
        n.dom.insertBefore(t.dom, e.dom);
      });
    },
    ue = (e, t) => {
      e.dom.appendChild(t.dom);
    },
    pe = (e, t) => {
      S(t, (t) => {
        ue(e, t);
      });
    },
    ge = (e, t) => {
      const n = e.dom;
      P(t, (e, t) => {
        ((e, t, n) => {
          if (!(o(n) || i(n) || d(n)))
            throw (
              (console.error(
                'Invalid call to Attribute.set. Key ',
                t,
                ':: Value ',
                n,
                ':: Element ',
                e
              ),
              new Error('Attribute value was not simple'))
            );
          e.setAttribute(t, n + '');
        })(n, t, e);
      });
    },
    he = (e) => O(e.dom.attributes, (e, t) => ((e[t.name] = t.value), e), {}),
    fe = (e) => {
      (e.dom.textContent = ''),
        S(ie(e), (e) => {
          ye(e);
        });
    },
    ye = (e) => {
      const t = e.dom;
      null !== t.parentNode && t.parentNode.removeChild(t);
    },
    ve = (e) => ((e) => z(e.dom.cloneNode(!0)))(e),
    Ce = (e, t) => {
      const n = ((e, t) => {
        const n = K(t),
          o = he(e);
        return ge(n, o), n;
      })(e, t);
      var o, r;
      (r = n),
        ((e) => h.from(e.dom.nextSibling).map(z))((o = e)).fold(
          () => {
            se(o).each((e) => {
              ue(e, r);
            });
          },
          (e) => {
            me(e, r);
          }
        );
      const s = ie(e);
      return pe(n, s), ye(e), n;
    },
    be = (e) => N(e, z),
    Ne = (e) => {
      const t = ne(e) ? e.dom.parentNode : e.dom;
      if (null == t || null === t.ownerDocument) return !1;
      const n = t.ownerDocument;
      return ((e) => {
        const t = ((e) => z(e.dom.getRootNode()))(e);
        return oe((n = t)) && l(n.dom.host) ? h.some(t) : h.none();
        var n;
      })(z(t)).fold(() => n.body.contains(t), ((o = Ne), (r = ce), (e) => o(r(e))));
      var o, r;
    },
    Se = (e, t, n) => {
      ((e, t, n) => {
        if (!o(n))
          throw (
            (console.error(
              'Invalid call to CSS.set. Property ',
              t,
              ':: Value ',
              n,
              ':: Element ',
              e
            ),
            new Error('CSS value must be a string: ' + n))
          );
        ((e) => void 0 !== e.style && a(e.style.getPropertyValue))(e) && e.style.setProperty(t, n);
      })(e.dom, t, n);
    },
    Le = (e, t) => {
      const n = (t || document).createDocumentFragment();
      return (
        S(e, (e) => {
          n.appendChild(e.dom);
        }),
        z(n)
      );
    };
  var Oe = (e, t, n, o, r) => (e(n, o) ? h.some(n) : a(r) && r(n) ? h.none() : t(n, o, r));
  const Ae = (e, t, n) => {
      let o = e.dom;
      const r = a(n) ? n : g;
      for (; o.parentNode; ) {
        o = o.parentNode;
        const e = z(o);
        if (t(e)) return h.some(e);
        if (r(e)) break;
      }
      return h.none();
    },
    Te = (e, t, n) => Oe((e, t) => t(e), Ae, e, t, n),
    xe = (e, t, n) => Ae(e, (e) => Q(e, t), n);
  var ke = tinymce.util.Tools.resolve('tinymce.dom.RangeUtils'),
    Ee = tinymce.util.Tools.resolve('tinymce.dom.TreeWalker'),
    we = tinymce.util.Tools.resolve('tinymce.util.VK'),
    De = tinymce.util.Tools.resolve('tinymce.dom.DOMUtils'),
    Be = tinymce.util.Tools.resolve('tinymce.util.Tools');
  const Me = (e) => (t) => l(t) && t.nodeName.toLowerCase() === e,
    Pe = (e) => (t) => l(t) && e.test(t.nodeName),
    Ie = (e) => l(e) && 3 === e.nodeType,
    Re = (e) => l(e) && 1 === e.nodeType,
    Ue = Pe(/^(OL|UL|DL)$/),
    $e = Pe(/^(OL|UL)$/),
    _e = Me('ol'),
    He = Pe(/^(LI|DT|DD)$/),
    Fe = Pe(/^(DT|DD)$/),
    Ve = Pe(/^(TH|TD)$/),
    je = Me('br'),
    Ke = (e, t) => l(t) && t.nodeName in e.schema.getTextBlockElements(),
    ze = (e, t) => l(e) && e.nodeName in t,
    Qe = (e, t) => l(t) && t.nodeName in e.schema.getVoidElements(),
    We = (e, t, n) => {
      const o = e.isEmpty(t);
      return !(n && e.select('span[data-mce-type=bookmark]', t).length > 0) && o;
    },
    qe = (e, t) => e.isChildOf(t, e.getRoot()),
    Ze = (e) => (t) => t.options.get(e),
    Ge = Ze('lists_indent_on_tab'),
    Je = Ze('forced_root_block'),
    Xe = Ze('forced_root_block_attrs'),
    Ye = (e, t, n = {}) => {
      const o = e.dom,
        r = e.schema.getBlockElements(),
        s = o.createFragment(),
        i = Je(e),
        l = Xe(e);
      let a,
        d,
        c = !1;
      for (
        d = o.create(i, { ...l, ...(n.style ? { style: n.style } : {}) }),
          ze(t.firstChild, r) || s.appendChild(d);
        (a = t.firstChild);

      ) {
        const e = a.nodeName;
        c || ('SPAN' === e && 'bookmark' === a.getAttribute('data-mce-type')) || (c = !0),
          ze(a, r)
            ? (s.appendChild(a), (d = null))
            : (d || ((d = o.create(i, l)), s.appendChild(d)), d.appendChild(a));
      }
      return !c && d && d.appendChild(o.create('br', { 'data-mce-bogus': '1' })), s;
    },
    et = De.DOM,
    tt = re('dd'),
    nt = re('dt'),
    ot = (e, t) => {
      var n;
      tt(t)
        ? Ce(t, 'dt')
        : nt(t) &&
          ((n = t), h.from(n.dom.parentElement).map(z)).each((n) =>
            ((e, t, n) => {
              const o = et.select('span[data-mce-type="bookmark"]', t),
                r = Ye(e, n),
                s = et.createRng();
              s.setStartAfter(n), s.setEndAfter(t);
              const i = s.extractContents();
              for (let t = i.firstChild; t; t = t.firstChild)
                if ('LI' === t.nodeName && e.dom.isEmpty(t)) {
                  et.remove(t);
                  break;
                }
              e.dom.isEmpty(i) || et.insertAfter(i, t), et.insertAfter(r, t);
              const l = n.parentElement;
              l &&
                We(e.dom, l) &&
                ((e) => {
                  const t = e.parentNode;
                  t &&
                    Be.each(o, (e) => {
                      t.insertBefore(e, n.parentNode);
                    }),
                    et.remove(e);
                })(l),
                et.remove(n),
                We(e.dom, t) && et.remove(t);
            })(e, n.dom, t.dom)
          );
    },
    rt = (e) => {
      nt(e) && Ce(e, 'dd');
    },
    st = (e, t) => {
      if (Ie(e)) return { container: e, offset: t };
      const n = ke.getNode(e, t);
      return Ie(n)
        ? { container: n, offset: t >= e.childNodes.length ? n.data.length : 0 }
        : n.previousSibling && Ie(n.previousSibling)
          ? { container: n.previousSibling, offset: n.previousSibling.data.length }
          : n.nextSibling && Ie(n.nextSibling)
            ? { container: n.nextSibling, offset: 0 }
            : { container: e, offset: t };
    },
    it = (e) => {
      const t = e.cloneRange(),
        n = st(e.startContainer, e.startOffset);
      t.setStart(n.container, n.offset);
      const o = st(e.endContainer, e.endOffset);
      return t.setEnd(o.container, o.offset), t;
    },
    lt = ['OL', 'UL', 'DL'],
    at = lt.join(','),
    dt = (e, t) => {
      const n = t || e.selection.getStart(!0);
      return e.dom.getParent(n, at, ut(e, n));
    },
    ct = (e) => {
      const t = e.selection.getSelectedBlocks();
      return L(
        ((e, t) => {
          const n = Be.map(t, (t) => e.dom.getParent(t, 'li,dd,dt', ut(e, t)) || t);
          return B(n);
        })(e, t),
        He
      );
    },
    mt = (e, t) => {
      const n = e.dom.getParents(t, 'TD,TH');
      return n.length > 0 ? n[0] : e.getBody();
    },
    ut = (e, t) => {
      const n = e.dom.getParents(t, e.dom.isBlock),
        o = T(n, (t) => {
          return (
            ((t) => t.nodeName.toLowerCase() !== Je(e))(t) &&
            ((n = e.schema), !Ue((o = t)) && !He(o) && b(lt, (e) => n.isValidChild(o.nodeName, e)))
          );
          var n, o;
        });
      return o.getOr(e.getBody());
    },
    pt = (e, t) => {
      const n = e.dom.getParents(t, 'ol,ul', ut(e, t));
      return D(n);
    },
    gt = (e, t) => {
      const n = N(t, (t) => pt(e, t).getOr(t));
      return B(n);
    },
    ht = (e) => /\btox\-/.test(e.className),
    ft = (e, t) => A(e, Ue, Ve).exists((e) => e.nodeName === t && !ht(e)),
    yt = (e, t) => null !== t && !e.dom.isEditable(t),
    vt = (e, t) => {
      const n = e.dom.getParent(t, 'ol,ul,dl');
      return yt(e, n) || !e.selection.isEditable();
    },
    Ct = (e, t) => {
      const n = e.selection.getNode();
      return (
        t({ parents: e.dom.getParents(n), element: n }),
        e.on('NodeChange', t),
        () => e.off('NodeChange', t)
      );
    },
    bt = (e, t, n) => e.dispatch('ListMutation', { action: t, element: n }),
    Nt = (e) => q(e, 'OL,UL'),
    St = (e) => ae(e).exists(Nt),
    Lt = (e) => 'listAttributes' in e,
    Ot = (e) => 'isComment' in e,
    At = (e) => e.depth > 0,
    Tt = (e) => e.isSelected,
    xt = (e) => {
      const t = ie(e),
        n = de(e).exists(Nt) ? t.slice(0, -1) : t;
      return N(n, ve);
    },
    kt = (e, t) => {
      ue(e.item, t.list);
    },
    Et = (e, t) => {
      const n = { list: K(t, e), item: K('li', e) };
      return ue(n.list, n.item), n;
    },
    wt = (e, t, n) => {
      const o = t.slice(0, n.depth);
      return (
        D(o).each((t) => {
          if (Lt(n)) {
            const o = ((e, t, n) => {
              const o = K('li', e);
              return ge(o, t), pe(o, n), o;
            })(e, n.itemAttributes, n.content);
            ((e, t) => {
              ue(e.list, t), (e.item = t);
            })(t, o),
              ((e, t) => {
                J(e.list) !== t.listType && (e.list = Ce(e.list, t.listType)),
                  ge(e.list, t.listAttributes);
              })(t, n);
          } else if (((e) => 'isFragment' in e)(n)) pe(t.item, n.content);
          else {
            const e = j(`\x3c!--${n.content}--\x3e`);
            ue(t.list, e);
          }
        }),
        o
      );
    },
    Dt = (e, t) => {
      let n = h.none();
      const o = O(
        t,
        (t, o, r) =>
          Ot(o)
            ? 0 === r
              ? ((n = h.some(o)), t)
              : wt(e, t, o)
            : o.depth > t.length
              ? ((e, t, n) => {
                  const o = ((e, t, n) => {
                    const o = [];
                    for (let r = 0; r < n; r++)
                      o.push(Et(e, Lt(t) ? t.listType : t.parentListType));
                    return o;
                  })(e, n, n.depth - t.length);
                  var r;
                  return (
                    ((e) => {
                      for (let t = 1; t < e.length; t++) kt(e[t - 1], e[t]);
                    })(o),
                    ((e, t) => {
                      for (let t = 0; t < e.length - 1; t++)
                        Se(e[t].item, 'list-style-type', 'none');
                      D(e).each((e) => {
                        Lt(t) && (ge(e.list, t.listAttributes), ge(e.item, t.itemAttributes)),
                          pe(e.item, t.content);
                      });
                    })(o, n),
                    (r = o),
                    U(D(t), w(r), kt),
                    t.concat(o)
                  );
                })(e, t, o)
              : wt(e, t, o),
        []
      );
      return (
        n.each((e) => {
          const t = j(`\x3c!--${e.content}--\x3e`);
          w(o).each((e) => {
            ((e, t) => {
              ae(e).fold(
                () => {
                  ue(e, t);
                },
                (n) => {
                  e.dom.insertBefore(t.dom, n.dom);
                }
              );
            })(e.list, t);
          });
        }),
        w(o).map((e) => e.list)
      );
    },
    Bt = (e) => (
      S(e, (t, n) => {
        ((e, t) => {
          const n = e[t].depth,
            o = (e) => e.depth === n && !e.dirty,
            r = (e) => e.depth < n;
          return A(k(e.slice(0, t)), o, r).orThunk(() => A(e.slice(t + 1), o, r));
        })(e, n).fold(
          () => {
            t.dirty &&
              Lt(t) &&
              ((e) => {
                e.listAttributes = ((e, t) => {
                  const n = {};
                  var o;
                  return (
                    ((e, t, n, o) => {
                      P(e, (e, r) => {
                        (t(e, r) ? n : o)(e, r);
                      });
                    })(
                      e,
                      t,
                      ((o = n),
                      (e, t) => {
                        o[t] = e;
                      }),
                      c
                    ),
                    n
                  );
                })(e.listAttributes, (e, t) => 'start' !== t);
              })(t);
          },
          (e) => {
            return (
              (o = e),
              void (
                Lt((n = t)) &&
                Lt(o) &&
                ((n.listType = o.listType), (n.listAttributes = { ...o.listAttributes }))
              )
            );
            var n, o;
          }
        );
      }),
      e
    ),
    Mt = (e, t, n, o) => {
      var r, s;
      if (8 === X((s = o)) || '#comment' === J(s))
        return [
          {
            depth: e + 1,
            content: null !== (r = o.dom.nodeValue) && void 0 !== r ? r : '',
            dirty: !1,
            isSelected: !1,
            isComment: !0
          }
        ];
      t.each((e) => {
        W(e.start, o) && n.set(!0);
      });
      const i = ((e, t, n) =>
        se(e)
          .filter(te)
          .map((o) => ({
            depth: t,
            dirty: !1,
            isSelected: n,
            content: xt(e),
            itemAttributes: he(e),
            listAttributes: he(o),
            listType: J(o),
            isInPreviousLi: !1
          })))(o, e, n.get());
      t.each((e) => {
        W(e.end, o) && n.set(!1);
      });
      const l = de(o)
        .filter(Nt)
        .map((o) => It(e, t, n, o))
        .getOr([]);
      return i.toArray().concat(l);
    },
    Pt = (e, t, n, o) =>
      ae(o)
        .filter(Nt)
        .fold(
          () => Mt(e, t, n, o),
          (r) => {
            const s = O(
              ie(o),
              (o, s, i) => {
                if (0 === i) return o;
                if (q(s, 'LI')) return o.concat(Mt(e, t, n, s));
                {
                  const t = {
                    isFragment: !0,
                    depth: e,
                    content: [s],
                    isSelected: !1,
                    dirty: !1,
                    parentListType: J(r)
                  };
                  return o.concat(t);
                }
              },
              []
            );
            return It(e, t, n, r).concat(s);
          }
        ),
    It = (e, t, n, o) => x(ie(o), (o) => (Nt(o) ? It : Pt)(e + 1, t, n, o)),
    Rt = (e, t, n) => {
      const o = ((e, t) => {
        const n = (() => {
          let e = !1;
          return {
            get: () => e,
            set: (t) => {
              e = t;
            }
          };
        })();
        return N(e, (e) => ({ sourceList: e, entries: It(0, t, n, e) }));
      })(
        t,
        ((e) => {
          const t = N(ct(e), z);
          return U(T(t, p(St)), T(k(t), p(St)), (e, t) => ({ start: e, end: t }));
        })(e)
      );
      S(o, (t) => {
        ((e, t) => {
          S(L(e, Tt), (e) =>
            ((e, t) => {
              switch (e) {
                case 'Indent':
                  t.depth++;
                  break;
                case 'Outdent':
                  t.depth--;
                  break;
                case 'Flatten':
                  t.depth = 0;
              }
              t.dirty = !0;
            })(t, e)
          );
        })(t.entries, n);
        const o = ((e, t) =>
          x(
            ((e, t) => {
              if (0 === e.length) return [];
              {
                let n = t(e[0]);
                const o = [];
                let r = [];
                for (let s = 0, i = e.length; s < i; s++) {
                  const i = e[s],
                    l = t(i);
                  l !== n && (o.push(r), (r = [])), (n = l), r.push(i);
                }
                return 0 !== r.length && o.push(r), o;
              }
            })(t, At),
            (t) =>
              w(t).exists(At)
                ? ((e, t) => {
                    const n = Bt(t);
                    return Dt(e.contentDocument, n).toArray();
                  })(e, t)
                : ((e, t) => {
                    const n = Bt(t);
                    return N(n, (t) => {
                      const n = Ot(t) ? Le([j(`\x3c!--${t.content}--\x3e`)]) : Le(t.content),
                        o = Lt(t) ? t.itemAttributes : {};
                      return z(Ye(e, n.dom, o));
                    });
                  })(e, t)
          ))(e, t.entries);
        var r;
        S(o, (t) => {
          bt(e, 'Indent' === n ? 'IndentList' : 'OutdentList', t.dom);
        }),
          (r = t.sourceList),
          S(o, (e) => {
            me(r, e);
          }),
          ye(t.sourceList);
      });
    },
    Ut = (e, t) => {
      const n = be(
          ((e) => {
            const t = ((e) => {
                const t = pt(e, e.selection.getStart()),
                  n = L(e.selection.getSelectedBlocks(), $e);
                return t.toArray().concat(n);
              })(e),
              n = ((e) => {
                const t = e.selection.getStart();
                return e.dom.getParents(t, 'ol,ul', ut(e, t));
              })(e);
            return T(n, (e) => {
              return (
                (t = z(e)),
                se(t).exists(
                  (e) =>
                    He(e.dom) && ae(e).exists((e) => !Ue(e.dom)) && de(e).exists((e) => !Ue(e.dom))
                )
              );
              var t;
            }).fold(
              () => gt(e, t),
              (e) => [e]
            );
          })(e)
        ),
        o = be(((e) => L(ct(e), Fe))(e));
      let r = !1;
      if (n.length || o.length) {
        const s = e.selection.getBookmark();
        Rt(e, n, t),
          ((e, t, n) => {
            S(n, 'Indent' === t ? rt : (t) => ot(e, t));
          })(e, t, o),
          e.selection.moveToBookmark(s),
          e.selection.setRng(it(e.selection.getRng())),
          e.nodeChanged(),
          (r = !0);
      }
      return r;
    },
    $t = (e, t) =>
      !((e) => {
        const t = dt(e);
        return yt(e, t) || !e.selection.isEditable();
      })(e) && Ut(e, t),
    _t = (e) => $t(e, 'Indent'),
    Ht = (e) => $t(e, 'Outdent'),
    Ft = (e) => $t(e, 'Flatten');
  var Vt = tinymce.util.Tools.resolve('tinymce.dom.BookmarkManager');
  const jt = De.DOM,
    Kt = (e) => {
      const t = {},
        n = (n) => {
          let o = e[n ? 'startContainer' : 'endContainer'],
            r = e[n ? 'startOffset' : 'endOffset'];
          if (Re(o)) {
            const e = jt.create('span', { 'data-mce-type': 'bookmark' });
            o.hasChildNodes()
              ? ((r = Math.min(r, o.childNodes.length - 1)),
                n ? o.insertBefore(e, o.childNodes[r]) : jt.insertAfter(e, o.childNodes[r]))
              : o.appendChild(e),
              (o = e),
              (r = 0);
          }
          (t[n ? 'startContainer' : 'endContainer'] = o), (t[n ? 'startOffset' : 'endOffset'] = r);
        };
      return n(!0), e.collapsed || n(), t;
    },
    zt = (e) => {
      const t = (t) => {
        let n = e[t ? 'startContainer' : 'endContainer'],
          o = e[t ? 'startOffset' : 'endOffset'];
        if (n) {
          if (Re(n) && n.parentNode) {
            const e = n;
            (o = ((e) => {
              var t;
              let n = null === (t = e.parentNode) || void 0 === t ? void 0 : t.firstChild,
                o = 0;
              for (; n; ) {
                if (n === e) return o;
                (Re(n) && 'bookmark' === n.getAttribute('data-mce-type')) || o++,
                  (n = n.nextSibling);
              }
              return -1;
            })(n)),
              (n = n.parentNode),
              jt.remove(e),
              !n.hasChildNodes() && jt.isBlock(n) && n.appendChild(jt.create('br'));
          }
          (e[t ? 'startContainer' : 'endContainer'] = n), (e[t ? 'startOffset' : 'endOffset'] = o);
        }
      };
      t(!0), t();
      const n = jt.createRng();
      return (
        n.setStart(e.startContainer, e.startOffset),
        e.endContainer && n.setEnd(e.endContainer, e.endOffset),
        it(n)
      );
    },
    Qt = (e) => {
      switch (e) {
        case 'UL':
          return 'ToggleUlList';
        case 'OL':
          return 'ToggleOlList';
        case 'DL':
          return 'ToggleDLList';
      }
    },
    Wt = (e, t) => {
      Be.each(t, (t, n) => {
        e.setAttribute(n, t);
      });
    },
    qt = (e, t, n) => {
      ((e, t, n) => {
        const o = n['list-style-type'] ? n['list-style-type'] : null;
        e.setStyle(t, 'list-style-type', o);
      })(e, t, n),
        ((e, t, n) => {
          Wt(t, n['list-attributes']),
            Be.each(e.select('li', t), (e) => {
              Wt(e, n['list-item-attributes']);
            });
        })(e, t, n);
    },
    Zt = (e, t) => l(t) && !ze(t, e.schema.getBlockElements()),
    Gt = (e, t, n, o) => {
      let r = t[n ? 'startContainer' : 'endContainer'];
      const s = t[n ? 'startOffset' : 'endOffset'];
      Re(r) && (r = r.childNodes[Math.min(s, r.childNodes.length - 1)] || r),
        !n && je(r.nextSibling) && (r = r.nextSibling);
      const i = (t, n) => {
        var r;
        const s = new Ee(
            t,
            ((t) => {
              for (; !e.dom.isBlock(t) && t.parentNode && o !== t; ) t = t.parentNode;
              return t;
            })(t)
          ),
          i = n ? 'next' : 'prev';
        let l;
        for (; (l = s[i]()); )
          if (
            !Qe(e, l) &&
            !F(l.textContent) &&
            0 !== (null === (r = l.textContent) || void 0 === r ? void 0 : r.length)
          )
            return h.some(l);
        return h.none();
      };
      if (n && Ie(r))
        if (F(r.textContent)) r = i(r, !1).getOr(r);
        else
          for (
            null !== r.parentNode && Zt(e, r.parentNode) && (r = r.parentNode);
            null !== r.previousSibling && (Zt(e, r.previousSibling) || Ie(r.previousSibling));

          )
            r = r.previousSibling;
      if (!n && Ie(r))
        if (F(r.textContent)) r = i(r, !0).getOr(r);
        else
          for (
            null !== r.parentNode && Zt(e, r.parentNode) && (r = r.parentNode);
            null !== r.nextSibling && (Zt(e, r.nextSibling) || Ie(r.nextSibling));

          )
            r = r.nextSibling;
      for (; r.parentNode !== o; ) {
        const t = r.parentNode;
        if (Ke(e, r)) return r;
        if (/^(TD|TH)$/.test(t.nodeName)) return r;
        r = t;
      }
      return r;
    },
    Jt = (e, t, n) => {
      const o = e.selection.getRng();
      let r = 'LI';
      const s = ut(
          e,
          ((e, t) => {
            const n = e.selection.getStart(!0),
              o = Gt(e, t, !0, e.getBody());
            return (
              (r = z(o)),
              (s = z(t.commonAncestorContainer)),
              (i = r),
              (l = (function (e, ...t) {
                return (...n) => {
                  const o = t.concat(n);
                  return e.apply(null, o);
                };
              })(W, s)),
              Ae(i, l, void 0).isSome() ? t.commonAncestorContainer : n
            );
            var r, s, i, l;
          })(e, o)
        ),
        i = e.dom;
      if ('false' === i.getContentEditable(e.selection.getNode())) return;
      'DL' === (t = t.toUpperCase()) && (r = 'DT');
      const l = Kt(o),
        a = L(
          ((e, t, n) => {
            const o = [],
              r = e.dom,
              s = Gt(e, t, !0, n),
              i = Gt(e, t, !1, n);
            let l;
            const a = [];
            for (let e = s; e && (a.push(e), e !== i); e = e.nextSibling);
            return (
              Be.each(a, (t) => {
                var s;
                if (Ke(e, t)) return o.push(t), void (l = null);
                if (r.isBlock(t) || je(t)) return je(t) && r.remove(t), void (l = null);
                const i = t.nextSibling;
                Vt.isBookmarkNode(t) && (Ue(i) || Ke(e, i) || (!i && t.parentNode === n))
                  ? (l = null)
                  : (l ||
                      ((l = r.create('p')),
                      null === (s = t.parentNode) || void 0 === s || s.insertBefore(l, t),
                      o.push(l)),
                    l.appendChild(t));
              }),
              o
            );
          })(e, o, s),
          e.dom.isEditable
        );
      Be.each(a, (o) => {
        let s;
        const l = o.previousSibling,
          a = o.parentNode;
        He(a) ||
          (l &&
          Ue(l) &&
          l.nodeName === t &&
          ((e, t, n) => {
            const o = e.getStyle(t, 'list-style-type');
            let r = n ? n['list-style-type'] : '';
            return (r = null === r ? '' : r), o === r;
          })(i, l, n)
            ? ((s = l), (o = i.rename(o, r)), l.appendChild(o))
            : ((s = i.create(t)), a.insertBefore(s, o), s.appendChild(o), (o = i.rename(o, r))),
          ((e, t) => {
            Be.each(
              [
                'margin',
                'margin-right',
                'margin-bottom',
                'margin-left',
                'margin-top',
                'padding',
                'padding-right',
                'padding-bottom',
                'padding-left',
                'padding-top'
              ],
              (n) => e.setStyle(t, n, '')
            );
          })(i, o),
          qt(i, s, n),
          Yt(e.dom, s));
      }),
        e.selection.setRng(zt(l));
    },
    Xt = (e, t, n) => {
      return (
        ((e, t) => Ue(e) && e.nodeName === (null == t ? void 0 : t.nodeName))(t, n) &&
        ((e, t, n) =>
          e.getStyle(t, 'list-style-type', !0) === e.getStyle(n, 'list-style-type', !0))(e, t, n) &&
        ((o = n), t.className === o.className)
      );
      var o;
    },
    Yt = (e, t) => {
      let n,
        o = t.nextSibling;
      if (Xt(e, t, o)) {
        const r = o;
        for (; (n = r.firstChild); ) t.appendChild(n);
        e.remove(r);
      }
      if (((o = t.previousSibling), Xt(e, t, o))) {
        const r = o;
        for (; (n = r.lastChild); ) t.insertBefore(n, t.firstChild);
        e.remove(r);
      }
    },
    en = (e, t, n, o) => {
      if (t.nodeName !== n) {
        const r = e.dom.rename(t, n);
        qt(e.dom, r, o), bt(e, Qt(n), r);
      } else qt(e.dom, t, o), bt(e, Qt(n), t);
    },
    tn = (e, t, n, o) => {
      if (
        (t.classList.forEach((e, n, o) => {
          e.startsWith('tox-') && (o.remove(e), 0 === o.length && t.removeAttribute('class'));
        }),
        t.nodeName !== n)
      ) {
        const r = e.dom.rename(t, n);
        qt(e.dom, r, o), bt(e, Qt(n), r);
      } else qt(e.dom, t, o), bt(e, Qt(n), t);
    },
    nn = (e) => 'list-style-type' in e,
    on = (e, t, n) => {
      const o = dt(e);
      if (vt(e, o)) return;
      const s = ((e) => {
          const t = dt(e),
            n = e.selection.getSelectedBlocks();
          return ((e, t) => l(e) && 1 === t.length && t[0] === e)(t, n)
            ? ((e) => L(e.querySelectorAll(at), Ue))(t)
            : L(n, (e) => Ue(e) && t !== e);
        })(e),
        i = r(n) ? n : {};
      s.length > 0
        ? ((e, t, n, o, r) => {
            const s = Ue(t);
            if (!s || t.nodeName !== o || nn(r) || ht(t)) {
              Jt(e, o, r);
              const i = Kt(e.selection.getRng()),
                l = s ? [t, ...n] : n,
                a = s && ht(t) ? tn : en;
              Be.each(l, (t) => {
                a(e, t, o, r);
              }),
                e.selection.setRng(zt(i));
            } else Ft(e);
          })(e, o, s, t, i)
        : ((e, t, n, o) => {
            if (t !== e.getBody())
              if (t)
                if (t.nodeName !== n || nn(o) || ht(t)) {
                  const r = Kt(e.selection.getRng());
                  ht(t) &&
                    t.classList.forEach((e, n, o) => {
                      e.startsWith('tox-') &&
                        (o.remove(e), 0 === o.length && t.removeAttribute('class'));
                    }),
                    qt(e.dom, t, o);
                  const s = e.dom.rename(t, n);
                  Yt(e.dom, s), e.selection.setRng(zt(r)), Jt(e, n, o), bt(e, Qt(n), s);
                } else Ft(e);
              else Jt(e, n, o), bt(e, Qt(n), t);
          })(e, o, t, i);
    },
    rn = De.DOM,
    sn = (e, t) => {
      const n = Be.grep(e.select('ol,ul', t));
      Be.each(n, (t) => {
        ((e, t) => {
          const n = t.parentElement;
          if (n && 'LI' === n.nodeName && n.firstChild === t) {
            const o = n.previousSibling;
            o && 'LI' === o.nodeName
              ? (o.appendChild(t), We(e, n) && rn.remove(n))
              : rn.setStyle(n, 'listStyleType', 'none');
          }
          if (Ue(n)) {
            const e = n.previousSibling;
            e && 'LI' === e.nodeName && e.appendChild(t);
          }
        })(e, t);
      });
    },
    ln = (e, t, n, o) => {
      let r = t.startContainer;
      const s = t.startOffset;
      if (Ie(r) && (n ? s < r.data.length : s > 0)) return r;
      const i = e.schema.getNonEmptyElements();
      Re(r) && (r = ke.getNode(r, s));
      const l = new Ee(r, o);
      n &&
        ((e, t) => !!je(t) && e.isBlock(t.nextSibling) && !je(t.previousSibling))(e.dom, r) &&
        l.next();
      const a = n ? l.next.bind(l) : l.prev2.bind(l);
      for (; (r = a()); ) {
        if ('LI' === r.nodeName && !r.hasChildNodes()) return r;
        if (i[r.nodeName]) return r;
        if (Ie(r) && r.data.length > 0) return r;
      }
      return null;
    },
    an = (e, t) => {
      const n = t.childNodes;
      return 1 === n.length && !Ue(n[0]) && e.isBlock(n[0]);
    },
    dn = (e) =>
      h
        .from(e)
        .map(z)
        .filter(ee)
        .exists(
          (e) =>
            ((e, t = !1) => {
              return Ne(e)
                ? e.dom.isContentEditable
                : ((n = e), Oe((e, t) => Q(e, t), xe, n, '[contenteditable]', void 0)).fold(
                    m(t),
                    (e) => 'true' === ((e) => e.dom.contentEditable)(e)
                  );
              var n;
            })(e) && !C(['details'], J(e))
        ),
    cn = (e, t, n) => {
      let o;
      const r = an(e, n) ? n.firstChild : n;
      if (
        (((e, t) => {
          an(e, t) && dn(t.firstChild) && e.remove(t.firstChild, !0);
        })(e, t),
        !We(e, t, !0))
      )
        for (; (o = t.firstChild); ) r.appendChild(o);
    },
    mn = (e, t, n) => {
      let o;
      const r = t.parentNode;
      if (!qe(e, t) || !qe(e, n)) return;
      Ue(n.lastChild) && (o = n.lastChild),
        r === n.lastChild && je(r.previousSibling) && e.remove(r.previousSibling);
      const s = n.lastChild;
      s && je(s) && t.hasChildNodes() && e.remove(s),
        We(e, n, !0) && fe(z(n)),
        cn(e, t, n),
        o && n.appendChild(o);
      const i = ((e, t) => {
        const n = e.dom,
          o = t.dom;
        return n !== o && n.contains(o);
      })(z(n), z(t))
        ? e.getParents(t, Ue, n)
        : [];
      e.remove(t),
        S(i, (t) => {
          We(e, t) && t !== e.getRoot() && e.remove(t);
        });
    },
    un = (e, t) => {
      const n = e.dom,
        o = e.selection,
        r = o.getStart(),
        s = mt(e, r),
        i = n.getParent(o.getStart(), 'LI', s);
      if (i) {
        const r = i.parentElement;
        if (r === e.getBody() && We(n, r)) return !0;
        const l = it(o.getRng()),
          a = n.getParent(ln(e, l, t, s), 'LI', s),
          d = a && (t ? n.isChildOf(i, a) : n.isChildOf(a, i));
        if (a && a !== i && !d)
          return (
            e.undoManager.transact(() => {
              var n, o;
              t
                ? ((e, t, n, o) => {
                    const r = e.dom;
                    if (r.isEmpty(o))
                      ((e, t, n) => {
                        fe(z(n)), mn(e.dom, t, n), e.selection.setCursorLocation(n, 0);
                      })(e, n, o);
                    else {
                      const s = Kt(t);
                      mn(r, n, o), e.selection.setRng(zt(s));
                    }
                  })(e, l, a, i)
                : (null === (o = (n = i).parentNode) || void 0 === o ? void 0 : o.firstChild) === n
                  ? Ht(e)
                  : ((e, t, n, o) => {
                      const r = Kt(t);
                      mn(e.dom, n, o);
                      const s = zt(r);
                      e.selection.setRng(s);
                    })(e, l, i, a);
            }),
            !0
          );
        if (d && !t && a !== i) {
          const t = l.commonAncestorContainer.parentElement;
          return !(
            !t ||
            n.isChildOf(a, t) ||
            (e.undoManager.transact(() => {
              const o = Kt(l);
              cn(n, t, a), t.remove();
              const r = zt(o);
              e.selection.setRng(r);
            }),
            0)
          );
        }
        if (!a && !t && 0 === l.startOffset && 0 === l.endOffset)
          return (
            e.undoManager.transact(() => {
              Ft(e);
            }),
            !0
          );
      }
      return !1;
    },
    pn = (e) => {
      const t = e.selection.getStart(),
        n = mt(e, t);
      return e.dom.getParent(t, 'LI,DT,DD', n) || ct(e).length > 0;
    },
    gn = (e, t) => {
      const n = e.selection;
      return (
        !vt(e, n.getNode()) &&
        (n.isCollapsed()
          ? ((e, t) =>
              un(e, t) ||
              ((e, t) => {
                const n = e.dom,
                  o = e.selection.getStart(),
                  r = mt(e, o),
                  s = n.getParent(o, n.isBlock, r);
                if (s && n.isEmpty(s, void 0, { checkRootAsContent: !0 })) {
                  const o = it(e.selection.getRng()),
                    i = ln(e, o, t, r),
                    l = n.getParent(i, 'LI', r);
                  if (i && l) {
                    const a = (e) => C(['td', 'th', 'caption'], J(e)),
                      d = (e) => e.dom === r;
                    return (
                      !!((e, t, n = u) => U(e, t, n).getOr(e.isNone() && t.isNone()))(
                        Te(z(l), a, d),
                        Te(z(o.startContainer), a, d),
                        W
                      ) &&
                      (e.undoManager.transact(() => {
                        const o = l.parentNode;
                        ((e, t, n) => {
                          const o = e.getParent(t.parentNode, e.isBlock, n);
                          e.remove(t), o && e.isEmpty(o) && e.remove(o);
                        })(n, s, r),
                          Yt(n, o),
                          e.selection.select(i, !0),
                          e.selection.collapse(t);
                      }),
                      !0)
                    );
                  }
                }
                return !1;
              })(e, t))(e, t)
          : ((e) =>
              !!pn(e) &&
              (e.undoManager.transact(() => {
                let t = !0;
                const n = () => (t = !1);
                e.on('input', n),
                  e.execCommand('Delete'),
                  e.off('input', n),
                  t && e.dispatch('input'),
                  sn(e.dom, e.getBody());
              }),
              !0))(e))
      );
    },
    hn = (e) => {
      const t = k(_(e).split('')),
        n = N(t, (e, t) => {
          const n = e.toUpperCase().charCodeAt(0) - 'A'.charCodeAt(0) + 1;
          return Math.pow(26, t) * n;
        });
      return O(n, (e, t) => e + t, 0);
    },
    fn = (e) => {
      if (--e < 0) return '';
      {
        const t = e % 26,
          n = Math.floor(e / 26);
        return fn(n) + String.fromCharCode('A'.charCodeAt(0) + t);
      }
    },
    yn = (e) => {
      const t = parseInt(e.start, 10);
      return R(e.listStyleType, 'upper-alpha')
        ? fn(t)
        : R(e.listStyleType, 'lower-alpha')
          ? fn(t).toLowerCase()
          : e.start;
    },
    vn = (e, t) => () => {
      const n = dt(e);
      return l(n) && n.nodeName === t;
    },
    Cn = (e) => {
      e.addCommand('mceListProps', () => {
        ((e) => {
          const t = dt(e);
          _e(t) &&
            !vt(e, t) &&
            e.windowManager.open({
              title: 'List Properties',
              body: {
                type: 'panel',
                items: [
                  {
                    type: 'input',
                    name: 'start',
                    label: 'Start list at number',
                    inputMode: 'numeric'
                  }
                ]
              },
              initialData: {
                start: yn({
                  start: e.dom.getAttrib(t, 'start', '1'),
                  listStyleType: h.from(e.dom.getStyle(t, 'list-style-type'))
                })
              },
              buttons: [
                { type: 'cancel', name: 'cancel', text: 'Cancel' },
                { type: 'submit', name: 'save', text: 'Save', primary: !0 }
              ],
              onSubmit: (t) => {
                ((e) => {
                  switch (
                    ((e) =>
                      /^[0-9]+$/.test(e)
                        ? 2
                        : /^[A-Z]+$/.test(e)
                          ? 0
                          : /^[a-z]+$/.test(e)
                            ? 1
                            : e.length > 0
                              ? 4
                              : 3)(e)
                  ) {
                    case 2:
                      return h.some({ listStyleType: h.none(), start: e });
                    case 0:
                      return h.some({
                        listStyleType: h.some('upper-alpha'),
                        start: hn(e).toString()
                      });
                    case 1:
                      return h.some({
                        listStyleType: h.some('lower-alpha'),
                        start: hn(e).toString()
                      });
                    case 3:
                      return h.some({ listStyleType: h.none(), start: '' });
                    case 4:
                      return h.none();
                  }
                })(t.getData().start).each((t) => {
                  e.execCommand('mceListUpdate', !1, {
                    attrs: { start: '1' === t.start ? '' : t.start },
                    styles: { 'list-style-type': t.listStyleType.getOr('') }
                  });
                }),
                  t.close();
              }
            });
        })(e);
      });
    };
  var bn = tinymce.util.Tools.resolve('tinymce.html.Node');
  const Nn = (e) => 3 === e.type,
    Sn = (e) => 0 === e.length,
    Ln = (e) => {
      const t = (t, n) => {
          const o = bn.create('li');
          S(t, (e) => o.append(e)), n ? e.insert(o, n, !0) : e.append(o);
        },
        n = O(e.children(), (e, n) => (Nn(n) ? [...e, n] : Sn(e) || Nn(n) ? e : (t(e, n), [])), []);
      Sn(n) || t(n);
    },
    On = (e, t) => (n) => (
      n.setEnabled(e.selection.isEditable()),
      Ct(e, (o) => {
        n.setActive(ft(o.parents, t)), n.setEnabled(!vt(e, o.element) && e.selection.isEditable());
      })
    ),
    An = (e, t) => (n) => Ct(e, (o) => n.setEnabled(ft(o.parents, t) && !vt(e, o.element)));
  e.add(
    'lists',
    (e) => (
      ((e) => {
        (0, e.options.register)('lists_indent_on_tab', { processor: 'boolean', default: !0 });
      })(e),
      ((e) => {
        e.on('PreInit', () => {
          const { parser: t } = e;
          t.addNodeFilter('ul,ol', (e) => S(e, Ln));
        });
      })(e),
      e.hasPlugin('rtc', !0)
        ? Cn(e)
        : (((e) => {
            Ge(e) &&
              ((e) => {
                e.on('keydown', (t) => {
                  t.keyCode !== we.TAB ||
                    we.metaKeyPressed(t) ||
                    e.undoManager.transact(() => {
                      (t.shiftKey ? Ht(e) : _t(e)) && t.preventDefault();
                    });
                });
              })(e),
              ((e) => {
                e.on('ExecCommand', (t) => {
                  const n = t.command.toLowerCase();
                  ('delete' !== n && 'forwarddelete' !== n) || !pn(e) || sn(e.dom, e.getBody());
                }),
                  e.on('keydown', (t) => {
                    t.keyCode === we.BACKSPACE
                      ? gn(e, !1) && t.preventDefault()
                      : t.keyCode === we.DELETE && gn(e, !0) && t.preventDefault();
                  });
              })(e);
          })(e),
          ((e) => {
            e.on('BeforeExecCommand', (t) => {
              const n = t.command.toLowerCase();
              'indent' === n ? _t(e) : 'outdent' === n && Ht(e);
            }),
              e.addCommand('InsertUnorderedList', (t, n) => {
                on(e, 'UL', n);
              }),
              e.addCommand('InsertOrderedList', (t, n) => {
                on(e, 'OL', n);
              }),
              e.addCommand('InsertDefinitionList', (t, n) => {
                on(e, 'DL', n);
              }),
              e.addCommand('RemoveList', () => {
                Ft(e);
              }),
              Cn(e),
              e.addCommand('mceListUpdate', (t, n) => {
                r(n) &&
                  ((e, t) => {
                    const n = dt(e);
                    null === n ||
                      vt(e, n) ||
                      e.undoManager.transact(() => {
                        r(t.styles) && e.dom.setStyles(n, t.styles),
                          r(t.attrs) && P(t.attrs, (t, o) => e.dom.setAttrib(n, o, t));
                      });
                  })(e, n);
              }),
              e.addQueryStateHandler('InsertUnorderedList', vn(e, 'UL')),
              e.addQueryStateHandler('InsertOrderedList', vn(e, 'OL')),
              e.addQueryStateHandler('InsertDefinitionList', vn(e, 'DL'));
          })(e)),
      ((e) => {
        const t = (t) => () => e.execCommand(t);
        e.hasPlugin('advlist') ||
          (e.ui.registry.addToggleButton('numlist', {
            icon: 'ordered-list',
            active: !1,
            tooltip: 'Numbered list',
            onAction: t('InsertOrderedList'),
            onSetup: On(e, 'OL')
          }),
          e.ui.registry.addToggleButton('bullist', {
            icon: 'unordered-list',
            active: !1,
            tooltip: 'Bullet list',
            onAction: t('InsertUnorderedList'),
            onSetup: On(e, 'UL')
          }));
      })(e),
      ((e) => {
        const t = {
          text: 'List properties...',
          icon: 'ordered-list',
          onAction: () => e.execCommand('mceListProps'),
          onSetup: An(e, 'OL')
        };
        e.ui.registry.addMenuItem('listprops', t),
          e.ui.registry.addContextMenu('lists', {
            update: (t) => {
              const n = dt(e, t);
              return _e(n) ? ['listprops'] : [];
            }
          });
      })(e),
      ((e) => ({
        backspaceDelete: (t) => {
          gn(e, t);
        }
      }))(e)
    )
  );
})();
