!(function () {
  'use strict';
  var t = tinymce.util.Tools.resolve('tinymce.PluginManager');
  const e = (t) => (e) => typeof e === t,
    r = (t) =>
      'string' ===
      ((t) => {
        const e = typeof t;
        return null === t
          ? 'null'
          : 'object' === e && Array.isArray(t)
            ? 'array'
            : 'object' === e &&
                ((r = o = t),
                (n = String).prototype.isPrototypeOf(r) ||
                  (null === (i = o.constructor) || void 0 === i ? void 0 : i.name) === n.name)
              ? 'string'
              : e;
        var r, o, n, i;
      })(t),
    o = e('boolean'),
    n = (t) => !((t) => null == t)(t),
    i = e('function'),
    s = e('number'),
    l = () => false;
  class a {
    constructor(t, e) {
      (this.tag = t), (this.value = e);
    }
    static some(t) {
      return new a(!0, t);
    }
    static none() {
      return a.singletonNone;
    }
    fold(t, e) {
      return this.tag ? e(this.value) : t();
    }
    isSome() {
      return this.tag;
    }
    isNone() {
      return !this.tag;
    }
    map(t) {
      return this.tag ? a.some(t(this.value)) : a.none();
    }
    bind(t) {
      return this.tag ? t(this.value) : a.none();
    }
    exists(t) {
      return this.tag && t(this.value);
    }
    forall(t) {
      return !this.tag || t(this.value);
    }
    filter(t) {
      return !this.tag || t(this.value) ? this : a.none();
    }
    getOr(t) {
      return this.tag ? this.value : t;
    }
    or(t) {
      return this.tag ? this : t;
    }
    getOrThunk(t) {
      return this.tag ? this.value : t();
    }
    orThunk(t) {
      return this.tag ? this : t();
    }
    getOrDie(t) {
      if (this.tag) return this.value;
      throw new Error(null != t ? t : 'Called getOrDie on None');
    }
    static from(t) {
      return n(t) ? a.some(t) : a.none();
    }
    getOrNull() {
      return this.tag ? this.value : null;
    }
    getOrUndefined() {
      return this.value;
    }
    each(t) {
      this.tag && t(this.value);
    }
    toArray() {
      return this.tag ? [this.value] : [];
    }
    toString() {
      return this.tag ? `some(${this.value})` : 'none()';
    }
  }
  (a.singletonNone = new a(!1)), Array.prototype.slice;
  const u = (t, e) => {
    for (let r = 0, o = t.length; r < o; r++) e(t[r], r);
  };
  i(Array.from) && Array.from;
  const c = (t) => {
      if (null == t) throw new Error('Node cannot be null or undefined');
      return { dom: t };
    },
    d = c,
    h = (t, e) => {
      const r = t.dom;
      if (1 !== r.nodeType) return !1;
      {
        const t = r;
        if (void 0 !== t.matches) return t.matches(e);
        if (void 0 !== t.msMatchesSelector) return t.msMatchesSelector(e);
        if (void 0 !== t.webkitMatchesSelector) return t.webkitMatchesSelector(e);
        if (void 0 !== t.mozMatchesSelector) return t.mozMatchesSelector(e);
        throw new Error('Browser lacks native selectors');
      }
    },
    m = (t) => (e) => ((t) => t.dom.nodeType)(e) === t,
    g = m(1),
    f = m(3),
    v = m(11),
    y = (t) => d(t.dom.host),
    p = (t, e) => {
      t.dom.removeAttribute(e);
    },
    w = (t) => {
      const e = f(t) ? t.dom.parentNode : t.dom;
      if (null == e || null === e.ownerDocument) return !1;
      const r = e.ownerDocument;
      return ((t) => {
        const e = ((t) => d(t.dom.getRootNode()))(t);
        return v((r = e)) && n(r.dom.host) ? a.some(e) : a.none();
        var r;
      })(d(e)).fold(() => r.body.contains(e), ((o = w), (i = y), (t) => o(i(t))));
      var o, i;
    },
    b = (t) =>
      'rtl' ===
      ((t, e) => {
        const r = t.dom,
          o = window.getComputedStyle(r).getPropertyValue(e);
        return '' !== o || w(t)
          ? o
          : ((t, e) =>
              ((t) => void 0 !== t.style && i(t.style.getPropertyValue))(t)
                ? t.style.getPropertyValue(e)
                : '')(r, e);
      })(t, 'direction')
        ? 'rtl'
        : 'ltr',
    S = (t, e) =>
      ((t) =>
        ((t, e) => {
          const r = [];
          for (let o = 0, n = t.length; o < n; o++) {
            const n = t[o];
            e(n, o) && r.push(n);
          }
          return r;
        })(
          ((t, e) => {
            const r = t.length,
              o = new Array(r);
            for (let n = 0; n < r; n++) {
              const r = t[n];
              o[n] = e(r, n);
            }
            return o;
          })(t.dom.childNodes, d),
          (t) => h(t, e)
        ))(t),
    N = (t) => g(t) && 'li' === t.dom.nodeName.toLowerCase();
  const A = (t, e, n) => {
      u(e, (e) => {
        const c = d(e),
          m = N(c),
          f = ((t, e) => {
            return (
              e
                ? ((r = t),
                  (o = 'ol,ul'),
                  ((t, e, r) => {
                    let n = t.dom;
                    const s = i(r) ? r : l;
                    for (; n.parentNode; ) {
                      n = n.parentNode;
                      const t = d(n);
                      if (h(t, o)) return a.some(t);
                      if (s(t)) break;
                    }
                    return a.none();
                  })(r, 0, n))
                : a.some(t)
            ).getOr(t);
            var r, o, n;
          })(c, m);
        var v;
        ((v = f), ((t) => a.from(t.dom.parentNode).map(d))(v).filter(g)).each((e) => {
          if (
            (t.setStyle(f.dom, 'direction', null),
            b(e) === n
              ? p(f, 'dir')
              : ((t, e, n) => {
                  ((t, e, n) => {
                    if (!(r(n) || o(n) || s(n)))
                      throw (
                        (console.error(
                          'Invalid call to Attribute.set. Key ',
                          e,
                          ':: Value ',
                          n,
                          ':: Element ',
                          t
                        ),
                        new Error('Attribute value was not simple'))
                      );
                    t.setAttribute(e, n + '');
                  })(t.dom, e, n);
                })(f, 'dir', n),
            b(f) !== n && t.setStyle(f.dom, 'direction', n),
            m)
          ) {
            const e = S(f, 'li[dir],li[style]');
            u(e, (e) => {
              p(e, 'dir'), t.setStyle(e.dom, 'direction', null);
            });
          }
        });
      });
    },
    T = (t, e) => {
      t.selection.isEditable() && (A(t.dom, t.selection.getSelectedBlocks(), e), t.nodeChanged());
    },
    C = (t, e) => (r) => {
      const o = (o) => {
        const n = d(o.element);
        r.setActive(b(n) === e), r.setEnabled(t.selection.isEditable());
      };
      return (
        t.on('NodeChange', o), r.setEnabled(t.selection.isEditable()), () => t.off('NodeChange', o)
      );
    };
  t.add('directionality', (t) => {
    ((t) => {
      t.addCommand('mceDirectionLTR', () => {
        T(t, 'ltr');
      }),
        t.addCommand('mceDirectionRTL', () => {
          T(t, 'rtl');
        });
    })(t),
      ((t) => {
        t.ui.registry.addToggleButton('ltr', {
          tooltip: 'Left to right',
          icon: 'ltr',
          onAction: () => t.execCommand('mceDirectionLTR'),
          onSetup: C(t, 'ltr')
        }),
          t.ui.registry.addToggleButton('rtl', {
            tooltip: 'Right to left',
            icon: 'rtl',
            onAction: () => t.execCommand('mceDirectionRTL'),
            onSetup: C(t, 'rtl')
          });
      })(t);
  });
})();
