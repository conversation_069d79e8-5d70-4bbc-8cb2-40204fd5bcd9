!(function () {
  'use strict';
  const e = (e) =>
    'string' ===
    ((e) => {
      const t = typeof e;
      return null === e
        ? 'null'
        : 'object' === t && Array.isArray(e)
          ? 'array'
          : 'object' === t &&
              ((n = a = e),
              (r = String).prototype.isPrototypeOf(n) ||
                (null === (o = a.constructor) || void 0 === o ? void 0 : o.name) === r.name)
            ? 'string'
            : t;
      var n, a, r, o;
    })(e);
  const t = (e) => undefined === e;
  const n = (e) => 'function' == typeof e,
    a = () => false;
  class r {
    constructor(e, t) {
      (this.tag = e), (this.value = t);
    }
    static some(e) {
      return new r(!0, e);
    }
    static none() {
      return r.singletonNone;
    }
    fold(e, t) {
      return this.tag ? t(this.value) : e();
    }
    isSome() {
      return this.tag;
    }
    isNone() {
      return !this.tag;
    }
    map(e) {
      return this.tag ? r.some(e(this.value)) : r.none();
    }
    bind(e) {
      return this.tag ? e(this.value) : r.none();
    }
    exists(e) {
      return this.tag && e(this.value);
    }
    forall(e) {
      return !this.tag || e(this.value);
    }
    filter(e) {
      return !this.tag || e(this.value) ? this : r.none();
    }
    getOr(e) {
      return this.tag ? this.value : e;
    }
    or(e) {
      return this.tag ? this : e;
    }
    getOrThunk(e) {
      return this.tag ? this.value : e();
    }
    orThunk(e) {
      return this.tag ? this : e();
    }
    getOrDie(e) {
      if (this.tag) return this.value;
      throw new Error(null != e ? e : 'Called getOrDie on None');
    }
    static from(e) {
      return null == e ? r.none() : r.some(e);
    }
    getOrNull() {
      return this.tag ? this.value : null;
    }
    getOrUndefined() {
      return this.value;
    }
    each(e) {
      this.tag && e(this.value);
    }
    toArray() {
      return this.tag ? [this.value] : [];
    }
    toString() {
      return this.tag ? `some(${this.value})` : 'none()';
    }
  }
  r.singletonNone = new r(!1);
  const o = Array.prototype.slice,
    i = Array.prototype.indexOf,
    s = (e, t) => {
      const n = e.length,
        a = new Array(n);
      for (let r = 0; r < n; r++) {
        const n = e[r];
        a[r] = t(n, r);
      }
      return a;
    },
    c = (e, t) => {
      const n = [];
      for (let a = 0, r = e.length; a < r; a++) {
        const r = e[a];
        t(r, a) && n.push(r);
      }
      return n;
    },
    m = (e, t) => {
      const n = o.call(e, 0);
      return n.sort(t), n;
    };
  n(Array.from) && Array.from;
  const l = Object.keys,
    u = Object.hasOwnProperty,
    p = (e, t) => u.call(e, t);
  let y = 0;
  const h = (e) => {
    const t = new Date().getTime(),
      n = Math.floor((window.crypto.getRandomValues(new Uint32Array(1))[0] / 4294967295) * 1e9);
    return y++, e + '_' + n + y + String(t);
  };
  var d = tinymce.util.Tools.resolve('tinymce.PluginManager');
  const g = (e) => (t) => t.options.get(e),
    k = g('help_tabs'),
    v = g('forced_plugins');
  var b = tinymce.util.Tools.resolve('tinymce.Resource'),
    f = tinymce.util.Tools.resolve('tinymce.util.I18n');
  const A = (e, t) => b.load(`tinymce.html-i18n.help-keynav.${t}`, `${e}/js/i18n/keynav/${t}.js`),
    w = (e) => A(e, f.getCode()).catch(() => A(e, 'en'));
  var C = tinymce.util.Tools.resolve('tinymce.Env');
  const S = (e) => {
      const t = C.os.isMacOS() || C.os.isiOS(),
        n = t
          ? {
              alt: '&#x2325;',
              ctrl: '&#x2303;',
              shift: '&#x21E7;',
              meta: '&#x2318;',
              access: '&#x2303;&#x2325;'
            }
          : { meta: 'Ctrl ', access: 'Shift + Alt ' },
        a = e.split('+'),
        r = s(a, (e) => {
          const t = e.toLowerCase().trim();
          return p(n, t) ? n[t] : e;
        });
      return t ? r.join('').replace(/\s/, '') : r.join('+');
    },
    M = [
      { shortcuts: ['Meta + B'], action: 'Bold' },
      { shortcuts: ['Meta + I'], action: 'Italic' },
      { shortcuts: ['Meta + U'], action: 'Underline' },
      { shortcuts: ['Meta + A'], action: 'Select all' },
      { shortcuts: ['Meta + Y', 'Meta + Shift + Z'], action: 'Redo' },
      { shortcuts: ['Meta + Z'], action: 'Undo' },
      { shortcuts: ['Access + 1'], action: 'Heading 1' },
      { shortcuts: ['Access + 2'], action: 'Heading 2' },
      { shortcuts: ['Access + 3'], action: 'Heading 3' },
      { shortcuts: ['Access + 4'], action: 'Heading 4' },
      { shortcuts: ['Access + 5'], action: 'Heading 5' },
      { shortcuts: ['Access + 6'], action: 'Heading 6' },
      { shortcuts: ['Access + 7'], action: 'Paragraph' },
      { shortcuts: ['Access + 8'], action: 'Div' },
      { shortcuts: ['Access + 9'], action: 'Address' },
      { shortcuts: ['Alt + 0'], action: 'Open help dialog' },
      { shortcuts: ['Alt + F9'], action: 'Focus to menubar' },
      { shortcuts: ['Alt + F10'], action: 'Focus to toolbar' },
      { shortcuts: ['Alt + F11'], action: 'Focus to element path' },
      { shortcuts: ['Alt + F12'], action: 'Focus to notification' },
      { shortcuts: ['Ctrl + F9'], action: 'Focus to contextual toolbar' },
      { shortcuts: ['Shift + Enter'], action: 'Open popup menu for split buttons' },
      { shortcuts: ['Meta + K'], action: 'Insert link (if link plugin activated)' },
      { shortcuts: ['Meta + S'], action: 'Save (if save plugin activated)' },
      { shortcuts: ['Meta + F'], action: 'Find (if searchreplace plugin activated)' },
      { shortcuts: ['Meta + Shift + F'], action: 'Switch to or from fullscreen mode' }
    ],
    _ = () => ({
      name: 'shortcuts',
      title: 'Handy Shortcuts',
      items: [
        {
          type: 'table',
          header: ['Action', 'Shortcut'],
          cells: s(M, (e) => {
            const t = s(e.shortcuts, S).join(' or ');
            return [e.action, t];
          })
        }
      ]
    }),
    x = s(
      [
        { key: 'accordion', name: 'Accordion' },
        { key: 'anchor', name: 'Anchor' },
        { key: 'autolink', name: 'Autolink' },
        { key: 'autoresize', name: 'Autoresize' },
        { key: 'autosave', name: 'Autosave' },
        { key: 'charmap', name: 'Character Map' },
        { key: 'code', name: 'Code' },
        { key: 'codesample', name: 'Code Sample' },
        { key: 'colorpicker', name: 'Color Picker' },
        { key: 'directionality', name: 'Directionality' },
        { key: 'emoticons', name: 'Emoticons' },
        { key: 'fullscreen', name: 'Full Screen' },
        { key: 'help', name: 'Help' },
        { key: 'image', name: 'Image' },
        { key: 'importcss', name: 'Import CSS' },
        { key: 'insertdatetime', name: 'Insert Date/Time' },
        { key: 'link', name: 'Link' },
        { key: 'lists', name: 'Lists' },
        { key: 'advlist', name: 'List Styles' },
        { key: 'media', name: 'Media' },
        { key: 'nonbreaking', name: 'Nonbreaking' },
        { key: 'pagebreak', name: 'Page Break' },
        { key: 'preview', name: 'Preview' },
        { key: 'quickbars', name: 'Quick Toolbars' },
        { key: 'save', name: 'Save' },
        { key: 'searchreplace', name: 'Search and Replace' },
        { key: 'table', name: 'Table' },
        { key: 'textcolor', name: 'Text Color' },
        { key: 'visualblocks', name: 'Visual Blocks' },
        { key: 'visualchars', name: 'Visual Characters' },
        { key: 'wordcount', name: 'Word Count' },
        { key: 'a11ychecker', name: 'Accessibility Checker', type: 'premium' },
        {
          key: 'typography',
          name: 'Advanced Typography',
          type: 'premium',
          slug: 'advanced-typography'
        },
        { key: 'ai', name: 'AI Assistant', type: 'premium' },
        { key: 'casechange', name: 'Case Change', type: 'premium' },
        { key: 'checklist', name: 'Checklist', type: 'premium' },
        { key: 'advcode', name: 'Enhanced Code Editor', type: 'premium' },
        {
          key: 'mediaembed',
          name: 'Enhanced Media Embed',
          type: 'premium',
          slug: 'introduction-to-mediaembed'
        },
        { key: 'advtable', name: 'Enhanced Tables', type: 'premium' },
        { key: 'exportpdf', name: 'Export to PDF', type: 'premium' },
        { key: 'exportword', name: 'Export to Word', type: 'premium' },
        { key: 'footnotes', name: 'Footnotes', type: 'premium' },
        { key: 'formatpainter', name: 'Format Painter', type: 'premium' },
        { key: 'editimage', name: 'Image Editing', type: 'premium' },
        { key: 'uploadcare', name: 'Image Optimizer Powered by Uploadcare', type: 'premium' },
        { key: 'importword', name: 'Import from Word', type: 'premium' },
        { key: 'inlinecss', name: 'Inline CSS', type: 'premium', slug: 'inline-css' },
        { key: 'linkchecker', name: 'Link Checker', type: 'premium' },
        { key: 'math', name: 'Math', type: 'premium' },
        { key: 'markdown', name: 'Markdown', type: 'premium' },
        { key: 'mentions', name: 'Mentions', type: 'premium' },
        { key: 'mergetags', name: 'Merge Tags', type: 'premium' },
        { key: 'pageembed', name: 'Page Embed', type: 'premium' },
        { key: 'permanentpen', name: 'Permanent Pen', type: 'premium' },
        {
          key: 'powerpaste',
          name: 'PowerPaste',
          type: 'premium',
          slug: 'introduction-to-powerpaste'
        },
        { key: 'revisionhistory', name: 'Revision History', type: 'premium' },
        {
          key: 'tinymcespellchecker',
          name: 'Spell Checker',
          type: 'premium',
          slug: 'introduction-to-tiny-spellchecker'
        },
        { key: 'autocorrect', name: 'Spelling Autocorrect', type: 'premium' },
        { key: 'tableofcontents', name: 'Table of Contents', type: 'premium' },
        { key: 'advtemplate', name: 'Templates', type: 'premium', slug: 'advanced-templates' },
        {
          key: 'tinycomments',
          name: 'Tiny Comments',
          type: 'premium',
          slug: 'introduction-to-tiny-comments'
        },
        { key: 'tinydrive', name: 'Tiny Drive', type: 'premium', slug: 'tinydrive-introduction' }
      ],
      (e) => ({ ...e, type: e.type || 'opensource', slug: e.slug || e.key })
    ),
    T = (e) => {
      const o = (e) =>
          `<a data-alloy-tabstop="true" tabindex="-1" href="${e.url}" target="_blank" rel="noopener">${e.name}</a>`,
        u = (e, t) => {
          return ((i = x),
          (s = (e) => e.key === t),
          ((e, t, n) => {
            for (let a = 0, o = e.length; a < o; a++) {
              const o = e[a];
              if (t(o, a)) return r.some(o);
              if (n(o, a)) break;
            }
            return r.none();
          })(i, s, a)).fold(
            () =>
              ((e, t) => {
                const a = e.plugins[t].getMetadata;
                if (n(a)) {
                  const e = a();
                  return { name: e.name, html: o(e) };
                }
                return { name: t, html: t };
              })(e, t),
            (e) => {
              const t = 'premium' === e.type ? `${e.name}*` : e.name;
              return {
                name: t,
                html: o({ name: t, url: `https://www.tiny.cloud/docs/tinymce/7/${e.slug}/` })
              };
            }
          );
          var i, s;
        },
        p = (e) => {
          const n = ((e) => {
              const n = l(e.plugins),
                a = v(e),
                r = t(a) ? ['onboarding'] : a.concat(['onboarding']);
              return c(n, (e) => !(((e, t) => i.call(e, t))(r, e) > -1));
            })(e),
            a = m(
              s(n, (t) => u(e, t)),
              (e, t) => e.name.localeCompare(t.name)
            ),
            r = s(a, (e) => '<li>' + e.html + '</li>'),
            o = r.length,
            p = r.join('');
          return (
            '<p><b>' + f.translate(['Plugins installed ({0}):', o]) + '</b></p><ul>' + p + '</ul>'
          );
        },
        y = {
          type: 'htmlpanel',
          presets: 'document',
          html: [
            ((e) => (null == e ? '' : '<div>' + p(e) + '</div>'))(e),
            (() => {
              const e = c(x, ({ type: e }) => 'premium' === e),
                t = m(
                  s(e, (e) => e.name),
                  (e, t) => e.localeCompare(t)
                ),
                n = s(t, (e) => `<li>${e}</li>`).join('');
              return (
                '<div><p><b>' +
                f.translate('Premium plugins:') +
                '</b></p><ul>' +
                n +
                '<li class="tox-help__more-link" "><a href="https://www.tiny.cloud/pricing/?utm_campaign=help_dialog_plugin_tab&utm_source=tiny&utm_medium=referral&utm_term=read_more&utm_content=premium_plugin_heading" rel="noopener" target="_blank" data-alloy-tabstop="true" tabindex="-1">' +
                f.translate('Learn more...') +
                '</a></li></ul></div>'
              );
            })()
          ].join('')
        };
      return { name: 'plugins', title: 'Plugins', items: [y] };
    };
  var O = tinymce.util.Tools.resolve('tinymce.EditorManager');
  const P = (t, n, a) => () => {
    (async (t, n, a) => {
      const o = _(),
        i = await (async (e) => ({
          name: 'keyboardnav',
          title: 'Keyboard Navigation',
          items: [{ type: 'htmlpanel', presets: 'document', html: await w(e) }]
        }))(a),
        c = T(t),
        m = (() => {
          var e, t;
          const n =
            '<a data-alloy-tabstop="true" tabindex="-1" href="https://www.tiny.cloud/docs/tinymce/7/changelog/?utm_campaign=help_dialog_version_tab&utm_source=tiny&utm_medium=referral" rel="noopener" target="_blank">TinyMCE ' +
            ((e = O.majorVersion),
            (t = O.minorVersion),
            (0 === e.indexOf('@') ? 'X.X.X' : e + '.' + t) + '</a>');
          return {
            name: 'versions',
            title: 'Version',
            items: [
              {
                type: 'htmlpanel',
                html: '<p>' + f.translate(['You are using {0}', n]) + '</p>',
                presets: 'document'
              }
            ]
          };
        })(),
        u = { [o.name]: o, [i.name]: i, [c.name]: c, [m.name]: m, ...n.get() };
      return r.from(k(t)).fold(
        () =>
          ((e) => {
            const t = l(e),
              n = t.indexOf('versions');
            return -1 !== n && (t.splice(n, 1), t.push('versions')), { tabs: e, names: t };
          })(u),
        (t) =>
          ((t, n) => {
            const a = {},
              r = s(t, (t) => {
                var r;
                if (e(t)) return p(n, t) && (a[t] = n[t]), t;
                {
                  const e = null !== (r = t.name) && void 0 !== r ? r : h('tab-name');
                  return (a[e] = t), e;
                }
              });
            return { tabs: a, names: r };
          })(t, u)
      );
    })(t, n, a).then(({ tabs: e, names: n }) => {
      const a = {
        type: 'tabpanel',
        tabs: ((e) => {
          const t = [],
            n = (e) => {
              t.push(e);
            };
          for (let t = 0; t < e.length; t++) e[t].each(n);
          return t;
        })(
          s(n, (t) => {
            return p((n = e), (a = t)) ? r.from(n[a]) : r.none();
            var n, a;
          })
        )
      };
      t.windowManager.open({
        title: 'Help',
        size: 'medium',
        body: a,
        buttons: [{ type: 'cancel', name: 'close', text: 'Close', primary: !0 }],
        initialData: {}
      });
    });
  };
  d.add('help', (e, t) => {
    const n = (() => {
        let e = {};
        return {
          get: () => e,
          set: (t) => {
            e = t;
          }
        };
      })(),
      a = ((e) => ({
        addTab: (t) => {
          var n;
          const a = null !== (n = t.name) && void 0 !== n ? n : h('tab-name'),
            r = e.get();
          (r[a] = t), e.set(r);
        }
      }))(n);
    ((e) => {
      (0, e.options.register)('help_tabs', { processor: 'array' });
    })(e);
    const r = P(e, n, t);
    return (
      ((e, t) => {
        e.ui.registry.addButton('help', {
          icon: 'help',
          tooltip: 'Help',
          onAction: t,
          context: 'any'
        }),
          e.ui.registry.addMenuItem('help', {
            text: 'Help',
            icon: 'help',
            shortcut: 'Alt+0',
            onAction: t,
            context: 'any'
          });
      })(e, r),
      ((e, t) => {
        e.addCommand('mceHelp', t);
      })(e, r),
      e.shortcuts.add('Alt+0', 'Open help dialog', 'mceHelp'),
      ((e, t) => {
        e.on('init', () => {
          w(t);
        });
      })(e, t),
      a
    );
  });
})();
