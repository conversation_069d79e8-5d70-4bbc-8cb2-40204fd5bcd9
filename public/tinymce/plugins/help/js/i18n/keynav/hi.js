tinymce.Resource.add(
  'tinymce.html-i18n.help-keynav.hi',
  '<h1>कीबोर्ड नेविगेशन शुरू करें</h1>\n' +
    '\n' +
    '<dl>\n' +
    '  <dt>मेन्यू बार पर फ़ोकस करें</dt>\n' +
    '  <dd>Windows या Linux: Alt+F9</dd>\n' +
    '  <dd>macOS: &#x2325;F9</dd>\n' +
    '  <dt>टूलबार पर फ़ोकस करें</dt>\n' +
    '  <dd>Windows या Linux: Alt+F10</dd>\n' +
    '  <dd>macOS: &#x2325;F10</dd>\n' +
    '  <dt>फ़ुटर पर फ़ोकस करें</dt>\n' +
    '  <dd>Windows या Linux: Alt+F11</dd>\n' +
    '  <dd>macOS: &#x2325;F11</dd>\n' +
    '  <dt>नोटिफ़िकेशन फ़ोकस</dt>\n' +
    '  <dd>Windows या Linux: Alt+F12</dd>\n' +
    '  <dd>macOS: &#x2325;F12</dd>\n' +
    '  <dt>प्रासंगिक टूलबार पर फ़ोकस करें</dt>\n' +
    '  <dd>Windows, Linux या macOS: Ctrl+F9</dd>\n' +
    '</dl>\n' +
    '\n' +
    '<p>नेविगेशन पहले UI आइटम पर शुरू होगा, जिसे हाइलाइट किया जाएगा या पहले आइटम के मामले में फ़ुटर तत्व पथ में\n' +
    '  रेखांकित किया जाएगा।</p>\n' +
    '\n' +
    '<h1>UI सेक्शन के बीच नेविगेट करें</h1>\n' +
    '\n' +
    '<p>एक UI सेक्शन से दूसरे सेक्शन में जाने के लिए, <strong>Tab</strong> दबाएं।</p>\n' +
    '\n' +
    '<p>एक UI सेक्शन से पिछले सेक्शन में जाने के लिए, <strong>Shift+Tab</strong> दबाएं।</p>\n' +
    '\n' +
    '<p>इन UI सेक्शन का <strong>Tab</strong> क्रम नीचे दिया गया है:</p>\n' +
    '\n' +
    '<ol>\n' +
    '  <li>मेन्यू बार</li>\n' +
    '  <li>प्रत्येक टूलबार समूह</li>\n' +
    '  <li>साइडबार</li>\n' +
    '  <li>फ़ुटर में तत्व पथ</li>\n' +
    '  <li>फ़ुटर में शब्द गणना टॉगल बटन</li>\n' +
    '  <li>फ़ुटर में ब्रांडिंग लिंक</li>\n' +
    '  <li>फ़ुटर में संपादक का आकार बदलने का हैंडल</li>\n' +
    '</ol>\n' +
    '\n' +
    '<p>अगर कोई UI सेक्शन मौजूद नहीं है, तो उसे छोड़ दिया जाता है।</p>\n' +
    '\n' +
    '<p>अगर फ़ुटर में कीबोर्ड नेविगेशन फ़ोकस है, और कोई दिखा देने वाला साइडबार नहीं है, तो <strong>Shift+Tab</strong> दबाने से\n' +
    '  फ़ोकस पहले टूलबार समूह पर चला जाता है, पिछले पर नहीं।</p>\n' +
    '\n' +
    '<h1>UI सेक्शन के भीतर नेविगेट करें</h1>\n' +
    '\n' +
    '<p>एक UI तत्व से दूसरे में जाने के लिए उपयुक्त <strong>ऐरो</strong> कुंजी दबाएं।</p>\n' +
    '\n' +
    '<p><strong>बाएं</strong> और <strong>दाएं</strong> ऐरो कुंजियां</p>\n' +
    '\n' +
    '<ul>\n' +
    '  <li>मेन्यू बार में मेन्यू के बीच ले जाती हैं।</li>\n' +
    '  <li>मेन्यू में एक सब-मेन्यू खोलें।</li>\n' +
    '  <li>टूलबार समूह में बटनों के बीच ले जाएं।</li>\n' +
    '  <li>फ़ुटर के तत्व पथ में आइटम के बीच ले जाएं।</li>\n' +
    '</ul>\n' +
    '\n' +
    '<p><strong>नीचे</strong> और <strong>ऊपर</strong> ऐरो कुंजियां</p>\n' +
    '\n' +
    '<ul>\n' +
    '  <li>मेन्यू में मेन्यू आइटम के बीच ले जाती हैं।</li>\n' +
    '  <li>टूलबार पॉप-अप मेन्यू में आइटम के बीच ले जाएं।</li>\n' +
    '</ul>\n' +
    '\n' +
    '<p>फ़ोकस वाले UI सेक्शन के भीतर <strong>ऐरो</strong> कुंजियां चलाती रहती हैं।</p>\n' +
    '\n' +
    '<p>कोई खुला मेन्यू, कोई खुला सब-मेन्यू या कोई खुला पॉप-अप मेन्यू बंद करने के लिए <strong>Esc</strong> कुंजी दबाएं।</p>\n' +
    '\n' +
    "<p>अगर मौजूदा फ़ोकस किसी विशेष UI सेक्शन के 'शीर्ष' पर है, तो <strong>Esc</strong> कुंजी दबाने से भी\n" +
    '  कीबोर्ड नेविगेशन पूरी तरह से बाहर हो जाता है।</p>\n' +
    '\n' +
    '<h1>मेन्यू आइटम या टूलबार बटन निष्पादित करें</h1>\n' +
    '\n' +
    '<p>जब वांछित मेन्यू आइटम या टूलबार बटन हाइलाइट किया जाता है, तो आइटम को निष्पादित करने के लिए <strong>Return</strong>, <strong>Enter</strong>,\n' +
    '  या <strong>Space bar</strong> दबाएं।</p>\n' +
    '\n' +
    '<h1>गैर-टैब वाले डायलॉग पर नेविगेट करें</h1>\n' +
    '\n' +
    '<p>गैर-टैब वाले डायलॉग में, डायलॉग खुलने पर पहला इंटरैक्टिव घटक फ़ोकस लेता है।</p>\n' +
    '\n' +
    '<p><strong>Tab</strong> or <strong>Shift+Tab</strong> दबाकर इंटरैक्टिव डायलॉग घटकों के बीच नेविगेट करें।</p>\n' +
    '\n' +
    '<h1>टैब किए गए डायलॉग पर नेविगेट करें</h1>\n' +
    '\n' +
    '<p>टैब किए गए डायलॉग में, डायलॉग खुलने पर टैब मेन्यू में पहला बटन फ़ोकस लेता है।</p>\n' +
    '\n' +
    '<p>इस डायलॉग टैब के इंटरैक्टिव घटकों के बीच नेविगेट करने के लिए <strong>Tab</strong> या\n' +
    '  <strong>Shift+Tab</strong> दबाएं।</p>\n' +
    '\n' +
    '<p>टैब मेन्यू को फ़ोकस देकर और फिर उपलब्ध टैब में के बीच जाने के लिए उपयुक्त <strong>ऐरो</strong>\n' +
    '  कुंजी दबाकर दूसरे डायलॉग टैब पर स्विच करें।</p>\n'
);
