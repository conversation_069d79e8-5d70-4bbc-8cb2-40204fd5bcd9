tinymce.Resource.add(
  'tinymce.html-i18n.help-keynav.ar',
  '<h1>بدء التنقل بواسطة لوحة المفاتيح</h1>\n' +
    '\n' +
    '<dl>\n' +
    '  <dt>التركيز على شريط القوائم</dt>\n' +
    '  <dd>نظاما التشغيل Windows أو Linux: Alt + F9</dd>\n' +
    '  <dd>نظام التشغيل macOS: &#x2325;F9</dd>\n' +
    '  <dt>التركيز على شريط الأدوات</dt>\n' +
    '  <dd>نظاما التشغيل Windows أو Linux: Alt + F10</dd>\n' +
    '  <dd>نظام التشغيل macOS: &#x2325;F10</dd>\n' +
    '  <dt>التركيز على التذييل</dt>\n' +
    '  <dd>نظاما التشغيل Windows أو Linux: Alt + F11</dd>\n' +
    '  <dd>نظام التشغيل macOS: &#x2325;F11</dd>\n' +
    '  <dt>تركيز الإشعارات</dt>\n' +
    '  <dd>نظاما التشغيل Windows أو Linux: Alt + F12</dd>\n' +
    '  <dd>نظام التشغيل macOS: &#x2325;F12</dd>\n' +
    '  <dt>التركيز على شريط أدوات السياق</dt>\n' +
    '  <dd>أنظمة التشغيل Windows أو Linux أو macOS: Ctrl+F9</dd>\n' +
    '</dl>\n' +
    '\n' +
    '<p>سيبدأ التنقل عند عنصر واجهة المستخدم الأول، والذي سيتم تمييزه أو تسطيره في حالة العنصر الأول في\n' +
    '  مسار عنصر التذييل.</p>\n' +
    '\n' +
    '<h1>التنقل بين أقسام واجهة المستخدم</h1>\n' +
    '\n' +
    '<p>للانتقال من أحد أقسام واجهة المستخدم إلى القسم التالي، اضغط على <strong>Tab</strong>.</p>\n' +
    '\n' +
    '<p>للانتقال من أحد أقسام واجهة المستخدم إلى القسم السابق، اضغط على <strong>Shift+Tab</strong>.</p>\n' +
    '\n' +
    '<p>ترتيب علامات <strong>Tab</strong> لأقسام واجهة المستخدم هذه هو:</p>\n' +
    '\n' +
    '<ol>\n' +
    '  <li>شريط القوائم</li>\n' +
    '  <li>كل مجموعة شريط الأدوات</li>\n' +
    '  <li>الشريط الجانبي</li>\n' +
    '  <li>مسار العنصر في التذييل</li>\n' +
    '  <li>زر تبديل عدد الكلمات في التذييل</li>\n' +
    '  <li>رابط إدراج العلامة التجارية في التذييل</li>\n' +
    '  <li>مؤشر تغيير حجم المحرر في التذييل</li>\n' +
    '</ol>\n' +
    '\n' +
    '<p>إذا لم يكن قسم واجهة المستخدم موجودًا، فسيتم تخطيه.</p>\n' +
    '\n' +
    '<p>إذا كان التذييل يحتوي على التركيز على ‏‫التنقل بواسطة لوحة المفاتيح، ولا يوجد شريط جانبي مرئي، فإن الضغط على <strong>Shift+Tab</strong>\n' +
    '  ينقل التركيز إلى مجموعة شريط الأدوات الأولى، وليس الأخيرة.</p>\n' +
    '\n' +
    '<h1>التنقل بين أقسام واجهة المستخدم</h1>\n' +
    '\n' +
    '<p>للانتقال من أحد عناصر واجهة المستخدم إلى العنصر التالي، اضغط على مفتاح <strong>السهم</strong> المناسب.</p>\n' +
    '\n' +
    '<p>مفتاحا السهمين <strong>اليسار‎</strong> و<strong>اليمين‎</strong></p>\n' +
    '\n' +
    '<ul>\n' +
    '  <li>التنقل بين القوائم في شريط القوائم.</li>\n' +
    '  <li>فتح قائمة فرعية في القائمة.</li>\n' +
    '  <li>التنقل بين الأزرار في مجموعة شريط الأدوات.</li>\n' +
    '  <li>التنقل بين العناصر في مسار عنصر التذييل.</li>\n' +
    '</ul>\n' +
    '\n' +
    '<p>مفتاحا السهمين <strong>لأسفل‎</strong> و<strong>لأعلى‎</strong></p>\n' +
    '\n' +
    '<ul>\n' +
    '  <li>التنقل بين عناصر القائمة في القائمة.</li>\n' +
    '  <li>التنقل بين العناصر في قائمة شريط الأدوات المنبثقة.</li>\n' +
    '</ul>\n' +
    '\n' +
    '<p>دورة مفاتيح <strong>الأسهم‎</strong> داخل قسم واجهة المستخدم التي تم التركيز عليها.</p>\n' +
    '\n' +
    '<p>لإغلاق قائمة مفتوحة أو قائمة فرعية مفتوحة أو قائمة منبثقة مفتوحة، اضغط على مفتاح <strong>Esc</strong>.</p>\n' +
    '\n' +
    '<p>إذا كان التركيز الحالي على "الجزء العلوي" من قسم معين لواجهة المستخدم، فإن الضغط على مفتاح <strong>Esc</strong> يؤدي أيضًا إلى الخروج\n' +
    '  من التنقل بواسطة لوحة المفاتيح بالكامل.</p>\n' +
    '\n' +
    '<h1>تنفيذ عنصر قائمة أو زر شريط أدوات</h1>\n' +
    '\n' +
    '<p>عندما يتم تمييز عنصر القائمة المطلوب أو زر شريط الأدوات، اضغط على زر <strong>Return</strong>، أو <strong>Enter</strong>،\n' +
    '  أو <strong>مفتاح المسافة</strong> لتنفيذ العنصر.</p>\n' +
    '\n' +
    '<h1>التنقل في مربعات الحوار غير المبوبة</h1>\n' +
    '\n' +
    '<p>في مربعات الحوار غير المبوبة، يتم التركيز على المكون التفاعلي الأول عند فتح مربع الحوار.</p>\n' +
    '\n' +
    '<p>التنقل بين مكونات الحوار التفاعلي بالضغط على زر <strong>Tab</strong> أو <strong>Shift+Tab</strong>.</p>\n' +
    '\n' +
    '<h1>التنقل في مربعات الحوار المبوبة</h1>\n' +
    '\n' +
    '<p>في مربعات الحوار المبوبة، يتم التركيز على الزر الأول في قائمة علامات التبويب عند فتح مربع الحوار.</p>\n' +
    '\n' +
    '<p>التنقل بين المكونات التفاعلية لعلامة التبويب لمربع الحوار هذه بالضغط على زر <strong>Tab</strong> أو\n' +
    '  <strong>Shift+Tab</strong>.</p>\n' +
    '\n' +
    '<p>التبديل إلى علامة تبويب أخرى لمربع الحوار من خلال التركيز على قائمة علامة التبويب ثم الضغط على زر <strong>السهم</strong> المناسب\n' +
    '  مفتاح للتنقل بين علامات التبويب المتاحة.</p>\n'
);
