tinymce.Resource.add(
  'tinymce.html-i18n.help-keynav.da',
  '<h1>Start tastaturnavigation</h1>\n' +
    '\n' +
    '<dl>\n' +
    '  <dt>Fokuser på menulinjen</dt>\n' +
    '  <dd>Windows eller Linux: Alt+F9</dd>\n' +
    '  <dd>macOS: &#x2325;F9</dd>\n' +
    '  <dt>Fokuser på værktøjslinjen</dt>\n' +
    '  <dd>Windows eller Linux: Alt+F10</dd>\n' +
    '  <dd>macOS: &#x2325;F10</dd>\n' +
    '  <dt>Fokuser på sidefoden</dt>\n' +
    '  <dd>Windows eller Linux: Alt+F11</dd>\n' +
    '  <dd>macOS: &#x2325;F11</dd>\n' +
    '  <dt>Fokuser på meddelelsen</dt>\n' +
    '  <dd>Windows eller Linux: Alt+F12</dd>\n' +
    '  <dd>macOS: &#x2325;F12</dd>\n' +
    '  <dt>Fokuser på kontekstuel værktøjslinje</dt>\n' +
    '  <dd>Windows, Linux eller macOS: Ctrl+F9</dd>\n' +
    '</dl>\n' +
    '\n' +
    '<p>Navigationen starter ved det første UI-element, som fremhæves eller understreges hvad angår det første element i\n' +
    '  sidefodens sti til elementet.</p>\n' +
    '\n' +
    '<h1>Naviger mellem UI-sektioner</h1>\n' +
    '\n' +
    '<p>Gå fra én UI-sektion til den næste ved at trykke på <strong>Tab</strong>.</p>\n' +
    '\n' +
    '<p>Gå fra én UI-sektion til den forrige ved at trykke på <strong>Shift+Tab</strong>.</p>\n' +
    '\n' +
    '<p><strong>Tab</strong>-rækkefølgen af disse UI-sektioner er:</p>\n' +
    '\n' +
    '<ol>\n' +
    '  <li>Menulinje</li>\n' +
    '  <li>Hver værktøjsgruppe</li>\n' +
    '  <li>Sidepanel</li>\n' +
    '  <li>Sti til elementet i sidefoden</li>\n' +
    '  <li>Til/fra-knap for ordoptælling i sidefoden</li>\n' +
    '  <li>Brandinglink i sidefoden</li>\n' +
    '  <li>Tilpasningshåndtag for editor i sidefoden</li>\n' +
    '</ol>\n' +
    '\n' +
    '<p>Hvis en UI-sektion ikke er til stede, springes den over.</p>\n' +
    '\n' +
    '<p>Hvis sidefoden har fokus til tastaturnavigation, og der ikke er noget synligt sidepanel, kan der trykkes på <strong>Shift+Tab</strong>\n' +
    '  for at flytte fokus til den første værktøjsgruppe, ikke den sidste.</p>\n' +
    '\n' +
    '<h1>Naviger inden for UI-sektioner</h1>\n' +
    '\n' +
    '<p>Gå fra ét UI-element til det næste ved at trykke på den relevante <strong>piletast</strong>.</p>\n' +
    '\n' +
    '<p><strong>Venstre</strong> og <strong>højre</strong> piletast</p>\n' +
    '\n' +
    '<ul>\n' +
    '  <li>flytter mellem menuerne i menulinjen.</li>\n' +
    '  <li>åbner en undermenu i en menu.</li>\n' +
    '  <li>flytter mellem knapperne i en værktøjsgruppe.</li>\n' +
    '  <li>flytter mellem elementer i sidefodens sti til elementet.</li>\n' +
    '</ul>\n' +
    '\n' +
    '<p>Pil <strong>ned</strong> og <strong>op</strong></p>\n' +
    '\n' +
    '<ul>\n' +
    '  <li>flytter mellem menupunkterne i en menu.</li>\n' +
    '  <li>flytter mellem punkterne i en genvejsmenu i værktøjslinjen.</li>\n' +
    '</ul>\n' +
    '\n' +
    '<p><strong>Piletasterne</strong> kører rundt inden for UI-sektionen, der fokuseres på.</p>\n' +
    '\n' +
    '<p>For at lukke en åben menu, en åben undermenu eller en åben genvejsmenu trykkes der på <strong>Esc</strong>-tasten.</p>\n' +
    '\n' +
    "<p>Hvis det aktuelle fokus er i 'toppen' af en bestemt UI-sektion, vil tryk på <strong>Esc</strong>-tasten også afslutte\n" +
    '  tastaturnavigationen helt.</p>\n' +
    '\n' +
    '<h1>Udfør et menupunkt eller en værktøjslinjeknap</h1>\n' +
    '\n' +
    '<p>Når det ønskede menupunkt eller den ønskede værktøjslinjeknap er fremhævet, trykkes der på <strong>Retur</strong>, <strong>Enter</strong>\n' +
    '  eller <strong>mellemrumstasten</strong> for at udføre elementet.</p>\n' +
    '\n' +
    '<h1>Naviger i ikke-faneopdelte dialogbokse</h1>\n' +
    '\n' +
    '<p>I ikke-faneopdelte dialogbokse får den første interaktive komponent fokus, når dialogboksen åbnes.</p>\n' +
    '\n' +
    '<p>Naviger mellem interaktive dialogbokskomponenter ved at trykke på <strong>Tab</strong> eller <strong>Shift+Tab</strong>.</p>\n' +
    '\n' +
    '<h1>Naviger i faneopdelte dialogbokse</h1>\n' +
    '\n' +
    '<p>I faneopdelte dialogbokse får den første knap i fanemenuen fokus, når dialogboksen åbnes.</p>\n' +
    '\n' +
    '<p>Naviger mellem interaktive komponenter i denne dialogboksfane ved at trykke på <strong>Tab</strong> eller\n' +
    '  <strong>Shift+Tab</strong>.</p>\n' +
    '\n' +
    '<p>Skift til en anden dialogboksfane ved at fokusere på fanemenuen og derefter trykke på den relevante <strong>piletast</strong>\n' +
    '  for at køre igennem de tilgængelige faner.</p>\n'
);
