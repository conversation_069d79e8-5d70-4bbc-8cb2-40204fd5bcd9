tinymce.Resource.add(
  'tinymce.html-i18n.help-keynav.ro',
  '<h1>Începeți navigarea de la tastatură</h1>\n' +
    '\n' +
    '<dl>\n' +
    '  <dt>Focalizare pe bara de meniu</dt>\n' +
    '  <dd>Windows sau Linux: Alt+F9</dd>\n' +
    '  <dd>macOS: &#x2325;F9</dd>\n' +
    '  <dt>Focalizare pe bara de instrumente</dt>\n' +
    '  <dd>Windows sau Linux: Alt+F10</dd>\n' +
    '  <dd>macOS: &#x2325;F10</dd>\n' +
    '  <dt>Focalizare pe subsol</dt>\n' +
    '  <dd>Windows sau Linux: Alt+F11</dd>\n' +
    '  <dd>macOS: &#x2325;F11</dd>\n' +
    '  <dt>Focalizare pe notificare</dt>\n' +
    '  <dd>Windows sau Linux: Alt+F12</dd>\n' +
    '  <dd>macOS: &#x2325;F12</dd>\n' +
    '  <dt>Focalizare pe o bară de instrumente contextuală</dt>\n' +
    '  <dd>Windows, Linux sau macOS: Ctrl+F9</dd>\n' +
    '</dl>\n' +
    '\n' +
    '<p>Navigarea va începe de la primul element al interfeței cu utilizatorul, care va fi evidențiat sau subliniat în cazul primului element din\n' +
    '  calea elementului Subsol.</p>\n' +
    '\n' +
    '<h1>Navigați între secțiunile interfeței cu utilizatorul</h1>\n' +
    '\n' +
    '<p>Pentru a trece de la o secțiune a interfeței cu utilizatorul la alta, apăsați <strong>Tab</strong>.</p>\n' +
    '\n' +
    '<p>Pentru a trece de la o secțiune a interfeței cu utilizatorul la cea anterioară, apăsați <strong>Shift+Tab</strong>.</p>\n' +
    '\n' +
    '<p>Ordinea cu <strong>Tab</strong> a acestor secțiuni ale interfeței cu utilizatorul este următoarea:</p>\n' +
    '\n' +
    '<ol>\n' +
    '  <li>Bara de meniu</li>\n' +
    '  <li>Fiecare grup de bare de instrumente</li>\n' +
    '  <li>Bara laterală</li>\n' +
    '  <li>Calea elementului în subsol</li>\n' +
    '  <li>Buton de comutare a numărului de cuvinte în subsol</li>\n' +
    '  <li>Link de branding în subsol</li>\n' +
    '  <li>Mâner de redimensionare a editorului în subsol</li>\n' +
    '</ol>\n' +
    '\n' +
    '<p>În cazul în care o secțiune a interfeței cu utilizatorul nu este prezentă, aceasta este omisă.</p>\n' +
    '\n' +
    '<p>În cazul în care subsolul are focalizarea navigației asupra tastaturii și nu există o bară laterală vizibilă, apăsarea butonului <strong>Shift+Tab</strong>\n' +
    '  mută focalizarea pe primul grup de bare de instrumente, nu pe ultimul.</p>\n' +
    '\n' +
    '<h1>Navigați în secțiunile interfeței cu utilizatorul</h1>\n' +
    '\n' +
    '<p>Pentru a trece de la un element de interfață cu utilizatorul la următorul, apăsați tasta cu <strong>săgeata</strong> corespunzătoare.</p>\n' +
    '\n' +
    '<p>Tastele cu săgeți către <strong>stânga</strong> și <strong>dreapta</strong></p>\n' +
    '\n' +
    '<ul>\n' +
    '  <li>navighează între meniurile din bara de meniuri.</li>\n' +
    '  <li>deschid un sub-meniu dintr-un meniu.</li>\n' +
    '  <li>navighează între butoanele dintr-un grup de bare de instrumente.</li>\n' +
    '  <li>navighează între elementele din calea elementelor subsolului.</li>\n' +
    '</ul>\n' +
    '\n' +
    '<p>Tastele cu săgeți în <strong>sus</strong> și în <strong>jos</strong></p>\n' +
    '\n' +
    '<ul>\n' +
    '  <li>navighează între elementele de meniu dintr-un meniu.</li>\n' +
    '  <li>navighează între elementele unui meniu pop-up din bara de instrumente.</li>\n' +
    '</ul>\n' +
    '\n' +
    '<p>Tastele cu <strong>săgeți</strong> navighează în cadrul secțiunii interfeței cu utilizatorul asupra căreia se focalizează.</p>\n' +
    '\n' +
    '<p>Pentru a închide un meniu deschis, un sub-meniu deschis sau un meniu pop-up deschis, apăsați tasta <strong>Esc</strong>.</p>\n' +
    '\n' +
    '<p>Dacă focalizarea curentă este asupra „părții superioare” a unei anumite secțiuni a interfeței cu utilizatorul, prin apăsarea tastei <strong>Esc</strong> se iese, de asemenea,\n' +
    '  în întregime din navigarea de la tastatură.</p>\n' +
    '\n' +
    '<h1>Executarea unui element de meniu sau a unui buton din bara de instrumente</h1>\n' +
    '\n' +
    '<p>Atunci când elementul de meniu dorit sau butonul dorit din bara de instrumente este evidențiat, apăsați <strong>Return</strong>, <strong>Enter</strong>,\n' +
    '  sau <strong>bara de spațiu</strong> pentru a executa elementul.</p>\n' +
    '\n' +
    '<h1>Navigarea de dialoguri fără file</h1>\n' +
    '\n' +
    '<p>În dialogurile fără file, prima componentă interactivă beneficiază de focalizare la deschiderea dialogului.</p>\n' +
    '\n' +
    '<p>Navigați între componentele dialogului interactiv apăsând <strong>Tab</strong> sau <strong>Shift+Tab</strong>.</p>\n' +
    '\n' +
    '<h1>Navigarea de dialoguri cu file</h1>\n' +
    '\n' +
    '<p>În dialogurile cu file, primul buton din meniul cu file beneficiază de focalizare la deschiderea dialogului.</p>\n' +
    '\n' +
    '<p>Navigați între componentele interactive ale acestei file de dialog apăsând <strong>Tab</strong> sau\n' +
    '  <strong>Shift+Tab</strong>.</p>\n' +
    '\n' +
    '<p>Treceți la o altă filă de dialog focalizând asupra meniului cu file și apoi apăsând <strong>săgeata</strong> corespunzătoare\n' +
    '  pentru a parcurge filele disponibile.</p>\n'
);
