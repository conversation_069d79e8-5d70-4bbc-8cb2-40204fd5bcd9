# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Taiwan Casino Game (TWCG) backend management system frontend built with React, TypeScript, Vite, and Ant Design. It provides administrative interfaces for managing players, transactions, games, operations, and system settings.

## Key Technologies

- **React 18** with TypeScript
- **Vite** as build tool and dev server
- **Ant Design** for UI components
- **Tailwind CSS v4** for styling (with SCSS for Ant Design overrides)
- **React Router v7** for navigation
- **TanStack Query** for data fetching
- **Zustand** for state management
- **i18next** for internationalization

## Common Development Commands

```bash
# Start development server (runs on port 5174)
npm run dev

# Build for production
npm run build

# Build for specific environments
npm run build:develop
npm run build:uat

# Lint code
npm run lint

# Preview production build
npm run preview

# Auto-translate i18n files
npm run translate
```

## Project Architecture

### File-Based Routing
- Uses `vite-plugin-pages` for automatic route generation
- Routes are created from `src/pages/**/index.tsx` files
- Route permissions are managed in `src/router/defaultRoutes.ts`

### Permission System
- Role-based access control with permissions defined in `src/enums/permissions.ts`
- Each route requires specific permissions mapped in `defaultRoutes.ts`
- Permission validation handled by `PermissionRoute` component

### API Architecture
- Centralized API service in `src/api/services/index.ts`
- Automatic camelCase/snake_case conversion for requests/responses
- Bearer token authentication with automatic 401 handling
- API modules organized by domain (auth, game, player, transaction, etc.)

### State Management
- **Zustand stores**: User state, permissions, bookmarks
- **TanStack Query**: Server state and caching
- **React Context**: Ant Design configuration, permissions

### Component Structure
- **Reusable components**: Prefixed with 'R' (RButton, RTable, etc.)
- **Page components**: Located in `src/pages/` with domain-specific folders
- **Layout components**: BaseLayout, ContentLayout, TableSearchLayout
- **Custom hooks**: Domain-specific logic in `src/hooks/`

### Styling System
- **Tailwind CSS**: Primary styling framework
- **SCSS**: Used for Ant Design customizations in `src/assets/styles/antd.scss`
- **CSS Variables**: Global variables in `src/assets/styles/tailwind.css`

### Development Environment
- Environment variables in `.env` files (`.env.runtime` for local overrides)
- Auto-import for React hooks and common imports
- SVG imports as React components using `?react` suffix

## Key Development Patterns

### Route Management
- New pages auto-generate routes when placed in `src/pages/`
- Add permission mappings to `defaultRoutes.ts` for new routes
- Use `VITE_TEST_PATH` in `.env.runtime` for development access

### API Integration
- Import API functions from domain-specific modules in `src/api/`
- Use TanStack Query hooks for data fetching
- Handle loading states and error boundaries consistently

### Form Handling
- Use Ant Design forms with custom R-prefixed components
- Form validation with built-in Ant Design rules
- Multi-language support with i18next

### Internationalization
- Add new keys to `src/i18n/locales/zh_TW.json`
- Run `npm run translate` to auto-generate other language files
- Use `useTranslation` hook for text rendering

## Git Workflow

### Branch Naming
Format: `<type>/TWCGCORE-<issueNo>-<feature-name>`
Example: `feat/TWCGCORE-1234-add-login-form`

### Commit Messages
Format: `<type>(#TWCGCORE-<issueNo>): <subject>`
Example: `feat(#TWCGCORE-1234): implement login form UI`

## Testing and Quality

- ESLint configuration with React and TypeScript rules
- Prettier for code formatting
- Husky pre-commit hooks with lint-staged
- No specific test framework configured - check with team for testing approach

## API Integration

### Available API Endpoints
The system integrates with a comprehensive backend API at `https://twcg-dev-api.light-morning.com/api/system/`. Key endpoint categories include:

- **Authentication**: Login, OTP, user info (`/auth/*`)
- **Admin Management**: Admin CRUD, roles, permissions (`/admin/*`, `/role/*`)
- **Player Management**: Player CRUD, balance adjustments, VIP levels (`/player/*`)
- **Game Management**: Games, providers, categories, maintenance (`/game/*`)
- **Game Orders**: Slot, fish, table, arcade game orders (`/gameorder/*`)
- **Transactions**: Top-up orders, gift management, funding records (`/topup/*`, `/gift/*`, `/funding/*`)
- **Content Management**: Carousels, announcements, front content (`/caro/*`, `/content/*`)
- **Platform Settings**: Platform configuration, sounds, settings (`/platform/*`)
- **Reporting**: Export logs, operation logs (`/exportlog/*`, `/log/*`)

### API Conventions
- All endpoints use Bearer token authentication
- Request/response data automatically converts between camelCase (frontend) and snake_case (backend)
- Timestamps are in milliseconds
- Pagination uses `page` and `limit` parameters
- Sorting uses `order_by` and `sort_by` parameters
- File uploads use multipart/form-data

### Key API Patterns
- List endpoints support filtering, sorting, and pagination
- Export endpoints return export record IDs for download tracking
- Status endpoints typically use 0=disabled, 1=enabled
- Time filtering supports `time_start`, `time_end`, and `time_filter` parameters
- Test account filtering available via `exclude_test` parameter

## Important Notes

- Main branch for PRs: `develop`
- Development server runs on port 5174
- TinyMCE editor included for rich text editing
- Drag-and-drop functionality available via @dnd-kit
- QR code generation supported via qrcode library
- API base URL configured via `VITE_API_BASE_URL` environment variable