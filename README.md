# twcg-backend

這是一個後台管理系統的前端專案，使用 React、Vite、TypeScript 和 Ant Design 構建。

## ✨ 功能特色

- **路由管理**: 使用 [`vite-plugin-pages`](https://github.com/hannoeru/vite-plugin-pages) 實現基於檔案系統的路由。
- **自動引入**: 透過 [`unplugin-auto-import`](https://github.com/antfu/unplugin-auto-import) 自動載入常用的 React Hooks。
- **UI 框架**: 整合 [Ant Design](https://ant.design/) 作為主要的 UI 組件庫。
- **樣式系統**: 使用 [Tailwind CSS v4](https://tailwindcss.com/) 進行快速樣式開發，並搭配 SCSS 處理 Ant Design 的樣式覆蓋。

## 🚀 開始使用

### 環境需求

- [Node.js](https://nodejs.org/) (建議版本 >= v20.11.0)
- [npm](https://www.npmjs.com/)

### 安裝依賴

```bash
npm install
```

### 啟動開發環境

```bash
npm run dev
```

### 複製.env.runtime

可以用來設定自己的runtime環境

> .env.runtime目前被gitignore

```
cp .env /env/.env.runtime
```

專案將會在 `http://localhost:5174` (或其他可用 port) 啟動。

## 🛠️ 開發指南

### 🔖 Git Branch Naming Convention

- **Format:**
  <type>[issueNo]/[feature-name]

- **Example:**
  feat/TWCGCORE-1234-add-login-form
- **Rules:**
- Use lowercase for `<type>` (e.g., `feat`, `fix`, etc.)
- The Redmine issue number should be in uppercase (e.g., `TWCGCORE-1234`)
- Feature name should be written in kebab-case (use hyphens to separate words)
- No spaces or special characters

### 📝 Commit Message Format

- **Format:**
  <type>(#issueNo): <subject>

- **Example:**
  feat(#TWCGCORE-1234): implement login form UI

- **Explanation:**
- `<type>` describes the nature of the change (see list below)
- `#issueNo` links the commit to a Redmine issue
- `<subject>` is a short, imperative-style summary of the change

#### ✅ Supported Commit Types

| Type       | Description                                           |
| ---------- | ----------------------------------------------------- |
| `feat`     | A new feature                                         |
| `fix`      | A bug fix                                             |
| `docs`     | Documentation-only changes                            |
| `style`    | Code style changes (formatting, etc.)                 |
| `refactor` | Code changes that neither fix a bug nor add a feature |
| `test`     | Adding or updating tests                              |
| `chore`    | Maintenance tasks (e.g., build tools, CI)             |

### Best Practices for Commit Messages

- Use the **present tense** (e.g., “add” not “added”)
- Keep the subject line **under 50 characters**
- **Do not** end the subject line with a period
- Separate body (if needed) from subject line with a blank line

### 路由與權限

- **新增路由**: 在 `src/pages/` 目錄下新增對應的 `index.tsx` 檔案或資料夾即可自動生成路由。
- **權限處理**: 像後端取得頁面權限，在`src/enums/permissions.ts`新增權限enum(將/me中的permissions中做name的對照)，之後在`src/router/defaultRoutes.ts`進行權限跟頁面的對照
- **開發用path** : 1. 可以請後端新增permission, 2. 也可以在`.env.runtime` 調整`VITE_TEST_PATH`

### 樣式修改

1.  **全局 CSS 變數**: 若要新增或修改全局的 CSS 變數，請至 `src/assets/styles/tailwind.css`。
2.  **Ant Design 樣式覆蓋**: 若要覆蓋 Ant Design 的組件樣式，請在 `src/assets/styles/antd.scss` 中進行修改。

> **注意**: 目前 Tailwind CSS v4 無法與 SCSS 一起在 PostCSS 中處理，因此在 `src/main.tsx` 中是分開引入的。

## 📜 可用腳本

- `npm run dev`: 啟動開發伺服器。
- `npm run build`: 編譯並打包生產環境的檔案。
- `npm run build:develop`: 使用 `develop` 環境變數進行打包。
- `npm run build:uat`: 使用 `uat` 環境變數進行打包。
- `npm run lint`: 檢查程式碼風格。
- `npm run preview`: 在本地預覽生產環境的打包結果。

## Use auto translate

- Put the new translation key in the `zh_TW.json` file.
- Run `npm run translate` to generate the new translation file for other languages.

## 組件相關注意事項

### 活動狀態枚舉
```typescript
export enum ActivityStatus {
  DRAFT = 1,              // 草稿
  UPCOMING = 2,           // 即將開始
  IN_PROGRESS = 3,        // 進行中
  WITHDRAW_EXPIRED = 4,   // 已過期下架
  WITHDRAW_MANUAL = 5,    // 手動下架
  SCHEDULED = 6,          // 已排定
  ENDED = 7               // 已結束
}
```
擴展新的狀態

如果需要添加新的狀態顏色配置：

1. 在 `ActivityStatus` 枚舉中添加新的狀態：
```typescript
export enum ActivityStatus {
  // ... 現有狀態
  NEW_STATUS = 8
}
```

2. 在 `ACTIVITY_STATUS_COLORS` 中添加對應的顏色配置：
```typescript
export const ACTIVITY_STATUS_COLORS = {
  // ... 現有配置
  [ActivityStatus.NEW_STATUS]: {
    color: 'var(--tag-bg-info)',
    textColor: 'var(--color-info)'
  }
};
```

3. 後端回傳對應的 ID 和 label 即可自動應用配置。

