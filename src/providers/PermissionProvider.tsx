import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

import { usePermissionStore } from '@/store/permissionStore';
import { useUserStore } from '@/store/userStore';

const isDev = import.meta.env.DEV;
const testPath = isDev ? import.meta.env.VITE_TEST_PATH : '';

export function PermissionProvider({ children }: { children: React.ReactNode }) {
  const { userInfo, fetchUserInfo, isLoading } = useUserStore();
  const { hasRoutePermission } = usePermissionStore();
  const navigate = useNavigate();

  useEffect(() => {
    if (userInfo.token && (!userInfo.permissions || userInfo.permissions.length === 0)) {
      fetchUserInfo();
      //   console.log('userInfo', userInfo.permissions);
    }
  }, [userInfo.token, userInfo.permissions, fetchUserInfo]);

  useEffect(() => {
    // 只在加載完成且有 token 時檢查權限
    if (!isLoading && userInfo.token && userInfo.permissions && userInfo.permissions.length > 0) {
      const currentPath = window.location.pathname;

      // 跳過特殊頁面的權限檢查
      if (
        currentPath === '/403' ||
        currentPath === '/404' ||
        currentPath === '/500' ||
        currentPath === testPath
      ) {
        return;
      }

      const hasPermission = hasRoutePermission(currentPath);
      // console.log(
      //   'PermissionProvider - checking path:',
      //   currentPath,
      //   'hasPermission:',
      //   hasPermission
      // );

      if (!hasPermission) {
        navigate('/403');
      }
    }
  }, [isLoading, hasRoutePermission, navigate, userInfo.token, userInfo.permissions]);

  return <>{children}</>;
}
