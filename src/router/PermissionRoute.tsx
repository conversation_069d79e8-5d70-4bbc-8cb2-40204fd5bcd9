import { Navigate, Outlet, useLocation } from 'react-router-dom';

import { usePermissionStore } from '@/store/permissionStore';

const PermissionRoute = () => {
  const { hasRoutePermission } = usePermissionStore();
  const location = useLocation();

  if (!hasRoutePermission(location.pathname)) {
    return <Navigate to="/403" replace />;
  }

  return <Outlet />;
};

export default PermissionRoute;
