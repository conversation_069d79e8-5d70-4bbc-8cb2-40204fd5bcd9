import clsx from 'clsx';
import { ReactNode } from 'react';

interface TableSearchLayoutProps {
  searchFields?: ReactNode;
  children: ReactNode;
  searchFieldsContainerClassName?: string;
  hasTab?: boolean;
}

const TableSearchLayout = ({
  searchFields,
  children,
  searchFieldsContainerClassName,
  hasTab = false
}: TableSearchLayoutProps) => {
  const baseSearchFieldsClassName = 'w-full px-4 py-8 bg-white rounded-none shadow-content';

  useEffect(() => {
    if (hasTab) {
      const baseLayoutHeader = document.getElementById('base-layout-header');
      if (baseLayoutHeader) {
        baseLayoutHeader.classList.remove('shadow-content');
      }
    } else {
      const baseLayoutHeader = document.getElementById('base-layout-header');
      if (baseLayoutHeader) {
        baseLayoutHeader.classList.add('shadow-content');
      }
    }
  }, [hasTab]);

  return (
    <div className="flex flex-col gap-4 p-3 h-full">
      {searchFields && (
        <div className={clsx(baseSearchFieldsClassName, searchFieldsContainerClassName)}>
          {searchFields}
        </div>
      )}
      <div className="flex flex-col bg-bg-primary">{children}</div>
    </div>
  );
};

export default TableSearchLayout;
