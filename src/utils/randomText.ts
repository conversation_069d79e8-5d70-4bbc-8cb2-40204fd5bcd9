export const createRandomString = (length = 10) => {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  const randomPassword = Array.from(
    { length },
    () => characters[Math.floor(Math.random() * characters.length)]
  ).join('');
  return randomPassword;
};

export const generatePasswordWithPattern = (letterCount: number, numberCount: number) => {
  const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
  const numbers = '0123456789';

  // Create arrays of specified length for letters and numbers
  const letterArray = Array.from(
    { length: letterCount },
    () => letters[Math.floor(Math.random() * letters.length)]
  );
  const numberArray = Array.from(
    { length: numberCount },
    () => numbers[Math.floor(Math.random() * numbers.length)]
  );

  // Combine arrays and shuffle
  const combined = [...letterArray, ...numberArray];
  for (let i = combined.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [combined[i], combined[j]] = [combined[j], combined[i]];
  }

  return combined.join('');
};
