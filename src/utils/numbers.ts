/**
 * 格式化數字
 * @param input
 * @param toFixed - 小數點後幾位，預設為 0
 * @returns
 * @e.g. const n = 1234567890 => "1,234,567,890"
 * @e.g. const n = 1234567890.123, toFixed = 2 => "1,234,567,890.12"
 */
export const numberFormat = (input: number, toFixed: number = 0) => {
  if (input === undefined || input === null) return '-';
  const inputNumber = Number(input);
  if (isNaN(inputNumber)) return input;
  const fixedNumber = inputNumber.toFixed(toFixed);
  const parts = fixedNumber.split('.');
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  return parts.join('.');
};
