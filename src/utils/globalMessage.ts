import { message } from 'antd';
import React from 'react';

import InfoIcon from '@/assets/img/icon/info.svg?react';

const InfoIconComponent: React.FC<{ type: 'success' | 'error' | 'info' }> = ({ type }) => {
  const colorMap = {
    success: 'var(--color-success)',
    error: 'var(--color-warning)',
    info: 'var(--color-info)'
  };
  return React.createElement(InfoIcon, {
    fill: colorMap[type],
    width: 16,
    height: 16,
    className: 'mr-2'
  });
};

export const globalMessage = ({
  content,
  type = 'error',
  duration,
  onClose
}: {
  content: string;
  type: 'success' | 'error' | 'info';
  duration?: number;
  onClose?: () => void;
}) => {
  return message.open({
    icon: React.createElement(InfoIconComponent, { type }),
    content,
    type,
    duration,
    onClose
  });
};
