import i18n from '@/i18n';

const isNumber = (value: string | number) => {
  return !isNaN(Number(value));
};

const checkDigit = ({ value, digits }: { value: string | number; digits?: number }) => {
  const numberValue = Number(value);
  if (!isNumber(numberValue)) {
    return false;
  }

  if (digits) {
    const valueString = value.toString();
    const integerPart = valueString.split('.')[0];
    if (integerPart.length > digits) {
      return false;
    }
  }

  return true;
};

const checkDecimalPlaces = ({
  value,
  decimalPlaces
}: {
  value: string | number;
  decimalPlaces: number;
}) => {
  const valueString = value.toString();
  const decimalPart = valueString.split('.')[1] || '';
  if (decimalPart.length > decimalPlaces) {
    return false;
  }
  return true;
};

export const digitValidator = ({
  value,
  digits,
  decimalPlaces
}: {
  value: string;
  digits: number;
  decimalPlaces?: number;
}) => {
  if (!isNumber(value)) {
    return Promise.reject(i18n.t('error_input_number'));
  }
  if (!checkDigit({ value, digits })) {
    return Promise.reject(new Error(i18n.t('error_input_digit', { digits, decimalPlaces })));
  }
  if (decimalPlaces && !checkDecimalPlaces({ value, decimalPlaces })) {
    return Promise.reject(new Error(i18n.t('error_input_decimal_places', { decimalPlaces })));
  }

  return Promise.resolve();
};
