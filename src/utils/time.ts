import dayjs from 'dayjs';

export const formatTime = (time: number | string, format = 'YYYY-MM-DD HH:mm:ss') => {
  if (typeof time === 'string') {
    time = Number(time);
  }
  return dayjs(time).format(format);
};

export const formatTimeToMinute = (time: number | string, format = 'YYYY-MM-DD HH:mm') => {
  if (typeof time === 'string') {
    time = Number(time);
  }
  return dayjs(time).format(format);
};
