export function snakeToCamel(snake: string): string {
  return snake.replace(/(_\w)/g, (matches) => matches[1].toUpperCase());
}

function camelToSnake(camel: string): string {
  return camel.replace(/([A-Z])/g, (matches) => `_${matches.toLowerCase()}`);
}
const originKeys = ['UID'];

type PlainObject = { [key: string]: unknown };

function convertKeys<T>(obj: T, convertFunc: (key: string) => string): T {
  if (Array.isArray(obj)) {
    return obj.map((item) => convertKeys(item, convertFunc)) as unknown as T;
  } else if (obj instanceof FormData) {
    const newFormData = new FormData();

    obj.forEach((value, key) => {
      const convertedKey = convertFunc(key);
      newFormData.append(convertedKey, value);
    });

    return newFormData as T;
  } else if (obj !== null && typeof obj === 'object') {
    const newObj: PlainObject = {};
    Object.keys(obj).forEach((key) => {
      const convertedKey = originKeys.includes(key) ? key : convertFunc(key);
      newObj[convertedKey] = convertKeys((obj as PlainObject)[key], convertFunc);
    });
    return newObj as T;
  } else {
    return obj;
  }
}

export function convertToCamel<T>(snakeObject: T) {
  return convertKeys(snakeObject, snakeToCamel);
}

export function convertToSnake<T>(snakeObject: T) {
  return convertKeys(snakeObject, camelToSnake);
}

export function cleanSearchParams<T extends object>(values: T) {
  return Object.entries(values).reduce((acc, [key, value]) => {
    if (value !== undefined && value !== '' && value !== 'all') {
      // @ts-ignore
      acc[key as keyof T] = value;
    }
    return acc;
  }, {} as T);
};
