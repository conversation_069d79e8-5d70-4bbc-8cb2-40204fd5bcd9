import {
  InternalLetterReceiver,
  InternalLetterRecord,
  InternalLetterRecordContentDetail,
  InternalLetterRecordSearchParams,
  MailCategory,
  MailTemplate,
  MailTemplateSearchParams,
  MailTemplateWithContentMapping,
  MailType,
  MailVariable
} from '@/types/mail';

import apiRequest, { IResponseDataPage } from './services';

// Get internal letter records
export const getInternalLetterRecords = async (params: InternalLetterRecordSearchParams) => {
  const data = {
    ...params
  };

  // Convert time parameters to match API expectations
  if (data.timeStart) {
    data.timeStart = Number(data.timeStart);
  }
  if (data.timeEnd) {
    data.timeEnd = Number(data.timeEnd);
  }

  return apiRequest().get<IResponseDataPage<InternalLetterRecord>>('/system/mail/record/list', {
    params: data
  });
};

// Get mail categories
export const getMailCategories = () => {
  return apiRequest().get<MailCategory[]>('/system/mail/category');
};

// Get mail types
export const getMailTypes = () => {
  return apiRequest().get<MailType[]>('/system/mail/type');
};

// Export mail records
export const triggerExportInternalLetterRecords = async (
  params: Omit<InternalLetterRecordSearchParams, 'page' | 'limit'>
) => {
  const data = {
    ...params
  };

  // Convert time parameters to match API expectations
  if (data.timeStart) {
    data.timeStart = Number(data.timeStart);
  }
  if (data.timeEnd) {
    data.timeEnd = Number(data.timeEnd);
  }

  return apiRequest().get<{ id: number }>('/system/mail/record/export', {
    params: data
  });
};

// Get mail record details
export const getMailRecordDetail = (id: string) => {
  return apiRequest().get<InternalLetterRecordContentDetail>('/system/mail/record/details', {
    params: { id }
  });
};

// Batch read mail records
export const batchReadMailRecords = (id: string) => {
  return apiRequest().get<InternalLetterReceiver[]>('/system/mail/record/list/read/batch', {
    params: { id }
  });
};

export const withdrawMailRecord = (id: string) => {
  return apiRequest().put('/system/mail/record/withdraw', { id, note: { zh_tw: '' } });
};

export const sendMailRecord = (templateId: number, account: string) => {
  return apiRequest().post('/system/mail/record/create', { templateId, account });
};

export const sendBatchMailRecord = (templateId: number, accounts: string[]) => {
  return apiRequest().post('/system/mail/record/create/batch', { templateId, accounts });
};

// Mail Template API functions
export const getMailTemplateList = async (params?: MailTemplateSearchParams) => {
  return apiRequest().get<IResponseDataPage<MailTemplate>>('/system/mail/template/list', {
    params
  });
};

export const getMailTemplateDetails = (id: number) => {
  return apiRequest().get<MailTemplateWithContentMapping>('/system/mail/template/details', {
    params: { id }
  });
};

type MailTemplateFormValue = Pick<
  MailTemplate,
  'title' | 'content' | 'description' | 'category' | 'type'
>;

export const createMailTemplate = (params: MailTemplateFormValue) => {
  return apiRequest().post<MailTemplate>('/system/mail/template/create', params);
};

export const updateMailTemplate = (params: MailTemplateFormValue & Pick<MailTemplate, 'id'>) => {
  return apiRequest().put<MailTemplate>('/system/mail/template/edit', params);
};

export const deleteMailTemplate = (params: { id: number }) => {
  return apiRequest().delete('/system/mail/template/del', params);
};

export const updateMailTemplateOrder = (params: { orders: number[] }) => {
  return apiRequest().put<MailTemplate>('/system/mail/template/edit/order', params);
};

export const getMailVariables = () => {
  return apiRequest().get<MailVariable[]>('/system/mail/variables');
};
