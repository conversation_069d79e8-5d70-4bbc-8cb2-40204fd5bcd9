import { TimeSelect } from '@/enums/timeSelect';
import {
  TopupCategory,
  TopupChannel,
  TopupIncompletedOrder,
  TopupItem,
  TopupOrderHistory,
  TopupPopular,
  TopupSupplier
} from '@/types/topup';

import apiRequest, { IResponse, IResponseDataPage, IResponseDataPageStatistic } from './services';

export const getTopupCategory = () => {
  return apiRequest().get<TopupCategory[]>('system/topup/category/list');
};

export const editTopupCategory = (params: { id: number; zhTwTitle: string; zhTwPhoto: File }) => {
  const formData = new FormData();
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      if (value instanceof File) {
        formData.append(key, value);
      } else {
        formData.append(key, value.toString());
      }
    }
  });
  return apiRequest().post<TopupCategory>('system/topup/category/edit', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

export const updateTopupCategoryOrder = (params: { orders: number[] }) => {
  return apiRequest().put<TopupCategory>('system/topup/category/edit/order', params);
};

export const getTopupChannel = (params: { categoryKey: string | null }) => {
  // 空值取得全部
  return apiRequest().get<TopupChannel[]>('system/topup/channel/list', { params });
};

export const editTopupChannelInfo = (params: { id: number; zhTwContent: string }) => {
  return apiRequest().put<TopupChannel>('system/topup/channel/edit/content', params);
};

export const editTopupChannel = (params: {
  id: number;
  zhTwTitle: string;
  zhTwPhoto: string;
  tags: number[];
}) => {
  return apiRequest().put<TopupChannel>('system/topup/channel/edit', params);
};

export const editTopupChannelPhoto = (params: { id: number; zhTwPhoto: File }) => {
  const formData = new FormData();
  formData.append('id', params.id.toString());
  formData.append('zhTwPhoto', params.zhTwPhoto);
  return apiRequest().post<{ id: number; zhTwPhoto: string }>(
    'system/topup/channel/upload',
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }
  );
};

export const editTopupChannelOrder = (params: { orders: number[] }) => {
  return apiRequest().put<TopupChannel>('system/topup/channel/edit/order', params);
};

export const getTopupItem = (params: { channelKey: string }) => {
  return apiRequest().get<TopupItem[]>('system/topup/item/list', { params });
};

export const editTopupItem = (params: {
  id: number;
  buyPrice: number;
  amount: number;
  status: 1 | 0;
}) => {
  return apiRequest().put<TopupItem>('system/topup/item/edit', params);
};

export const editTopupItemOrder = (params: { orders: number[] }) => {
  return apiRequest().put<TopupItem>('system/topup/item/edit/order', params);
};

export const createTopupItem = (params: {
  categoryKey: string;
  channelKey: string;
  buyPrice: number;
  amount: number;
  status: 1 | 0;
}) => {
  return apiRequest().post<TopupItem>('system/topup/item/create', params);
};

// 供應商設定
export const getTopupSupplier = () => {
  return apiRequest().get<TopupSupplier[]>('system/topup/platform/list');
};
// 基礎設定
export const editTopupSetting = (params: {
  topupVipLevel: number;
  topupFrozenRemainTimeLimit: number;
}) => {
  return apiRequest().put('system/topup/edit/vip', params);
};

// 熱門充值組合參數type
export type TopupPopularCreateParams = {
  zhTwTitle: string;
  categoryKey: string;
  channelKey: string;
  itemId: number;
  highlight: number;
  zhTwPhoto?: File | string; // 組合圖
  zhTwContent?: string; // 標籤
};

export type TopupPopularEditParams = TopupPopularCreateParams & {
  id: number;
};

// 熱門充值組合
export const getTopupPopular = () => {
  return apiRequest().get<TopupPopular[]>('system/topup/popularitem/list');
};

// 新增熱門充值組合
export const createTopupPopular = (params: TopupPopularCreateParams) => {
  const formData = new FormData();

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      if (value instanceof File) {
        formData.append(key, value);
      } else {
        formData.append(key, value.toString());
      }
    }
  });

  return apiRequest().post<TopupPopular>('system/topup/popularitem/create', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

// 編輯熱門充值組合
export const editTopupPopular = (params: TopupPopularEditParams) => {
  const formData = new FormData();
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      if (value instanceof File) {
        formData.append(key, value);
      } else {
        formData.append(key, value.toString());
      }
    }
  });
  return apiRequest().post<TopupPopular>('system/topup/popularitem/edit', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

export const updateTopupPopularOrder = (params: { orders: number[] }) => {
  return apiRequest().put<TopupPopular>('system/topup/popularitem/edit/order', params);
};

export const deleteTopupPopular = (params: { id: number }) => {
  return apiRequest().delete<TopupPopular>('system/topup/popularitem/del', params);
};

export type TopupOrderHistoryStatic = {
  accountCount: number;
  orderCount: number;
  sumTtlAmount: number;
  sumTtlPoint: number;
};

type TopupOrderHistoryData = {
  data: TopupOrderHistory[];
  statistic: TopupOrderHistoryStatic;
  total: number;
};

export type TopupOrderHistoryResponse = IResponse<TopupOrderHistoryData>;

// 取得已完成充值列表清單
export const getTopupOrderHistory = (
  params: {
    timeFilter?: TimeSelect;
    start?: number;
    end?: number;
    account?: string;
    name?: string;
    orderId?: string;
    refAccount?: string;
    agentAccount?: string;
    platformKey?: string;
    categoryKey?: string;
    channelKey?: string;
    tagId?: number[];
    status?: 1 | 0;
    excludeTest?: 1 | 0;
    page: number;
    limit: number;
  } & {
    timeStart?: number;
    timeEnd?: number;
  }
) => {
  const data = {
    ...params
  };
  if (data.start) {
    data.timeStart = Number(data.start);
  }
  if (data.end) {
    data.timeEnd = Number(data.end);
  }
  return apiRequest().get<IResponseDataPageStatistic<TopupOrderHistory[], TopupOrderHistoryStatic>>(
    '/system/topup/order/completed',
    {
      params: data
    }
  );
};

// 取得未完成充值列表清單
export const getTopupIncompletedOrder = (
  params: {
    // start?: number;
    // end?: number;
    account?: string;
    name?: string;
    orderId?: string;
    refAccount?: string;
    agentAccount?: string;
    platformKey?: string;
    categoryKey?: string;
    channelKey?: string;
    tagId?: number[];
    excludeTest?: 1 | 0;
    page: number;
    limit: number;
  }
  // & {
  //   timeStart?: number;
  //   timeEnd?: number;
  // }
) => {
  const data = {
    ...params
  };
  // if (data.start) {
  //   data.timeStart = Number(data.start);
  // }
  // if (data.end) {
  //   data.timeEnd = Number(data.end);
  // }
  return apiRequest().get<IResponseDataPage<TopupIncompletedOrder>>(
    '/system/topup/order/incompleted',
    {
      params: data
    }
  );
};

// 更新充值列表備註(已完成充值訂單頁面)
export const updateTopupOrderNote = (params: { id: number; note: { zhTw: string } }) => {
  return apiRequest().put('system/topup/order/note', params);
};

// 導出紀錄
export const getTopupOrderHistoryExport = (
  params: {
    timeFilter?: TimeSelect;
    start?: number;
    end?: number;
    account?: string;
    name?: string;
    orderId?: string;
    refAccount?: string;
    agentAccount?: string;
    platformKey?: string;
    categoryKey?: string;
    channelKey?: string;
    tagId?: number[];
    status?: 1 | 0;
    excludeTest?: 1 | 0;
  } & {
    timeStart?: number;
    timeEnd?: number;
  }
) => {
  const data = {
    ...params
  };
  if (data.start) {
    data.timeStart = Number(data.start);
  }
  if (data.end) {
    data.timeEnd = Number(data.end);
  }
  return apiRequest().get<{ id: number }>('/system/topup/order/export', {
    params: data
  });
};
