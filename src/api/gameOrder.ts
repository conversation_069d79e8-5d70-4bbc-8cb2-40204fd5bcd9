import { GameOrderResult } from '@/enums/game';
import { TimeSelect } from '@/enums/timeSelect';
import { GameCategoryCount, GameOrder } from '@/types/game';

import apiRequest from './services';

export const getCategoryCount = async (
  params: {
    timeFilter?: TimeSelect;
    start?: number;
    end?: number;
    playerAccount?: string;
    id?: string;
    gameAccount?: string;
    excludeTest?: 1 | 0;
  } & {
    timeStart?: number;
    timeEnd?: number;
  }
) => {
  if (params.start) {
    params.timeStart = Number(params.start);
  }
  if (params.end) {
    params.timeEnd = Number(params.end);
  }
  return apiRequest().get<GameCategoryCount>('/system/gameorder/list/count', {
    params
  });
};

type GameOrderStatistics = {
  accountCount: number;
  orderCount: number;
  sumPageBetPoint: number;
  sumTtlBetPoint: number;
  sumPageProfit: number;
  sumTtlProfit: number;
};

type GameOrderData = {
  data: GameOrder[];
  statistic: GameOrderStatistics;
  total: number;
};

export type GameOrderResponse = GameOrderData;

export const getSlotGameOrder = async (
  params: {
    timeFilter?: TimeSelect;
    playerAccount?: string;
    id?: string; // 遊戲id
    gameAccount?: string;
    providers?: string[];
    excludeTest?: 1 | 0;
    status: 1 | 0;
    result?: GameOrderResult;
    start?: number;
    end?: number;
    page: number;
    limit: number;
  } & {
    timeStart?: number;
    timeEnd?: number;
  }
) => {
  const data = {
    ...params
  };
  if (data.start) {
    data.timeStart = Number(data.start);
  }
  if (data.end) {
    data.timeEnd = Number(data.end);
  }
  return apiRequest().get<GameOrderResponse>('/system/gameorder/list/slot', {
    params: data
  });
};

export const getFishingGameOrder = async (
  params: {
    timeFilter?: TimeSelect;
    playerAccount?: string;
    id?: string; // 遊戲id
    gameAccount?: string;
    providers?: string[];
    excludeTest?: 1 | 0;
    status: 1 | 0;
    result?: GameOrderResult;
    start?: number;
    end?: number;
    page: number;
    limit: number;
  } & {
    timeStart?: number;
    timeEnd?: number;
  }
) => {
  const data = {
    ...params
  };
  if (data.start) {
    data.timeStart = Number(data.start);
  }
  if (data.end) {
    data.timeEnd = Number(data.end);
  }
  return apiRequest().get<GameOrderResponse>('/system/gameorder/list/fish', {
    params: data
  });
};

export const getTableGameOrder = async (
  params: {
    timeFilter?: TimeSelect;
    playerAccount?: string;
    id?: string; // 遊戲id
    gameAccount?: string;
    providers?: string[];
    excludeTest?: 1 | 0;
    status: 1 | 0;
    matchId?: string;
    result?: GameOrderResult;
    start?: number;
    end?: number;
    page: number;
    limit: number;
  } & {
    timeStart?: number;
    timeEnd?: number;
  }
) => {
  const data = {
    ...params
  };
  if (data.start) {
    data.timeStart = Number(data.start);
  }
  if (data.end) {
    data.timeEnd = Number(data.end);
  }
  return apiRequest().get<GameOrderResponse>('/system/gameorder/list/table', {
    params: data
  });
};

export const getArcadeGameOrder = async (
  params: {
    timeFilter?: TimeSelect;
    playerAccount?: string;
    id?: string; // 遊戲id
    gameAccount?: string;
    matchId?: string;
    providers?: string[];
    excludeTest?: 1 | 0;
    status: 1 | 0;
    result?: GameOrderResult;
    start?: number;
    end?: number;
    page: number;
    limit: number;
  } & {
    timeStart?: number;
    timeEnd?: number;
  }
) => {
  const data = {
    ...params
  };
  if (data.start) {
    data.timeStart = Number(data.start);
  }
  if (data.end) {
    data.timeEnd = Number(data.end);
  }
  return apiRequest().get<GameOrderResponse>('/system/gameorder/list/arcade', {
    params: data
  });
};

// 注單數據導出
export const exportGameOrder = async (
  params: {
    category: 'slot' | 'fish' | 'table' | 'arcade';
    timeFilter?: TimeSelect;
    playerAccount?: string;
    id?: string; // 遊戲id
    gameAccount?: string;
    matchId?: string;
    providers?: string[];
    excludeTest?: 1 | 0;
    status: 1 | 0;
    result?: GameOrderResult;
    start?: number;
    end?: number;
  } & {
    timeStart?: number;
    timeEnd?: number;
  }
) => {
  const data = {
    ...params
  };
  if (data.start) {
    data.timeStart = Number(data.start);
  }
  if (data.end) {
    data.timeEnd = Number(data.end);
  }
  return apiRequest().get<{ id: number }>('/system/gameorder/list/export', {
    params: data
  });
};

// 第三方注單號查詢內容的url
export const getBetContent = async (params: { id: number; status: 0 | 1 }) => {
  return apiRequest().get<{ url: string }>('/system/gameorder/detail', {
    params
  });
};
