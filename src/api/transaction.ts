import { TransactionRecord } from '@/types/transaction';

import apiRequest, { IResponseDataPage } from './services';

export const getTransactionRecord = async (
  params: {
    id?: string;
    playerId?: number;
    account?: string;
    name?: string;
    currency?: string;
    accountType: number; // 1: 非測試帳號, 0: all
    type?: string; // 金流類型
    start?: number;
    end?: number;
    page: number;
    limit: number;
  } & {
    timeStart?: number;
    timeEnd?: number;
  }
) => {
  const data = {
    ...params
  };
  if (data.start) {
    data.timeStart = Number(data.start);
  }
  if (data.end) {
    data.timeEnd = Number(data.end);
  }
  return apiRequest().get<IResponseDataPage<TransactionRecord>>('/system/funding/list', {
    params: data
  });
};

// 數據導出
export const getTransactionRecordExport = async (
  params: {
    id?: string;
    playerId?: number;
    account?: string;
    name?: string;
    currency?: string;
    accountType?: number; // 1: 非測試帳號, 0: all
    type?: string; // 金流類型
    start?: number;
    end?: number;
  } & {
    timeStart?: number;
    timeEnd?: number;
  }
) => {
  const data = {
    ...params
  };
  if (data.start) {
    data.timeStart = Number(data.start);
  }
  if (data.end) {
    data.timeEnd = Number(data.end);
  }
  return apiRequest().get<{ id: number }>('/system/funding/export', {
    params: data
  });
};
