import { ActivityFilter } from '@/pages/operation/activity/enum';
import { Activity, DisActiveActivity } from '@/types/activity';
import {
  ActivityTemplate,
  UnreviewedActiveOrder,
  UnreviewedActiveOrderSearchParams
} from '@/types/activity';

import apiRequest, { IResponseDataPage } from './services';

export const getAcitveActivity = (params: {
  templateType?: number;
  needApply?: 0 | 1;
  isReview?: 0 | 1;
  status?: number;
}) => {
  return apiRequest().get<Activity[]>('/system/activity/list/continued', { params });
};

export const getDisActiveActivity = (params: { page: number; limit: number }) => {
  return apiRequest().get<IResponseDataPage<DisActiveActivity>>(
    '/system/activity/list/discontinued',
    { params }
  );
};

interface giftMoneyCondition {
  min: number | null;
  max: number | null;
  feedback?: number; // 固定額度上限
  feedbackPercent?: number; // 百分比
  feedbackUpperLimit?: number; // 固定額度上限
}

export const createActivity = (params: {
  templateType: number;
  order?: number;
  isReview: 0 | 1;
  zhTwTitle: string;
  zhTwContent: string;
  zhTwPhoto?: File;
  needApply?: 0 | 1;
  openCta?: 0 | 1;
  joinLevel?: number; // 參加的vip等級
  joinMode?: 0 | 1 | 2; // 0:不限制 1:可參與 2:不可參與
  tags?: number[]; //可參與及不可參與規範的玩家標籤
  frozenTime: number; // 單位為小時,輸入0表示不凍結
  withdrawRewardTime: number; // 單位為小時,輸入0表示不凍結
  giftMoneyMode: 1 | 2; // 1:比例 2:固定金額
  giftMoneyCondition: giftMoneyCondition[];
}) => {
  const formData = new FormData();
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      if (value instanceof File) {
        formData.append(key, value);
      } else if (typeof value === 'object') {
        // 對於對象和數組，轉換為 JSON 字串
        formData.append(key, JSON.stringify(value));
      } else {
        formData.append(key, String(value));
      }
    }
  });
  return apiRequest().post('/system/activity/create', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

// 取得所有活動細則(模板)明細
export const getActivityTemplate = () => {
  return apiRequest().get<{ id: number; label: string }[]>('/system/activity/template');
};

// 取得所有上架時間類型
export const getPublishTimeType = () => {
  return apiRequest().get<{ id: number; label: string }[]>('/system/activity/shelfTime');
};

// 取得所有活動實際進行時間類型
export const getCounductTimeType = () => {
  return apiRequest().get<{ id: number; label: string }[]>('/system/activity/conductTime');
};

// 取得所有活動狀態類型
export const getActivityStatus = (params: { filter?: ActivityFilter }) => {
  return apiRequest().get<{ id: number; label: string }[]>('/system/activity/status', { params });
};

// 取得活動詳細資訊
export const getActivityDetail = (params: { id: number }) => {
  return apiRequest().get<Activity>(`/system/activity/details`, { params });
};

// 更新活動
export const updateActivity = (params: {
  id: number;
  templateType: number;
  order?: number;
  isReview: 0 | 1;
  zhTwTitle: string;
  zhTwContent: string;
  zhTwPhoto?: File;
  needApply?: 0 | 1;
  openCta?: 0 | 1;
  joinLevel: number; // 參加的vip等級
  joinMode: 0 | 1 | 2; // 0:不限制 1:可參與 2:不可參與
  tags?: number[]; //可參與及不可參與規範的玩家標籤
  frozenTime: number; // 單位為小時,輸入0表示不凍結
  withdrawRewardTime: number; // 單位為小時,輸入0表示不凍結
  giftMoneyMode?: 1 | 2; // 1:比例 2:固定金額
  giftMoneyCondition?: giftMoneyCondition[];
}) => {
  const formData = new FormData();
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      if (value instanceof File) {
        formData.append(key, value);
      } else if (typeof value === 'object') {
        // 對於對象和數組，轉換為 JSON 字串
        formData.append(key, JSON.stringify(value));
      } else {
        formData.append(key, String(value));
      }
    }
  });
  return apiRequest().post(`/system/activity/edit`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

export const releaseActivity = (params: {
  id: number;
  shelfType: number;
  shelfStartTime: number | null;
  shelfEndTime: number | null;
  conductType: number;
  conductWeek?: {
    weekStart: number;
    weekEnd: number;
    timeStart: string;
    timeEnd: string;
  };
  conductStartTime: number | null;
  conductEndTime: number | null;
}) => {
  return apiRequest().put('/system/activity/release', params);
};

export const unpublishActivity = (params: { id: number }) => {
  return apiRequest().put('/system/activity/discontinue', params);
};

// Unreviewed Active Orders API functions
export const getUnreviewedActiveOrderList = async (params: UnreviewedActiveOrderSearchParams) => {
  return apiRequest().get<IResponseDataPage<UnreviewedActiveOrder>>(
    '/system/activity/list/unreviewed',
    {
      params
    }
  );
};

// Activity Template API functions
export const getActivityTemplates = () => {
  return apiRequest().get<ActivityTemplate[]>('/system/activity/template');
};

export const updateUnreviewedActiveOrderReviewStatus = (params: { id: string; review: number }) => {
  return apiRequest().put('/system/activity/review/unreviewed', params);
};
