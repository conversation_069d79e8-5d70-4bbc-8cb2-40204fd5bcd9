import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import axios from 'axios';

import { useUserStore } from '@/store/userStore';
import { globalMessage } from '@/utils/globalMessage';
import { convertToCamel, convertToSnake } from '@/utils/object';

export interface IResponse<T> {
  code: number;
  data?: T;
  message?: string;
  msg?: string;
}

export interface IResponseDataPage<T> {
  data: T[];
  total: number;
}

export interface IResponseDataPageStatistic<T, U> {
  data: T[];
  total: number;
  statistic: U;
}

interface ApiRequestOptions extends AxiosRequestConfig {
  useAuth?: boolean;
}

const baseConfig: AxiosRequestConfig = {
  baseURL: `${import.meta.env.VITE_API_BASE_URL}api`, // 需從.env設定環境變數
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
};

function createApi(config: ApiRequestOptions): AxiosInstance {
  const { useAuth = true, ...axiosConfig } = config;
  const instance = axios.create(Object.assign({}, baseConfig, axiosConfig));
  const { userInfo } = useUserStore.getState();
  const token = userInfo?.token;

  if (useAuth && token) {
    instance.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }

  instance.interceptors.request.use(
    (config) => {
      return {
        ...config,
        params: convertToSnake(config.params), // 網址後帶參數
        data: convertToSnake(config.data) // 傳給後端的資料
      };
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  instance.interceptors.response.use(
    (res: AxiosResponse) => {
      const convertToCamelData = convertToCamel(res.data);

      if (res?.data?.message) {
        globalMessage({
          content: res?.data?.message,
          type: res?.data?.code === 200 ? 'success' : 'error'
        });
      }

      // 處理 401 未授權錯誤
      if (convertToCamelData.code === 401) {
        const { clearUser } = useUserStore.getState();
        clearUser();
        return Promise.reject(convertToCamelData);
      }

      if (convertToCamelData.code !== 200) {
        return Promise.reject(convertToCamelData);
      }
      return convertToCamelData;
    },
    (error) => {
      // 處理 HTTP 狀態碼 401
      if (error.response?.status === 401) {
        const { clearUser } = useUserStore.getState();
        clearUser();
      }

      // 如果是網路連線異常，Axios 在前端檢測到網路連線問題時自動設定的錯誤代碼來觸發 ErrorBoundary
      if (
        !error.response &&
        (error.code === 'NETWORK_ERROR' ||
          error.code === 'ERR_NETWORK' ||
          error.code === 'ERR_BAD_REQUEST')
      ) {
        throw new Error('網路連線異常，請檢查您的網路連線');
      }

      return Promise.reject(error);
    }
  );

  return instance;
}

export function apiRequest(config: ApiRequestOptions = {}) {
  const instance = createApi(config);
  return {
    request: <T>(config: AxiosRequestConfig): Promise<IResponse<T>> => {
      return instance.request(config);
    },
    get: <T>(url: string, config?: AxiosRequestConfig): Promise<IResponse<T>> => {
      return instance.get(url, config);
    },
    post: <T>(
      url: string,
      data?: Record<string, unknown> | FormData,
      config?: AxiosRequestConfig
    ): Promise<IResponse<T>> => {
      return instance.post(url, data, config);
    },
    put: <T>(
      url: string,
      data?: Record<string, unknown> | FormData,
      config?: AxiosRequestConfig
    ): Promise<IResponse<T>> => {
      return instance.put(url, data, config);
    },
    delete: <T>(url: string, data?: Record<string, unknown>): Promise<IResponse<T>> => {
      return instance.delete(url, { data });
    }
  };
}

export default apiRequest;
