import apiRequest from './services';

export const getLanguageList = async () => {
  return await apiRequest().get<{ default: string; list: string[] }>('/system/lang');
};

export const uploadImage = (params: { images: File[] }) => {
  const formData = new FormData();
  params.images.forEach((image) => {
    formData.append('images[]', image);
  });
  return apiRequest().post<string[]>('/system/editor/upload/images', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};
