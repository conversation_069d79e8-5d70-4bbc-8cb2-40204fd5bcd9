import { SoundEffectAPI } from '@/enums/platform';
import {
  FrontendBindPhoneSetting,
  FrontendChangeNameSetting,
  LoginSettingsData
} from '@/types/platform';

import apiRequest from './services';

export interface PlatformSettings {
  id?: number;
  key: string;
  customerServiceLink: string;
  frontendBackgroundMusic: string;
  frontendBackgroundMusicName: string;
  frontendClickOpenSound: string;
  frontendClickOpenSoundName: string;
  frontendClickCloseSound: string;
  frontendClickCloseSoundName: string;
  frontendClickHintSound: string;
  frontendClickHintSoundName: string;
  frontendClickFailedSound: string;
  frontendClickFailedSoundName: string;
  loginSetting: LoginSettingsData;
  frontendChangeNameSetting: FrontendChangeNameSetting;
  frontendBindPhoneSetting: FrontendBindPhoneSetting;
  createdAt: string;
  updatedAt: string;
  topupVipLevel: number;
  topupFrozenRemainTimeLimit: number;
}

export interface PlatformSettingsRequest extends Record<string, unknown> {
  id?: number;
  customerServiceLink?: string;
  frontendBackgroundMusic?: string | File;
  frontendBackgroundMusicName?: string;
  frontendClickOpenSound?: string | File;
  frontendClickOpenSoundName?: string;
  frontendClickCloseSound?: string | File;
  frontendClickCloseSoundName?: string;
  frontendClickHintSound?: string | File;
  frontendClickHintSoundName?: string;
  frontendClickFailedSound?: string | File;
  frontendClickFailedSoundName?: string;
  loginSetting?: LoginSettingsData;
  frontendChangeNameSetting?: FrontendChangeNameSetting;
  frontendBindPhoneSetting?: FrontendBindPhoneSetting;
}

// 音效相關的字段
const SOUND_FIELDS = [
  SoundEffectAPI.BACKGROUND_MUSIC,
  SoundEffectAPI.CLICK_OPEN_SOUND,
  SoundEffectAPI.CLICK_CLOSE_SOUND,
  SoundEffectAPI.CLICK_HINT_SOUND,
  SoundEffectAPI.CLICK_FAILED_SOUND
] as const;

export const getPlatformSettings = async () => {
  const response = await apiRequest({ useAuth: true }).get<PlatformSettings>(
    '/system/platform/get'
  );
  return response;
};

export const editSoundSettings = async (settings: PlatformSettingsRequest) => {
  const formData = new FormData();

  // 只添加文件和相關名稱
  SOUND_FIELDS.forEach((field) => {
    const value = settings[field];
    const nameKey = `${field}Name`;

    if (value instanceof File) {
      formData.append(field, value);

      if (settings[nameKey]) {
        formData.append(nameKey, String(settings[nameKey]));
      }
    }
  });

  const response = await apiRequest({ useAuth: true }).post(
    '/system/platform/edit',
    formData as unknown as Record<string, unknown>,
    {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }
  );

  return response;
};

export const editBasicSettings = async (settings: PlatformSettingsRequest) => {
  const basicSettings: Record<string, unknown> = {};

  Object.entries(settings).forEach(([key, value]) => {
    basicSettings[key] = value;
  });

  const response = await apiRequest({ useAuth: true }).post(
    '/system/platform/editBasic',
    basicSettings
  );

  return response;
};
