import {
  Game,
  GameCategory,
  GameLayoutConfig,
  GameMaintenance,
  GameMaintenanceMode,
  GameProvider,
  OtherGameLayoutMode
} from '@/types/game';

import apiRequest, { IResponseDataPage } from './services';

// API Functions

// Provider Management
export const getGameProviderList = async () => {
  return apiRequest().get<GameProvider[]>('/system/game/list/provider');
};

export const editGameProviderStatus = async (params: { id: string; status: 1 | 0 }) => {
  return apiRequest().put('/system/game/edit/provider/status', params);
};

// Category Management
export const getGameCategoryList = async () => {
  return apiRequest().get<GameCategory[]>('/system/game/list/category');
};

export const editGameCategoryStatus = async (params: { id: string; status: 1 | 0 }) => {
  return apiRequest().put('/system/game/edit/category/status', params);
};

// Game Management
export const getGameList = async (params?: {
  page?: number;
  limit?: number;
  provider?: string;
  name?: string;
  category?: string;
  status?: number;
}) => {
  return apiRequest().get<IResponseDataPage<Game>>('/system/game/list', {
    params
  });
};

export const editGameStatus = async (params: { id: string; status: 1 | 0 }) => {
  return apiRequest().put('/system/game/edit/status', params);
};

export const editGameLimit = async (params: { id: string; vipLimit: number }) => {
  return apiRequest().put('/system/game/edit/limit', params);
};

// Game Image Upload
export const uploadGameImage = async (params: {
  id: string;
  icon?: File;
  rkmv?: File;
  rkmh?: File;
}) => {
  const formData = new FormData();
  formData.append('id', params.id);
  ['icon', 'rkmv', 'rkmh'].forEach((key) => {
    if (params[key as keyof typeof params]) {
      formData.append(key, params[key as keyof typeof params] as File);
    }
  });

  return apiRequest().post('/system/game/edit/img', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

// Game File Upload
export const uploadGameFile = async (params: { files: File[] }) => {
  const formData = new FormData();
  params.files.forEach((file) => {
    formData.append('files[]', file);
  });

  return apiRequest().post('/system/game/edit/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 60 * 1000 // 圖片上傳需要比較久時間
  });
};

// Maintenance Management
export const getGameMaintenanceList = async (params?: {
  page?: number;
  limit?: number;
  providerName?: string;
  gameName?: string;
  status?: number;
}) => {
  return apiRequest().get<IResponseDataPage<GameMaintenance>>('/system/game/list/maintain', {
    params
  });
};

export const createGameMaintenance = async (params: {
  providerId: string;
  categoryId: string;
  gameId: string;
  mode: GameMaintenanceMode;
  startTime: number;
  endTime: number;
}) => {
  return apiRequest().post('/system/game/create/maintain', params);
};

export const editGameMaintenanceStatus = async (params: { id: string; status: 1 | 0 }) => {
  return apiRequest().put('/system/game/edit/maintain/status', params);
};

export const cancelGameMaintenance = async (params: { id: string }) => {
  return apiRequest().put('/system/game/edit/maintain/cancel', params);
};

// Game Options
export const getGameOptions = async (params?: { providerId?: string; categoryId?: string }) => {
  return apiRequest().get<[]>('/system/game/list/option', {
    params
  });
};

// Game Layout Management
export const getGameLayoutList = async (params: { type: string }) => {
  return apiRequest().get<GameLayoutConfig>('/system/game/list/order', {
    params
  });
};

export const updateGameLayout = async (params: {
  type: string;
  orders: string[];
  otherGameOrderMode: OtherGameLayoutMode;
}) => {
  return apiRequest().put('/system/game/edit/order', params);
};
