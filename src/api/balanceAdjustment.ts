import { BalanceAdjustment, BalanceAdjustmentSearchParams } from '@/types/balanceAdjustment';

import apiRequest, { IResponseDataPage } from './services';

export type BalanceAdjustmentCreateParams = {
  account: string;
  changeType: number;
  currency: string;
  amount: number;
  frozenTime?: number;
  note: string;
};

export const getBalanceAdjustmentList = async (params: BalanceAdjustmentSearchParams) => {
  return apiRequest().get<IResponseDataPage<BalanceAdjustment>>('/system/player/list/remain', {
    params
  });
};

export const createBalanceAdjustment = async (params: BalanceAdjustmentCreateParams) => {
  return apiRequest().put('/system/player/edit/remain', params);
};
