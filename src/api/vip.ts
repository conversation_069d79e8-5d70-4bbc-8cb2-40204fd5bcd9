import { Vip } from '@/types/vip';

import apiRequest from './services';

export const getVipList = async (params: { name?: string; status?: number }) => {
  return apiRequest().get<Vip[]>('/system/vip/getVip', { params });
};

export type EditParams = {
  id: number;
  totalAccumulatedValue: number;
  totalAccumulatedBet: number;
  monthAccumulatedValue: number;
  monthAccumulatedBet: number;
  bindPhone: number;
  firstTopup: number;
};

export const updateVip = async (data: EditParams[]) => {
  return apiRequest().put<Vip>('/system/vip/editVip', { data });
};

export const editVipLevelSetting = async (params: { vipId: number }) => {
  return apiRequest().put(`/system/vip/editVipStatus`, params);
};
