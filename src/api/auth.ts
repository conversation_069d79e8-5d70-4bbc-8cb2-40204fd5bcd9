import apiRequest from './services';

export interface LoginResponseData {
  firstLogin: boolean;
  rebindOtp: boolean;
  qrcodeUrl?: string;
  twoFactorSecret?: string;
}

export interface OtpLoginResponseData {
  firstLogin: boolean;
  rebindotp: boolean;
  token: string;
}

export interface UserInfoResponse {
  adminId: string;
  account: string;
  permissions: permissionsCategory[];
  token: string;
}
export interface permissionsCategory extends permissionsGroup {
  permissions: permissionsGroup[];
}

export interface permissionsGroup extends permissions {
  order: number;
  permissions: permissions[];
}

export interface permissions {
  id: number;
  label: string;
  name: string;
}

// 輸入帳密登入
export const login = async (params: { account: string; password: string }) => {
  const response = await apiRequest({ useAuth: false }).post<LoginResponseData>(
    '/system/auth/login',
    params
  );
  return response;
};

// otp 登入
export const otpLogin = async (params: {
  account: string;
  password: string;
  newPassword?: string;
  newPasswordConfirmation?: string;
  otp: string;
}) => {
  const response = await apiRequest({ useAuth: false }).post<OtpLoginResponseData>(
    '/system/auth/otplogin',
    params
  );
  return response;
};

// 檢查密碼
export const updatePassword = async (params: {
  password: string;
  passwordConfirmation: string;
}) => {
  const response = await apiRequest({ useAuth: false }).post('/system/auth/check/pwd', params);
  return response;
};

// 取得使用者資料
export const getUserInfo = async () => {
  const response = await apiRequest({ useAuth: true }).post<UserInfoResponse>('/system/auth/me');
  return response;
};

// 登出
export const logout = async () => {
  const response = await apiRequest({ useAuth: true }).post('/system/auth/logout');
  return response;
};
