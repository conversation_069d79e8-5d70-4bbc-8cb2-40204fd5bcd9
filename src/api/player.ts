import { Permission, PlayerData, PlayerPermission, PlayerTag } from '@/types/playerlist';

import apiRequest, { IResponseDataPage } from './services';

export const getPlayerList = async (
  params: {
    accountType: number;
    refAccount?: string;
    account?: string;
    start?: number;
    end?: number;
    name?: string;
    phone?: string;
    registerType?: 1 | 2 | 3;
    tags?: { id: number; name: string }[];
    level?: number;
    status?: number;
    page: number;
    limit: number;
  } & {
    timeStart?: number;
    timeEnd?: number;
  }
) => {
  const data = {
    ...params
  };
  if (data.start) {
    data.timeStart = Number(data.start);
  }
  if (data.end) {
    data.timeEnd = Number(data.end);
  }
  return apiRequest().get<IResponseDataPage<PlayerData>>('/system/player/list', {
    params: data
  });
};

export const createPlayer = (params: {
  accountType: number;
  refAccount?: string;
  account: string;
  name: string;
  password: string;
  countryCode?: string;
  phone?: string;
  level: number;
  playerTag?: string;
  status: 1 | 0;
}) => {
  return apiRequest().post('/system/player/create', params);
};

export const editPlayer = (params: {
  playerId: number;
  name: string;
  countryCode?: string;
  phone?: string;
  level: number;
  playerTag?: string;
  status: 1 | 0;
}) => {
  return apiRequest().put('/system/player/edit', params);
};

export const editPlayerStatus = (params: { id: number; status: 1 | 0 }) => {
  const data = { ...params, playerId: params.id };
  return apiRequest().put('/system/player/edit/status', data);
};

// 檢查玩家帳號是否重複
export const checkAccount = (params: { account: string }) => {
  return apiRequest().post('/system/player/check/account', params);
};

// 檢查玩家暱稱是否重複
export const checkName = (params: { name: string }) => {
  return apiRequest().post('/system/player/check/name', params);
};

// 獲取同手機帳號的帳號
export const getPlayerByPhone = (params: { countryCode: string; phone: string }) => {
  return apiRequest().get<{ id: number; account: string; name: string; remain: number }>(
    '/system/player/getPlayerByPhone',
    {
      params
    }
  );
};

export const getPlayerTagList = (params: { page?: number; limit?: number }) => {
  return apiRequest().get<IResponseDataPage<PlayerTag>>('/system/tag/list', { params });
};

export const getPlayerTagListOption = () => {
  return apiRequest().get<PlayerTag[]>('/system/tag/list');
};

export const createPlayerTag = (params: { name: string; permissions: number[] }) => {
  return apiRequest().post('/system/tag/create', params);
};

export const editPlayerTag = (params: { tagId: number; name: string; permissions: number[] }) => {
  return apiRequest().put('/system/tag/edit', params);
};

export const deletePlayerTag = (params: { tagId: number }) => {
  return apiRequest().delete('/system/tag/del', params);
};

export const checkPlayerTagName = (params: { name: string }) => {
  return apiRequest().post('/system/tag/check/name', params);
};

// 取得玩家權限 (類型 １玩家 ２標籤 ３VIP)
export const getAllPlayerPermission = (params: { groupType: number }) => {
  return apiRequest().get<Permission[]>('/system/permission/list', { params });
};

// 取得標籤權限
export const getTagPermission = (params: { id: number }) => {
  return apiRequest().get<number[]>('/system/tag/getPermission', { params });
};

// 取得玩家權限
export const getPlayerPermission = (params: { id: number }) => {
  return apiRequest().get<PlayerPermission>('/system/player/getPermissions', { params });
};

//編輯玩家權限
export const editPlayerPermission = (params: { id: number; permissions: number[] }) => {
  return apiRequest().put('/system/player/editPermissions', params);
};

// 解除玩家VIP Modify狀態
export const editPlayerVipModify = (params: { id: number }) => {
  return apiRequest().put('/system/player/edit/modify', params);
};

// 取得玩家 VIp等級變動紀錄
export const getPlayerVipChangeLog = (params: { id: number }) => {
  return apiRequest().get<{ id: number; permissions: number[] }>('/system/vip/getVipChangeLog', {
    params
  });
};
