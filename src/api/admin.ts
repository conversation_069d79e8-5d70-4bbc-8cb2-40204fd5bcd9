import {
  type Admin,
  AdminLog,
  AdminLogOption,
  Permission,
  Role,
  type RoleOption
} from '@/types/admin';

import apiRequest, { IResponse, IResponseDataPage } from './services';

export const getAdminList = (
  params: {
    account?: string;
    createdBy?: string;
    updatedBy?: string;
    roleId?: number;
    status?: 1 | 0;
    start?: number;
    end?: number;
    page: number;
    limit: number;
  } & {
    timeStart?: number;
    timeEnd?: number;
  }
) => {
  const data = {
    ...params
  };
  if (data.start) {
    data.timeStart = Number(data.start);
  }
  if (data.end) {
    data.timeEnd = Number(data.end);
  }
  return apiRequest().get<IResponseDataPage<Admin>>('/system/admin/list', { params: data });
};

export const createAdmin = (params: {
  account: string;
  password: string;
  roleId: number;
  status: 1 | 0;
}) => {
  return apiRequest().post('/system/admin/create', params);
};

export const editAdmin = (params: { id: number; roleId?: number; status?: 1 | 0 }) => {
  const data = { ...params, adminId: params.id };
  return apiRequest().put('/system/admin/edit', data);
};

export const editAdminStatus = (params: { id: number; status: 1 | 0 }) => {
  const data = { ...params, adminId: params.id };
  return apiRequest().post('/system/admin/status', data);
};

export const getRoleListOptions = () => {
  return apiRequest().get<RoleOption[]>('/system/role/list/option');
};

export const getRoleList = (params: { page: number; limit: number }) => {
  return apiRequest().get<IResponseDataPage<Role>>('/system/role/list', { params });
};

export const getRoleAdminList = (params: { roleId: number }) => {
  return apiRequest().get<IResponseDataPage<Admin>>('/system/admin/list', { params });
};

export const createRole = (params: { name: string; permissions: number[] }) => {
  return apiRequest().post('/system/role/create', params);
};

export const editRole = (params: { id: number; name: string; permissions: number[] }) => {
  const data = { ...params, roleId: params.id };
  return apiRequest().put('/system/role/edit', data);
};

export const deleteRole = (params: { id: number }) => {
  const data = { roleId: params.id };
  return apiRequest().delete('/system/role/del', data);
};

export const getAllPermissions = () => {
  return apiRequest().get<Permission[]>('/system/role/list/permissions');
};

export const adminLogListOptions = () => {
  return apiRequest().get<AdminLogOption[]>('/system/log/list/page');
};

// 取得操作紀錄
export const getAdminLogList = (
  params: {
    account?: string;
    opPage?: number; // 操作頁面
    content?: string;
    start?: number;
    end?: number;
    page: number;
    limit: number;
  } & {
    timeStart?: number;
    timeEnd?: number;
  }
) => {
  const data = {
    ...params
  };
  if (data.start) {
    data.timeStart = Number(data.start);
  }
  if (data.end) {
    data.timeEnd = Number(data.end);
  }
  return apiRequest().get<IResponseDataPage<AdminLog>>('/system/log/list', { params: data });
};

// 數據導出操作紀錄
export const getLogDataExport = (
  params: {
    account?: string;
    opPage?: number;
    content?: string;
    start?: number;
    end?: number;
  } & {
    timeStart?: number;
    timeEnd?: number;
  }
) => {
  const data = {
    ...params
  };
  if (data.start) {
    data.timeStart = Number(data.start);
  }
  if (data.end) {
    data.timeEnd = Number(data.end);
  }
  return apiRequest().get<{ id: number }>('/system/log/export', {
    params: data
  });
};

export enum ExportStatus {
  PROCESSING = 1,
  COMPLETED = 2,
  DELETED = 3,
  ERROR = 4
}

export type RecordExport = {
  id: number;
  account: string;
  opPage: string;
  status: ExportStatus; // 1.處理中 2.已完成 3.刪除 4.異常
  link: string;
  startTime: number; // 資料區間
  endTime: number;
  createdAt: number; // 新增時間
  updatedAt: number; // 更新時間
};

export const getRecordExport = (params: {
  id?: number;
  account?: string;
  page: number;
  limit: number;
}) => {
  return apiRequest().get<IResponseDataPage<RecordExport>>('/system/exportlog/list', {
    params
  });
};

export const deleteRecordExport = (params: { id: number }) => {
  return apiRequest().delete('/system/exportlog/del', { id: params.id });
};

export const resetAdminPassword = (params: {
  adminId: number;
}): Promise<IResponse<{ newPassword: string }>> => {
  return apiRequest().post('/system/auth/reset/pwd', params);
};

export const resetAdminOtp = (params: { adminId: number }): Promise<IResponse<object>> => {
  return apiRequest().post('/system/auth/reset/otp', params);
};
