import { ReactNode } from 'react';
import { ErrorBoundary } from 'react-error-boundary';

import AntdProvider from '@/components/providers/AntdProvider';
import ErrorBoundaryPage from '@/pages/errorBoundary';

interface AppProps {
  children: ReactNode;
}

function App({ children }: AppProps) {
  return (
    <>
      <AntdProvider>
        <ErrorBoundary fallback={<ErrorBoundaryPage />}>{children}</ErrorBoundary>
      </AntdProvider>
    </>
  );
}

export default App;
