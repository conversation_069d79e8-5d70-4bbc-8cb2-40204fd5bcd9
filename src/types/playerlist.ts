export type PlayerData = {
  id: number;
  accountType: number; // 測試正式帳號
  account: string;
  name: string; // 暱稱
  refAccount?: string; // 上級帳號
  level: number; // VIP等級
  countryCode: string; // 國碼
  phone: string;
  playerPhoneId?: number;
  registerType: number;
  remain: number;
  tags: { id: number; name: string }[];
  isModify: 1 | 0; // 是否是VIP Modify狀態
  registerIp: string;
  registerIpIsoCode: string; // 註冊IP國碼
  createdAt: number; // 註冊時間
  lastLoginIp: string;
  lastLoginIpIsoCode: string; // 最後登入IP國碼
  lastLoginTime: number;
  status: 1 | 0;
};

export type PlayerTag = {
  id: number;
  name: string;
  order: number;
  createdAt: number;
  updatedAt: number;
  createdBy: string;
  updatedBy: string;
};

export type Permission = {
  id: number;
  name: string;
  label: string;
  permissions?: Permission[];
};

export type PlayerPermission = {
  id: number;
  account: string;
  name: string;
  permissions?: Permission[];
};
