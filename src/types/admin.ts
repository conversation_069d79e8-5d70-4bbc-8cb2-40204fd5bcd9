export type Permission = {
  id: number;
  label: string;
  permissions?: Permission[];
  children?: Permission[];
};

export type Role = {
  id?: number;
  name: string;
  createdBy: string;
  updatedBy: string;
  createdAt: number;
  updatedAt: number;
  admins: {
    id: number;
    account: string;
  }[];
  permissions: Permission[];
};

export type RoleOption = Pick<Role, 'id' | 'name'>;

export type Admin = {
  id: number;
  account: string;
  twoFactorSecret: string;
  qrcodeUrl: string;
  status: 1 | 0;
  firstLogin: boolean;
  rebindOtp: boolean;
  createdBy: number;
  updatedBy: number;
  createdAt: number;
  updatedAt: number;
  roles: Role[];
};

export type AdminLog = {
  id: number;
  account: string;
  opPage: number; // 操作頁面
  content: string; // 操作內容
  createdAt: number; // 操作時間
};

export type AdminLogOption = {
  // 操作頁面
  id: number;
  label: string;
};
