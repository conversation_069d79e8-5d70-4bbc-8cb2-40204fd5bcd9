export type TopupCategory = {
  id: number;
  key: string;
  contentMapping: {
    [key: string]: {
      title: string;
    };
  };
  imgUrlMapping: {
    [key: string]: {
      photo: string;
    };
  };
  order: number;
  createdAt: number;
  updatedAt: number;
  channelsCount: number;
};

export type TopupChannel = {
  id: number;
  key: string; // channelkey
  categoryKey: string;
  platformKey: string;
  contentMapping: {
    [key: string]: {
      title: string;
      content: string;
    };
  };
  imgUrlMapping:
    | string
    | {
        [key: string]: {
          photo: string;
        };
      };
  order: number;
  createdAt: number;
  updatedAt: number;
  itemsCount: number;
  tags: Array<{
    id: number;
    name: string;
    pivot: {
      channel_id: number;
      tag_id: number;
    };
  }>;
};

// 轉換組 channelkey必填
export type TopupItem = {
  id: number;
  channelKey: string;
  order: number;
  buyPrice: number;
  amount: number;
  status: 1 | 0;
};

// 供應商
export type TopupSupplier = {
  key: string;
  name: string;
};

export type TopupPopular = {
  id: number;
  categoryKey: string;
  channelKey: string;
  itemId: number; // 轉換組id
  contentMapping: {
    [key: string]: {
      title: string;
      content: string;
    };
  };
  imgUrlMapping: {
    [key: string]: {
      photo: string;
    };
  };
  order: number;
  highlight: 1 | 0;
};

export type TopupIncompletedOrder = {
  id: number;
  createdAt: number;
  account: string;
  name: string;
  refAccount: string;
  agentAccount: string;
  tags: { id: number; name: string }[];
  topupCount: number;
  platformKey: string; // 供應商
  categoryKey: string;
  channelKey: string;
  amount: number;
  points: number;
  status: 1 | 0;
  note: { zhTw: string };
};

export enum TopupOrderStatus {
  Pending = 0,
  Success = 1,
  Closed = 2
}

export type TopupOrderHistory = {
  id: number;
  createdAt: number;
  account: string;
  name: string;
  refAccount: string;
  agentAccount: string;
  tags: { id: number; name: string }[];
  topupCount: number;
  platformKey: string; // 供應商
  categoryKey: string;
  channelKey: string;
  amount: number;
  points: number;
  status: TopupOrderStatus;
  note: { zhTw: string };
  updatedAt: number;
  updatedBy: string;
};
