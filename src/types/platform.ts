// 定義音效設定的資料型別
export interface AudioFile {
  url?: string;
  name?: string;
  formData?: FormData;
  file?: File;
}

export interface FrontendChangeNameSetting {
  isFree: number;
  freeTimes: number;
  fee: string;
}

export interface FrontendBindPhoneSetting {
  changeable: number;
  fee: string;
}

// 定義一般設定的資料型別
export interface GeneralSettingsData {
  customerServiceLink: string;
  frontendChangeNameSetting: FrontendChangeNameSetting;
  frontendBindPhoneSetting: FrontendBindPhoneSetting;
}

export interface LoginSettingsData {
  line: 1 | 0;
  phone: 1 | 0;
  account: 1 | 0;
}
