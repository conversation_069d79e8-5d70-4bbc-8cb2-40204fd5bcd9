import { GameOrderResult } from '@/enums/game';

export type GameCategoryCount = {
  slot: {
    settled: number;
    unsettled: number;
  };
  fish: {
    settled: number;
    unsettled: number;
  };
  table: {
    settled: number;
    unsettled: number;
  };
  arcade: {
    settled: number;
    unsettled: number;
  };
};

export type GameOrder = {
  id: number;
  gameId: string;
  provider: string;
  providerName: { key: string; name: string };
  providerId: string;
  playerAccount: string;
  gameAccount: string;
  gameName: string;
  betAmount: number;
  profit: number;
  result: GameOrderResult;
  status: 1 | 0;
  createdAt: number; // 下注時間
  updatedAt: number; // 更新時間
};


export enum GameProviderWalletType {
  SINGLE = 'single',
  TRANSFER = 'transfer'
}
// Game Provider Types
export interface GameProvider {
  id: string;
  name: string;
  walletType: GameProviderWalletType;
  status: 1 | 0;
  updatedAt: number;
  updatedBy: string;
}

// Game Category Types
export interface GameCategory {
  id: string;
  category: string;
  name: string;
  status: 1 | 0;
  updatedAt: number;
  updatedBy: string;
}

// Game Types
export interface Game {
  id: string;
  name: string;
  category: string;
  provider: string;
  icon: string;
  rkmv: string;
  rkmh: string;
  conditions?: {
    vipLimit: number;
  };
  useJp: number;
  useMulti: number;
  noIcon: number;
  status: number;
  updatedBy: string;
  updatedAt: string;
}

export enum GameMaintenanceMode {
  NOW = 1,
  SCHEDULE = 2
}

export enum GameMaintenanceStatus {
  UNDER_MAINTENANCE = 1,
  SCHEDULED = 2,
  ENDED = 3,
  CANCELED = 4
}

// Game Maintenance Types
export interface GameMaintenance {
  id: string;
  providerName: string;
  categoryName: string;
  gameName: string;
  mode: GameMaintenanceMode;
  status: GameMaintenanceStatus;
  timeStart: number;
  timeEnd: number;
  createdBy: string;
  createdAt: number;
  updatedBy: string;
  updatedAt: number;
}

export interface GameLayoutItem {
  order: number;
  id: string;
  provider: string;
  category: string;
  name: string;
  icon: string;
}

export enum OtherGameLayoutMode {
  AUTO = 2,
  UNKNOWN = 1
}

export interface GameLayoutConfig {
  total: number;
  data: GameLayoutItem[];
  otherGameOrderMode: OtherGameLayoutMode;
}