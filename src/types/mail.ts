// Internal Letter Record interface based on API documentation
export interface InternalLetterRecord {
  id: string;
  category: number;
  category_Label: string;
  type: number;
  typeLabel: string;
  title: string;
  content: string;
  account: string;
  readCount: number;
  unreadCount: number;
  isRead: number;
  isReadLabel: string;
  isReceive: number;
  isReceiveLabel: string;
  readAt: number;
  receiveAt: number;
  attachmentId: number;
  status: MailStatusFilter;
  statusLabel: string;
  note: string;
  createdBy: string;
  updatedBy: string;
  createdAt: number;
  updatedAt: number;
}

export interface InternalLetterReceiver {
  account: string;
  isRead: MailReadStatus;
  isReadLabel: string;
  readAt: number;
  status: MailStatusFilter;
  statusLabel: string;
}

export interface InternalLetterRecordContentDetail {
  contentMapping: {
    [key: string]: {
      title: string;
      content: string;
    };
  };
}

// Mail Category interface for API response
export interface MailCategory {
  id: number;
  label: string;
}

// Mail Type interface for API response
export interface MailType {
  id: number;
  label: string;
  variables: MailVariable[];
}

// Search parameters for internal letter records API
export interface InternalLetterRecordSearchParams {
  timeFilter?: 'updated_at' | 'created_at';
  timeStart?: number;
  timeEnd?: number;
  category?: number;
  type?: number;
  isRead?: number;
  page: number;
  limit: number;
  orderBy?: string;
  sortBy?: 'asc' | 'desc';
}

// Search field options for the search form
export const MAIL_SEARCH_FIELD_OPTIONS = [
  { label: 'pages_operation_internalLetterManagement_account', value: 'account' },
  { label: 'pages_operation_internalLetterManagement_title', value: 'title' }
] as const;

// Status options for filters

export enum MailStatus {
  WITHDRAWN = 3,
  ACTIVE = 1
}

export enum MailStatusFilter {
  ALL = 'all',
  WITHDRAWN = MailStatus.WITHDRAWN,
  ACTIVE = MailStatus.ACTIVE
}

export enum MailReadStatus {
  UNREAD = 0,
  READ = 1
}

export enum MailReadStatusFilter {
  ALL = 'all',
  UNREAD = MailReadStatus.UNREAD,
  READ = MailReadStatus.READ
}

export enum MailReceiveStatusFilter {
  ALL = 'all',
  NOT_RECEIVED = 0,
  RECEIVED = 1
}
export interface MailTemplate {
  id: number;
  templateId: string;
  title: string;
  content: string;
  description: string;
  category: number;
  categoryLabel: string;
  type: number;
  typeLabel: string;
  createdBy: string;
  createdAt: number;
  updatedAt: number;
}

export interface MailTemplateWithContentMapping extends MailTemplate {
  contentMapping?: {
    [key: string]: {
      title: string;
      content: string;
    };
  };
}

export interface MailVariable {
  label: string;
  key: string;
}

export interface MailTemplateSearchParams {
  page?: number;
  limit?: number;
  title?: string;
  category?: number | 'all';
  type?: number | 'all';
}

export enum MailTemplateAddingType {
  ADD = 'add',
  EDIT = 'edit',
  COPY = 'copy',
  NONE = 'none'
}
