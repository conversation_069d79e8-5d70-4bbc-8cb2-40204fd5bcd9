export interface GiftSettingVip {
  id: number;
  reserveAmount: string;
  minGiftAmount: string;
  giftTimes: number;
  giftQuotas: string;
  giftFeeRate: string;
}

export interface GiftSetting {
  receiveGiftVipLevel: number;
  giveGiftVipLevel: number;
  receiveGiftTimeLimit: number;
  receiveGiftFrozenRemainTimeLimit: number;
  vipSetting: GiftSettingVip[];
}

export enum GiftStatusFilter {
  INCOMPLETE = 'incomplete',
  COMPLETE = 'complete'
}

// Gift Status interface for API response
export interface GiftStatus {
  id: string;
  label: string;
}

// Incomplete Gift Order interface based on API documentation
export interface IncompleteGiftOrder {
  id: string;
  playerId: number;
  playerAccount: string;
  playerName: string;
  targetId: number;
  targetAccount: string;
  targetName: string;
  item: string;
  todayTimes: number;
  totalTimes: number;
  status: number;
  createdAt: number;
  updatedAt: number;
  updatedBy: string;
}

// Completed Gift Order interface based on API documentation
export interface CompletedGiftOrder {
  id: string;
  playerId: number;
  playerAccount: string;
  playerName: string;
  targetId: number;
  targetAccount: string;
  targetName: string;
  item: string;
  todayTimes: number;
  totalTimes: number;
  status: number;
  note: {
    zhTw: string;
  };
  createdAt: number;
  updatedAt: number;
  updatedBy: string;
}

// Search parameters for gift orders API
export interface GiftOrderSearchParams {
  timeFilter?: 'updated_at' | 'created_at';
  timeStart?: number;
  timeEnd?: number;
  playerAccount?: string;
  targetAccount?: string;
  giftId?: string;
  status?: string;
  excludeTest?: 0 | 1;
  page: number;
  limit: number;
  orderBy?: string;
  sortBy?: 'asc' | 'desc';
}

// Search parameters for completed gift orders API
export interface CompletedGiftOrderSearchParams {
  timeFilter?: 'updated_at' | 'created_at';
  timeStart?: number;
  timeEnd?: number;
  playerAccount?: string;
  targetAccount?: string;
  giftId?: string;
  status?: string;
  excludeTest?: 0 | 1;
  page: number;
  limit: number;
  orderBy?: string;
  sortBy?: 'asc' | 'desc';
}

export type GiftQuerySearchFormValues = {
  account: string;
};

// Gift Quota Check API Types
export interface GiftQuotaPlayerInfo {
  name: string;
  level: number;
  remainFrozen: string;
  tags: string[];
}

export interface GiftQuotaGiftInfo {
  todayTimes: number;
  todayAmount: number;
  levelTimes: number;
  levelAmount: string;
}

export enum FrozenStatus {
  FROZEN = 0,
  RELEASED = 1,
  MANUAL_RELEASED = 3
}

export interface GiftQuotaFrozenInfo {
  amount: number;
  createdAt: number;
  id: string;
  releasedAt: number;
  status: FrozenStatus;
  statusLabel: string;
  title: string;
  type: string;
  updatedAt: number;
}

export interface GiftQuotaCheckResponse {
  playerInfo: GiftQuotaPlayerInfo;
  giftInfo: GiftQuotaGiftInfo;
  frozenInfo: GiftQuotaFrozenInfo[];
}
