import { message } from 'antd';
import copyToClipboard from 'copy-to-clipboard';
import { useState } from 'react';

import RButton from '@/components/RButton';
import RModal from '@/components/RModal';

export const useAccountDialog = () => {
  const { t } = useTranslation();
  const [visible, setVisible] = useState(false);
  const [accountInfo, setAccountInfo] = useState({ account: '', password: '' });

  const showAccountDialog = ({ account, password }: { account: string; password: string }) => {
    setAccountInfo({ account, password });
    setVisible(true);
  };

  const handleCopy = () => {
    copyToClipboard(
      `${t('login_label_account')}: ${accountInfo.account}\n${t('common_default_password')}: ${accountInfo.password}`
    );
    message.success(t('hooks_useCopy_copySuccess'));
  };

  const AccountDialog = (
    <RModal
      open={visible}
      onCancel={() => setVisible(false)}
      footer={
        <RButton
          type="default"
          color="default"
          className="!w-30 !text-text-secondary"
          onClick={handleCopy}
        >
          {t('common_copy')}
        </RButton>
      }
      title={t('common_add_success')}
      centered={true} // 依需求調整
      closable
      width={360}
      height={270}
    >
      <div>
        <p>{t('common_add_success_description', { name: t('login_label_account') })}</p>
        <div>
          <div className="my-5">
            <span className="text-text-placeholder">{t('login_label_account')}</span>
            <div className="text-text mt-2">{accountInfo.account}</div>
          </div>
          <div>
            <span className="text-text-placeholder">{t('common_default_password')}</span>
            <div className="text-text mt-2">{accountInfo.password}</div>
          </div>
        </div>
      </div>
    </RModal>
  );

  return {
    showAccountDialog,
    AccountDialog
  };
};
