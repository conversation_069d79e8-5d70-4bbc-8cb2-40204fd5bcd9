import useConfirmModal from './useConfirmModal';

const useActions = () => {
  const { t } = useTranslation();
  const { confirmModal } = useConfirmModal();

  const handleDelete = (title: string, onOk: () => void) => {
    confirmModal({
      content: t('common_confirm_delete_name', { name: title }),
      onOk: () => {
        onOk();
      }
    });
  };

  const handleEditStatus = (title: string, status: number, onOk: () => void) => {
    confirmModal({
      content:
        status === 1
          ? t('common_confirm_disable_name', { name: title })
          : t('common_confirm_active_name', { name: title }),
      onOk: () => {
        onOk();
      }
    });
  };

  return { confirmModal, handleDelete, handleEditStatus };
};

export default useActions;
