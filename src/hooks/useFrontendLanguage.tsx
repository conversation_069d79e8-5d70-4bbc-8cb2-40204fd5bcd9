import { useQuery } from '@tanstack/react-query';

import { getLanguageList } from '@/api/common';
import { snakeToCamel } from '@/utils/object';

const DEFAULT_LANGUAGE = 'zhTw';

export const languageOptions = [
  { label: 'components_language_en', value: 'en' },
  { label: 'components_language_ja', value: 'ja' },
  { label: 'components_language_ko', value: 'ko' },
  { label: 'components_language_zh_tw', value: 'zhTw' }
];

export const languageOptionsLabelByValue = languageOptions.reduce(
  (acc, cur) => {
    acc[cur.value] = cur.label;
    return acc;
  },
  {} as Record<string, string>
);

const useFrontendLanguage = () => {
  const { t } = useTranslation();

  const { data } = useQuery({
    queryKey: ['frontendLanguage'],
    queryFn: () => getLanguageList(),
    select: (data) => {
      return {
        default: (data?.data && snakeToCamel(data.data?.default)) ?? DEFAULT_LANGUAGE,
        list: data?.data?.list.map((language) => snakeToCamel(language)) || []
      };
    },
    staleTime: 24 * 60 * 60 * 1000,
    gcTime: 24 * 60 * 60 * 1000
  });

  const languageListOptions =
    data?.list.map((language) => {
      const languageOption = languageOptions.find((option) => option.value === language);
      return {
        label: t(languageOption?.label || ''),
        value: language
      };
    }) || [];

  return {
    frontendLanguageList: data?.list || [DEFAULT_LANGUAGE],
    defaultFrontendLanguage: data?.default || DEFAULT_LANGUAGE,
    languageListOptions
  };
};

export default useFrontendLanguage;
