import { TFunction } from 'i18next';
import { useLocation } from 'react-router';

import { permissionsGroup } from '@/api/auth';
import { defaultRoutes } from '@/router/defaultRoutes';
import { useUserStore } from '@/store/userStore';
import { getRouteName } from '@/utils/routeUtils';

const getPermissionRoute = (permissions: permissionsGroup[], pathNames: string[]) => {
  const parentRoute = Object.keys(defaultRoutes)?.find((route) => route === pathNames[0]) ?? '';
  const childRoute = defaultRoutes?.[parentRoute]?.routes.find(
    (route) => route.pageName === pathNames[1]
  );

  const parentRoutePermission = permissions?.find(
    (permission) => permission.name === defaultRoutes[parentRoute]?.permissionId
  );

  if (!parentRoutePermission) {
    return {
      parentRoutePermission: null,
      childRoutePermission: null
    };
  }

  const childRoutePermission = parentRoutePermission?.permissions?.find(
    (permission) => permission.name === childRoute?.permissionId
  );

  return { parentRoutePermission, childRoutePermission };
};

const getRouteNameMap = (permissions: permissionsGroup[], t: TFunction) => {
  const routeNameMap: Record<string, string> = {};
  permissions?.forEach((permission) => {
    const parentRoute = Object.entries(defaultRoutes)?.find(
      (route) => route[1].permissionId === permission.name
    );
    permission?.permissions?.forEach((childPermission) => {
      const parentPathName = parentRoute?.[0];
      const childPathName = parentRoute?.[1]?.routes?.find(
        (route) => route.permissionId === childPermission.name
      )?.pageName;

      routeNameMap[`/${parentPathName}/${childPathName}`] =
        childPermission.label || getRouteName(location.pathname, t);
    });
  });
  return routeNameMap;
};

const useRoute = () => {
  const { t } = useTranslation();
  const permissions = useUserStore((state) => state.userInfo.permissions);
  const location = useLocation();

  const pathNames = location.pathname.split('/').slice(1);

  const getCurrentPermissionRoute = () => {
    const { parentRoutePermission, childRoutePermission } = getPermissionRoute(
      permissions,
      pathNames
    );

    return { parentRoutePermission, childRoutePermission };
  };

  const currentRoute = getCurrentPermissionRoute().childRoutePermission;
  const routeNameMap = useMemo(() => getRouteNameMap(permissions, t), [permissions, t]);

  const currentRouteName = currentRoute?.label || getRouteName(location.pathname, t);

  return { currentRoute, routeNameMap, currentRouteName };
};

export default useRoute;
