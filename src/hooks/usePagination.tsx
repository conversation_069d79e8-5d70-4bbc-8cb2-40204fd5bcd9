const usePagination = ({ defaultLimit = 10 }) => {
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(defaultLimit);
  const [total, setTotal] = useState(0);

  const handleChangePage = (page: number, pageSize: number) => {
    setPage(page);
    setLimit(pageSize);
  };

  return {
    page,
    setPage,
    limit,
    setLimit,
    total,
    setTotal,
    handleChangePage
  };
};

export default usePagination;
