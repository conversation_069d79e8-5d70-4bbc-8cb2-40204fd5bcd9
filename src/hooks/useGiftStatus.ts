import { useQuery } from '@tanstack/react-query';

import { getGiftStatus } from '@/api/gift';
import { GiftStatusFilter } from '@/types/gift';

// Query keys for gift status
export const giftStatusKeys = {
  all: ['giftStatus'] as const,
  lists: (filter: GiftStatusFilter) => [...giftStatusKeys.all, 'list', filter] as const
};

export const useGiftStatus = (params: { filter: GiftStatusFilter }) => {
  return useQuery({
    queryKey: giftStatusKeys.lists(params.filter),
    queryFn: () => getGiftStatus(params.filter),
    select: (data) => data.data || []
  });
};
