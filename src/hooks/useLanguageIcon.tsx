// import CN from '@/assets/img/flags/zh_cn.svg?react';
import EN from '@/assets/img/flags/en_us.svg?react';
import JP from '@/assets/img/flags/ja_jp.svg?react';
import TW from '@/assets/img/flags/zh_tw.svg?react';

export const useLanguageIcon = () => {
  const languageList = [
    // {
    //   name: 'zn_cn',
    //   icon: <CN />
    // },
    {
      name: '+886',
      icon: <TW className="w-4 h-4" />
    },
    {
      name: 'EN',
      icon: <EN />
    },
    {
      name: 'JP',
      icon: <JP />
    }
  ];

  const getLanguageIcon = (name: string) => {
    return languageList.find((item) => item.name === name)?.icon;
  };

  return { getLanguageIcon };
};
