import type { MenuProps } from 'antd';

import { defaultRoutes } from '@/router/defaultRoutes';
import { usePermissionStore } from '@/store/permissionStore';

type MenuItem = Required<MenuProps>['items'][number];

function getItem(
  label: React.ReactNode,
  key: React.Key,
  icon?: React.ReactNode,
  children?: MenuItem[]
): MenuItem {
  return {
    key,
    icon,
    children,
    label
  } as MenuItem;
}

export const useMenuItems = () => {
  const { getFilteredPermissions } = usePermissionStore();
  const userPermission = getFilteredPermissions();

  return Object.entries(defaultRoutes)
    .map(([key, value]) => {
      // 查找對應的父級權限
      const parent = userPermission.find((p) => p?.name === value.permissionId);
      if (!parent) return null;

      // 處理子路由權限
      const children = value.routes
        .map((route) => {
          const child = parent.permissions?.find((p) => p.name === route.permissionId);
          return child ? getItem(child.label, route.pageName) : null;
        })
        .filter(Boolean);

      // 如果沒有有效的子項，則不顯示父級菜單
      if (children.length === 0) return null;

      return getItem(parent.label, key, value.icon, children);
    })
    .filter(Boolean) as MenuItem[];
};

// 處理菜單點擊的 hook
export const useMenuClick = () => {
  const navigate = useNavigate();

  return (key: string) => {
    const parentKey = Object.entries(defaultRoutes).find(([, value]) =>
      value.routes.some((route) => route.pageName === key)
    )?.[0];

    if (parentKey) {
      navigate(`/${parentKey.toLowerCase()}/${key}`);
    }
  };
};

// 獲取第一個可用的路由
export const getFirstAvailableRoute = (): string => {
  const routes = Object.entries(defaultRoutes);
  if (routes.length === 0) {
    return '';
  }
  const [, firstRouteConfig] = routes[0];
  if (firstRouteConfig.routes.length === 0) {
    return '';
  }
  return firstRouteConfig.routes[0].pageName;
};

// 獲取默認選中的菜單項
export const getDefaultSelectedKey = (pathname: string): string => {
  for (const [parentKey, value] of Object.entries(defaultRoutes)) {
    const matchedRoute = value.routes.find((route) =>
      pathname.includes(`/${parentKey.toLowerCase()}/${route.pageName}`)
    );
    if (matchedRoute) {
      return matchedRoute.pageName;
    }
  }

  return getFirstAvailableRoute();
};

export const getFirstParentRoute = (): string => {
  const routes = Object.keys(defaultRoutes);
  if (routes.length === 0) {
    return '';
  }
  return routes[0];
};

// 獲取默認展開的菜單項
export const getDefaultOpenKeys = (pathname: string): string[] => {
  for (const [parentKey, value] of Object.entries(defaultRoutes)) {
    if (
      value.routes.some((route) =>
        pathname.includes(`/${parentKey.toLowerCase()}/${route.pageName}`)
      )
    ) {
      return [parentKey];
    }
  }
  return [getFirstParentRoute()];
};
