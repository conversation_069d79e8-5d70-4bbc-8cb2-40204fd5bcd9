import { useEffect } from 'react';

/**
 * A reusable hook to show or hide a DOM element by its id.
 * @param elementId - The id of the DOM element to show/hide.
 * @param visible - Whether the element should be visible (true) or hidden (false).
 */

const useToggleElementVisibility = (elementId: string, visible: boolean) => {
  useEffect(() => {
    const el = document.getElementById(elementId);
    if (el) {
      el.style.display = visible ? 'block' : 'none';
    }
  }, [elementId, visible]);
};

export default useToggleElementVisibility;
