import { FormInstance } from 'antd';
import { useEffect, useState } from 'react';

import RForm from '@/components/RForm';

export interface UseEditableTableOptions {
  onCreateSuccess?: () => void;
  onUpdateSuccess?: () => void;
  onDeleteSuccess?: () => void;
  resetOnModalClose?: boolean;
  modalOpen?: boolean;
}

export interface UseEditableTableReturn<T> {
  form: FormInstance<T>;
  editingId: string | number | null;
  isAdding: boolean;
  handleAdd: () => void;
  handleEdit: (record: T & { id: string | number }) => void;
  handleCancel: () => void;
  setEditingId: (id: string | number | null) => void;
  setIsAdding: (adding: boolean) => void;
  handleCreateSuccess: () => void;
  handleUpdateSuccess: () => void;
  handleDeleteSuccess: () => void;
}

/**
 * Custom hook for managing editable table state and form operations
 * @param options Configuration options for the hook
 * @returns Object containing form instance, state variables, and handler functions
 */
export const useEditableTable = <T extends Record<string, unknown>>(
  options: UseEditableTableOptions = {}
): UseEditableTableReturn<T> => {
  const {
    onCreateSuccess,
    onUpdateSuccess,
    onDeleteSuccess,
    resetOnModalClose = false,
    modalOpen
  } = options;

  const [form] = RForm.useForm<T>();
  const [editingId, setEditingId] = useState<string | number | null>(null);
  const [isAdding, setIsAdding] = useState(false);

  const handleAdd = () => {
    setIsAdding(true);
    setEditingId(null);
    form.resetFields();
  };

  const handleEdit = (record: T & { id: string | number }) => {
    setEditingId(record.id);
    setIsAdding(false);
    // Use setTimeout to ensure form is ready for field values
    setTimeout(() => {
      form.setFieldsValue(record as never);
    });
  };

  const handleCancel = () => {
    setIsAdding(false);
    setEditingId(null);
    form.resetFields();
  };

  // Reset form state when modal closes (if enabled)
  useEffect(() => {
    if (resetOnModalClose && modalOpen === false) {
      setIsAdding(false);
      setEditingId(null);
      form.resetFields();
    }
  }, [modalOpen, form, resetOnModalClose]);

  // Success handlers that can be called by parent component
  const handleCreateSuccess = () => {
    setIsAdding(false);
    form.resetFields();
    onCreateSuccess?.();
  };

  const handleUpdateSuccess = () => {
    setEditingId(null);
    form.resetFields();
    onUpdateSuccess?.();
  };

  const handleDeleteSuccess = () => {
    onDeleteSuccess?.();
  };

  return {
    form,
    editingId,
    isAdding,
    handleAdd,
    handleEdit,
    handleCancel,
    setEditingId,
    setIsAdding,
    handleCreateSuccess,
    handleUpdateSuccess,
    handleDeleteSuccess
  };
};

export default useEditableTable;
