import { RefObject } from 'react';

type UseClickOutsideProps = {
  ref: RefObject<HTMLElement>;
  excludeSelector?: string;
  onClickOutside: () => void;
  enabled?: boolean;
};

const useClickOutside = ({
  ref,
  excludeSelector,
  onClickOutside,
  enabled = true
}: UseClickOutsideProps) => {
  useEffect(() => {
    if (!enabled) return;

    const handleClickOutside = (event: MouseEvent) => {
      const isOutsideRef = ref.current && !ref.current.contains(event.target as Node);
      const isOnExcludedElement = excludeSelector
        ? !!(event.target as Element)?.closest(excludeSelector)
        : false;

      if (isOutsideRef && !isOnExcludedElement) {
        onClickOutside();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [ref, excludeSelector, onClickOutside, enabled]);
};

export default useClickOutside;
