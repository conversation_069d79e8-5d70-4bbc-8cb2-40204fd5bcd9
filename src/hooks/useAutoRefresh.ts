import { useCallback, useEffect, useRef, useState } from 'react';

export interface AutoRefreshOptions {
  enabled?: boolean;
  interval: number; // in milliseconds
  onRefresh?: () => void;
  pauseOnUserInteraction?: boolean;
}

export interface AutoRefreshReturn {
  isEnabled: boolean;
  interval: number;
  timeRemaining: number;
  setInterval: (interval: number) => void;
  refresh: () => void;
}

export const useAutoRefresh = ({
  enabled = false,
  interval,
  onRefresh
}: AutoRefreshOptions): AutoRefreshReturn => {
  const [currentInterval, setCurrentInterval] = useState(interval);
  const [timeRemaining, setTimeRemaining] = useState(interval);

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const countdownRef = useRef<NodeJS.Timeout | null>(null);

  const clearTimers = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    if (countdownRef.current) {
      clearInterval(countdownRef.current);
      countdownRef.current = null;
    }
  }, []);

  const startCountdown = useCallback(() => {
    setTimeRemaining(currentInterval);
    countdownRef.current = setInterval(() => {
      setTimeRemaining((prev) => {
        if (prev <= 1000) {
          return currentInterval;
        }
        return prev - 1000;
      });
    }, 1000);
  }, [currentInterval]);

  const refresh = useCallback(() => {
    onRefresh?.();
    setTimeRemaining(currentInterval);
  }, [onRefresh, currentInterval]);

  const startAutoRefresh = useCallback(() => {
    clearTimers();
    if (enabled) {
      intervalRef.current = setInterval(refresh, currentInterval);
      startCountdown();
    }
  }, [enabled, currentInterval, refresh, clearTimers, startCountdown]);

  const setIntervalValue = useCallback((newInterval: number) => {
    setCurrentInterval(newInterval);
    setTimeRemaining(newInterval);
  }, []);

  // Start/stop auto refresh based on enabled state
  useEffect(() => {
    startAutoRefresh();
    return clearTimers;
  }, [startAutoRefresh, clearTimers]);

  // Cleanup on unmount
  useEffect(() => {
    return clearTimers;
  }, [clearTimers]);

  return {
    isEnabled: enabled,
    interval: currentInterval,
    timeRemaining,
    setInterval: setIntervalValue,
    refresh
  };
};

export default useAutoRefresh;
