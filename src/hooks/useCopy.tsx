import { useTranslation } from 'react-i18next';

import useAntdApp from '@/hooks/useAntdApp';

export const useCopy = (text: string, showMessage = true) => {
  const { t } = useTranslation();
  const { message } = useAntdApp();
  const renderMessage = () => {
    message.success({
      content: t('hooks_useCopy_copySuccess'),
      key: 'copySuccess'
    });
  };
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(text);
      if (showMessage) {
        renderMessage();
      }
    } catch {
      const oInput = document.createElement('input');
      oInput.value = text;
      document.body.appendChild(oInput);
      oInput.select();
      document.execCommand('Copy');
      oInput.className = 'oInput';
      if (oInput.parentNode) {
        oInput.parentNode.removeChild(oInput);
      }
      if (showMessage) {
        renderMessage();
      }
    }
  };
  return copyToClipboard;
};
