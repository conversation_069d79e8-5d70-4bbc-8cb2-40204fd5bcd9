import { useCallback, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useBlocker, useNavigate } from 'react-router-dom';

import useConfirmModal from './useConfirmModal';

/**
 * Hook for handling navigation blocking when form has unsaved changes
 *
 * 功能：
 * 1. 攔截 React Router 導航（側邊欄點擊、瀏覽器前進後退等）
 * 2. 攔截頁面離開事件（beforeunload - 關閉標籤頁、刷新頁面等）
 * 3. 顯示確認對話框讓用戶選擇是否離開
 * 4. 防止重複顯示對話框
 * 5. 自動清理狀態
 */

interface UseNavigationBlockerOptions {
  /** 表單是否有未保存的更改 */
  isFormChanged: boolean;
  /** 是否處於編輯模式 */
  isEditing: boolean;
  /** 用戶確認導航時的回調函數 */
  onConfirmNavigation?: () => void;
  /** 用戶取消導航時的回調函數 */
  onCancelNavigation?: () => void;
}

interface UseNavigationBlockerReturn {
  /** Modal是否已顯示 */
  isModalShown: React.MutableRefObject<boolean>;
  /** 是否正在導航 */
  isNavigating: React.MutableRefObject<boolean>;
}

export const useNavigationBlocker = ({
  isFormChanged,
  isEditing,
  onConfirmNavigation,
  onCancelNavigation
}: UseNavigationBlockerOptions): UseNavigationBlockerReturn => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { confirmModal } = useConfirmModal();
  const isModalShown = useRef(false);
  const isNavigating = useRef(false);

  // 使用 useCallback 穩定 blocker 函數
  const blockerFn = useCallback(
    ({ currentLocation, nextLocation }: { currentLocation: any; nextLocation: any }) => {
      const shouldBlock =
        isFormChanged &&
        isEditing &&
        currentLocation.pathname !== nextLocation.pathname &&
        !isNavigating.current;
      return shouldBlock;
    },
    [isFormChanged, isEditing, isNavigating]
  );

  // 導航攔截器
  const blocker = useBlocker(blockerFn);

  // 處理導航攔截
  useEffect(() => {
    if (blocker.state === 'blocked' && !isModalShown.current && !isNavigating.current) {
      const nextPath = blocker.location.pathname;
      isModalShown.current = true;

      confirmModal({
        title: t('common_alert'),
        content: `${t('pages_platform_modal_content')}，${t('pages_platform_model_comfirm')}？`,
        onOk: () => {
          isModalShown.current = false;
          isNavigating.current = true; // 設置導航標記

          onConfirmNavigation?.();

          // 先重置 blocker 狀態，再執行導航
          blocker.reset?.();
          navigate(nextPath, { replace: true });
        },
        onCancel: () => {
          isModalShown.current = false;
          isNavigating.current = false;

          // 調用取消導航回調
          onCancelNavigation?.();

          // 手動重置 blocker 狀態
          blocker.reset?.();
        }
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [blocker.state, blocker.reset, navigate, t, onConfirmNavigation, onCancelNavigation]);

  // 處理導航取消
  useEffect(() => {
    if (blocker.state === 'unblocked' && isModalShown.current) {
      isModalShown.current = false;
      isNavigating.current = false;
    }
  }, [blocker.state]);

  // 組件卸載時的清理
  useEffect(() => {
    return () => {
      isModalShown.current = false;
      isNavigating.current = false;
    };
  }, []);

  // 處理頁面離開事件 (beforeunload)
  useEffect(() => {
    if (!isEditing) return;

    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (isFormChanged) {
        e.preventDefault();
        e.returnValue = '';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [isEditing, isFormChanged]);

  return {
    isModalShown,
    isNavigating
  };
};

export default useNavigationBlocker;
