import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Spin } from 'antd';
import { useEffect } from 'react';

import { editPlayerPermission, getAllPlayerPermission, getPlayerPermission } from '@/api/player';
import FormModal from '@/components/FormModal';
import { PermissionSelect } from '@/components/PermissionSelect';
import RForm from '@/components/RForm';

export type FormValue = {
  id?: number;
  permissions: number[];
};

export const PlayerPermissionModal = ({
  open,
  onOk,
  onClose,
  id
}: {
  open: boolean;
  onOk?: () => void;
  onClose: () => void;
  id: number;
}) => {
  const { t } = useTranslation();
  const [form] = RForm.useForm<FormValue>();
  const queryClient = useQueryClient();

  // 取得所有玩家權限
  const { data: permissionsData } = useQuery({
    queryKey: ['getAllPlayerPermission'],
    queryFn: () => getAllPlayerPermission({ groupType: 1 })
  });

  // 取得該玩家的權限
  const { data: playerPermissions, isPending: isPlayerPermissionsPending } = useQuery({
    queryKey: ['getPlayerPermission', id],
    queryFn: () => {
      if (!id) {
        throw new Error('Player ID is required');
      }
      return getPlayerPermission({ id: id });
    },
    enabled: !!id && open
  });

  const handleClose = () => {
    form.resetFields();
    onClose();
  };

  const { mutate: editMutation, isPending: isEditPending } = useMutation({
    mutationFn: editPlayerPermission,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['playerPermissionList'] });
      onOk?.();
      handleClose();
    }
  });

  const handleSubmit = (values: FormValue) => {
    if (!values.id) return;
    editMutation({ id: values.id, permissions: values.permissions });
  };

  // 當玩家權限數據加載完成時更新表單的權限值
  useEffect(() => {
    if (open && id && playerPermissions?.data && !isPlayerPermissionsPending) {
      form.setFieldsValue({
        id: id,
        permissions: playerPermissions.data.permissions?.map((permission) => permission.id) ?? []
      });
    }
  }, [playerPermissions?.data, isPlayerPermissionsPending, form, open, id]);

  return (
    <FormModal
      title={t('common_edit_name', {
        name: t('common_permission')
      })}
      form={form}
      open={open}
      onClose={handleClose}
      onSubmit={handleSubmit}
      isLoading={isEditPending}
    >
      <Spin spinning={isPlayerPermissionsPending}>
        <div className="flex flex-col gap-2">
          <RForm.Item name="id" noStyle></RForm.Item>
          <RForm.Item
            label={
              <span className="text-text-secondary">
                {`${t('pages_player')}${t('common_account')} / ${t('pages_player_nickname')}`}
              </span>
            }
            name="name"
          >
            <span>
              {playerPermissions?.data?.account} / {playerPermissions?.data?.name}
            </span>
          </RForm.Item>
          <RForm.Item name="permissions">
            <PermissionSelect data={permissionsData?.data ?? []} />
          </RForm.Item>
        </div>
      </Spin>
    </FormModal>
  );
};

export default PlayerPermissionModal;
