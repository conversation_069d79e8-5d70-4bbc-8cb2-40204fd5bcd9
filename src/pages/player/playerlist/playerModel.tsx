import { useQuery } from '@tanstack/react-query';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import type { RuleObject } from 'antd/es/form';

import {
  checkAccount,
  checkName,
  createPlayer,
  editPlayer,
  getPlayerTagListOption
} from '@/api/player';
import { getVipList } from '@/api/vip';
import CopyIcon from '@/components/CopyIcon';
import FormModal from '@/components/FormModal';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RSelect from '@/components/RSelect';
import RSwitch from '@/components/RSwitch';
import { generatePasswordWithPattern } from '@/utils/randomText';
export type FormValue = {
  id?: number;
  accountType: number;
  refAccount?: string; // 上級帳號
  account: string;
  name: string;
  password?: string;
  countryCode?: string;
  phone?: string;
  level: number;
  tags: number[];
  playerTag?: string;
  status: 1 | 0;
};

export const PlayerModal = ({
  open,
  onOk,
  onClose,
  initialValues,
  showAccountDialog
}: {
  open: boolean;
  onOk?: () => void;
  onClose: () => void;
  initialValues?: FormValue;
  showAccountDialog: ({ account, password }: { account: string; password: string }) => void;
}) => {
  const { t } = useTranslation();
  const [form] = RForm.useForm<FormValue>();
  const [password, setPassword] = useState(generatePasswordWithPattern(3, 4));
  const queryClient = useQueryClient();
  const isEdit = !!initialValues;
  const { data: tagList } = useQuery({
    queryKey: ['tagList'],
    queryFn: getPlayerTagListOption
  });
  const { data: vipList } = useQuery({
    queryKey: ['vipList'],
    queryFn: () => getVipList({ status: 1 })
  });

  const { mutate: createPlayerMutate, isPending: isCreatePlayerPending } = useMutation({
    mutationFn: createPlayer,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['playerList'] });
      onOk?.();
      const accountInfo = {
        account: form.getFieldValue('account'),
        password: password
      };
      handleClose();
      showAccountDialog({
        account: accountInfo.account,
        password: accountInfo.password
      });
    }
  });

  const { mutate: editPlayerMutate, isPending: iseditPlayerPending } = useMutation({
    mutationFn: editPlayer,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['playerList'] });
      queryClient.invalidateQueries({ queryKey: ['getPlayerVipChangeLog'] });
      onOk?.();
      handleClose();
    }
  });

  const { mutateAsync: checkAccountMutation } = useMutation({
    mutationFn: (account: string) => checkAccount({ account })
  });

  const { mutateAsync: checkNicknameMutation } = useMutation({
    mutationFn: (name: string) => checkName({ name })
  });

  const handleClose = () => {
    form.resetFields();
    onClose();
  };

  useEffect(() => {
    if (open && !isEdit) {
      setPassword(generatePasswordWithPattern(3, 4));
    }
  }, [open, isEdit]);

  const handleSubmit = (values: FormValue) => {
    if (isEdit) {
      editPlayerMutate({ ...values, playerId: values.id! });
    } else {
      createPlayerMutate({ ...values, password });
    }
  };

  const isLoading = isCreatePlayerPending || iseditPlayerPending;

  const validateAccount = async (_rule: RuleObject, value: string) => {
    if (!value) return Promise.resolve();

    try {
      const response = await checkAccountMutation(value);
      if (response.data) {
        return Promise.reject();
      }
      return Promise.resolve();
    } catch {
      return Promise.reject(t('pages_player_account_exist'));
    }
  };

  const validateNickname = async (_rule: RuleObject, value: string) => {
    if (!value) return Promise.resolve();

    if (isEdit && initialValues?.name === value) {
      return Promise.resolve();
    }

    try {
      const response = await checkNicknameMutation(value);
      if (response.data) {
        return Promise.reject();
      }
      return Promise.resolve();
    } catch {
      return Promise.reject(t('pages_player_account_exist'));
    }
  };

  return (
    <FormModal<FormValue>
      form={form}
      title={t(isEdit ? 'common_edit_name' : 'common_add_name', { name: t('pages_player') })}
      open={open}
      onClose={handleClose}
      onSubmit={handleSubmit}
      isLoading={isLoading}
      formProps={{
        layout: 'vertical',
        className: 'grid gap-y-2 gap-x-15 grid-cols-2'
      }}
      width={780}
      initialValues={initialValues || { status: 1 }}
    >
      <RForm.Item name="id" noStyle></RForm.Item>
      <RForm.Item
        label={t('pages_player_accountType')}
        name="accountType"
        rules={[{ required: true }]}
      >
        <RSelect
          options={[
            { label: t('pages_player_accountType_official'), value: 1 },
            { label: t('pages_player_accountType_test'), value: 2 }
          ]}
          disabled={isEdit}
        />
      </RForm.Item>
      <RForm.Item label={t('pages_player_parentAccount')} name="refAccount">
        <RInput disabled={isEdit} />
      </RForm.Item>
      <RForm.Item
        label={t('common_account')}
        name="account"
        validateTrigger="onBlur"
        rules={[
          { required: true },
          {
            pattern: /^[a-zA-Z0-9]{6,20}$/,
            message: t('login_reset_password_placeholder_password')
          },
          { validator: isEdit ? () => Promise.resolve() : validateAccount }
        ]}
      >
        <RInput disabled={isEdit} />
      </RForm.Item>
      <RForm.Item
        label={t('pages_player_nickname')}
        name="name"
        validateTrigger="onBlur"
        rules={[
          { required: true },
          {
            pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{4,20}$/,
            message: t('pages_player_nickname_error')
          },
          { validator: validateNickname }
        ]}
      >
        <RInput />
      </RForm.Item>
      {!isEdit && (
        <RForm.Item label={t('pages_admin_password')}>
          <div className="flex gap-2 items-center">
            {password}
            <CopyIcon text={password} />
          </div>
        </RForm.Item>
      )}
      <RForm.Item
        label={
          <div className="flex justify-between items-center gap-63">
            <span className="block flex-1">{t('pages_player_phone')}</span>
            <a onClick={() => form.setFieldsValue({ phone: '', countryCode: '' })}>
              {t('common_clear')}
            </a>
          </div>
        }
      >
        <div className="flex gap-2">
          <RForm.Item name="countryCode" noStyle>
            <RSelect
              options={[{ label: '+886', value: '+886' }]}
              style={{ width: 80, height: 36 }}
            />
          </RForm.Item>
          <RForm.Item
            name="phone"
            noStyle
            rules={[{ pattern: /^9\d{8}$/, message: t('pages_player_phone_error') }]}
          >
            <RInput />
          </RForm.Item>
        </div>
      </RForm.Item>
      <RForm.Item label={t('pages_player_vipLevel')} name="level" rules={[{ required: true }]}>
        <RSelect
          options={
            vipList?.data
              ?.filter((vip) => vip.status === 1)
              .map((vip) => ({ label: vip.name, value: vip.id })) || []
          }
        />
      </RForm.Item>
      <RForm.Item label={t('pages_player_playerTag')} name="tags">
        <RSelect
          mode="multiple"
          options={tagList?.data?.map((tag) => ({ label: tag.name, value: tag.id })) || []}
        />
      </RForm.Item>
      <RForm.Item label={t('common_status')} name="status" valuePropName="checked">
        <RSwitch onChange={(checked) => form.setFieldsValue({ status: checked ? 1 : 0 })} />
      </RForm.Item>
    </FormModal>
  );
};

export default PlayerModal;
