import { useQuery } from '@tanstack/react-query';

import { getPlayerVipChangeLog } from '@/api/player';
import RButton from '@/components/RButton';
import RModal from '@/components/RModal';
import RTable from '@/components/RTable';
import { formatTime } from '@/utils/time';

interface VipChangeLogProps {
  open: boolean;
  onClose: () => void;
  id: number;
  account: string;
}

const VipChangeLogModal = ({ open, onClose, id, account }: VipChangeLogProps) => {
  const { t } = useTranslation();

  const { data, isLoading } = useQuery({
    queryKey: ['getPlayerVipChangeLog', id],
    queryFn: () => getPlayerVipChangeLog({ id }),
    enabled: !!id && open
    // staleTime: 0,
    // refetchOnWindowFocus: false
  });

  const columns = [
    {
      title: t('common_level'),
      dataIndex: 'afterLevel',
      key: 'afterLevel',
      render: (afterLevel: number) => {
        return (
          <div className="flex items-center gap-1">
            {afterLevel ? <span className="truncate">VIP{afterLevel}</span> : '-'}
          </div>
        );
      }
    },
    {
      title: t('common_operator'),
      dataIndex: 'createdBy',
      key: 'createdBy',
      render: (createdBy: string) => {
        return <span>{createdBy || '-'}</span>;
      }
    },
    {
      title: t('common_addTime'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (time: number) => {
        return <span>{time ? formatTime(time) : '-'}</span>;
      }
    }
  ];

  const vipChangeLogData = data?.data ? (Array.isArray(data.data) ? data.data : [data.data]) : [];

  return (
    <RModal
      open={open}
      onCancel={onClose}
      title={t('pages_player_vipRecord')}
      footer={null}
      width={780}
    >
      <div className="mx-2 mb-3.5 flex items-center gap-2.5">
        <span className="text-text-placeholder">{t('login_label_account')}:</span>
        {account}
      </div>
      <h3 className="mx-2 mb-1 text-text-placeholder">{t('common_record')}</h3>
      <div className="mx-2 bg-gray-100 rounded">
        <RTable
          loading={isLoading}
          columns={columns}
          rowKey="id"
          dataSource={vipChangeLogData}
          pagination={false}
        />
      </div>
      <div className="m-6 mt-15 w-30 mx-auto">
        <RButton type="default" className="!w-30" onClick={onClose} loading={isLoading}>
          {t('common_close')}
        </RButton>
      </div>
    </RModal>
  );
};

export default VipChangeLogModal;
