import { useQuery } from '@tanstack/react-query';

import { getPlayerByPhone } from '@/api/player';
import CopyIcon from '@/components/CopyIcon';
import RButton from '@/components/RButton';
import RModal from '@/components/RModal';
import RTable from '@/components/RTable';
import { useLanguageIcon } from '@/hooks/useLanguageIcon';

interface GetPlayerByPhoneProps {
  phone?: string;
  countryCode?: string;
  open: boolean;
  onClose: () => void;
}

const GetPlayerByPhone = ({
  phone,
  countryCode,

  open,
  onClose
}: GetPlayerByPhoneProps) => {
  const { t } = useTranslation();

  const { data, isLoading } = useQuery({
    queryKey: ['getPlayerByPhone', phone, countryCode],
    queryFn: () =>
      phone && countryCode ? getPlayerByPhone({ phone, countryCode }) : Promise.resolve(null),
    enabled: !!phone && !!countryCode
  });
  // console.log(data);

  const columns = [
    {
      title: t('login_label_account'),
      dataIndex: 'account',
      key: 'account',
      render: (account: string) => {
        return (
          <div className="flex items-center gap-1">
            <CopyIcon text={account} width={12} height={12} />
            <span className="truncate">{account}</span>
          </div>
        );
      }
    },
    {
      title: t('pages_player_nickname'),
      dataIndex: 'name',
      key: 'name',
      render: (name: string) => {
        return (
          <div className="flex items-center gap-1">
            <CopyIcon text={name} width={12} height={12} />
            <span className="truncate">{name}</span>
          </div>
        );
      }
    },
    {
      title: t('pages_player_mainPointRemain'),
      dataIndex: 'remain',
      key: 'remain',
      render: (remain: number) => {
        const num = Number(remain);
        return <span>{isNaN(num) ? '-' : num.toFixed(0)}</span>;
      }
    }
  ];

  const playerData = data?.data ? (Array.isArray(data.data) ? data.data : [data.data]) : [];
  const { getLanguageIcon } = useLanguageIcon();

  return (
    <RModal
      open={open}
      onCancel={onClose}
      title={t('pages_player_getPlayerByPhone')}
      footer={null}
      width={780}
    >
      <div className="mx-2 mb-4 flex items-center gap-2.5">
        <span className="text-text-placeholder">{t('pages_player_phone')}:</span>
        {countryCode && getLanguageIcon(countryCode)}
        {countryCode}
        {phone}
      </div>
      <h3 className="mx-2 mb-1 text-text-placeholder">{t('pages_player_bindPlayerList')}</h3>
      <div className="mx-2 bg-gray-100 rounded">
        <RTable
          loading={isLoading}
          columns={columns}
          rowKey="id"
          dataSource={playerData}
          pagination={{
            hideOnSinglePage: true,
            pageSize: playerData.length,
            current: 1,
            showSizeChanger: false
          }}
        />
      </div>
      <div className="m-6 mt-15 w-30 mx-auto">
        <RButton type="default" className="!w-30" onClick={onClose} loading={isLoading}>
          {t('common_close')}
        </RButton>
      </div>
    </RModal>
  );
};

export default GetPlayerByPhone;
