import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Spin } from 'antd';
import { RuleObject } from 'antd/es/form';

import {
  checkPlayerTagName,
  createPlayerTag,
  editPlayerTag,
  getAllPlayerPermission
} from '@/api/player';
import FormModal from '@/components/FormModal';
import { PermissionSelect } from '@/components/PermissionSelect';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';

export type FormValue = {
  id?: number;
  name: string;
  permissions: number[];
};

const Block = ({ title, children }: { title: string; children: React.ReactNode }) => {
  return (
    <div className="flex flex-col gap-2">
      <div className="text-[13px] font-bold">{title}</div>
      <div>{children}</div>
    </div>
  );
};

export const PlayerTagModal = ({
  open,
  onOk,
  onClose,
  initialValues,
  isReadOnly = false
}: {
  open: boolean;
  onOk?: () => void;
  onClose: () => void;
  initialValues?: FormValue;
  isReadOnly?: boolean;
}) => {
  const { t } = useTranslation();
  const [form] = RForm.useForm<FormValue>();

  const queryClient = useQueryClient();
  const isEdit = !!initialValues?.id;

  const { data: permissionsData } = useQuery({
    queryKey: ['getAllPlayerPermission'],
    queryFn: () => getAllPlayerPermission({ groupType: 2 })
  });

  const validateName = async (_rule: RuleObject, value: string) => {
    if (!value) return Promise.resolve();

    if (isEdit && initialValues?.name === value) {
      return Promise.resolve();
    }

    try {
      const response = await checkPlayerTagName({ name: value });
      if (response.data) {
        return Promise.reject(t('pages_player_tag_exist'));
      }
      return Promise.resolve();
    } catch {
      return Promise.reject(t('pages_player_tag_exist'));
    }
  };

  const handleClose = () => {
    form.resetFields();
    onClose();
  };

  const { mutate: createMutation, isPending: isCreatePending } = useMutation({
    mutationFn: createPlayerTag,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['playerTagList'] });
      onOk?.();
      handleClose();
    }
  });

  const { mutate: editMutation, isPending: isEditPending } = useMutation({
    mutationFn: editPlayerTag,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['playerTagList'] });
      onOk?.();
      handleClose();
    }
  });

  const handleSubmit = (values: FormValue) => {
    if (isEdit) {
      editMutation({ ...values, tagId: values.id! });
    } else {
      createMutation({ ...values });
    }
  };

  useEffect(() => {
    if (open && initialValues) {
      form.setFieldsValue({ ...initialValues });
    }
  }, [open, form, initialValues]);
  const isLoading = isCreatePending || isEditPending;

  return (
    <FormModal
      title={t(isReadOnly ? 'common_read' : isEdit ? 'common_edit_name' : 'common_add_name', {
        name: t('pages_playerTag')
      })}
      form={form}
      open={open}
      onClose={handleClose}
      onSubmit={handleSubmit}
      isLoading={isLoading}
      initialValues={initialValues}
      isReadOnly={isReadOnly}
    >
      <Spin spinning={isLoading}>
        <div className="flex flex-col gap-2">
          <RForm.Item name="id" noStyle></RForm.Item>
          <Block title={t('pages_role_baseSetting')}>
            <RForm.Item
              label={
                <span className="text-text-secondary">{t('pages_player_playerTag_name')}</span>
              }
              name="name"
              validateTrigger="onBlur"
              rules={[{ required: true }, { validator: validateName }]}
            >
              <RInput className="!w-[320px]" disabled={isReadOnly} />
            </RForm.Item>
          </Block>
          <Block title={t('pages_role_permissionSetting')}>
            <RForm.Item name="permissions" rules={[{ required: true }]}>
              <PermissionSelect disabled={isReadOnly} data={permissionsData?.data ?? []} />
            </RForm.Item>
          </Block>
        </div>
      </Spin>
    </FormModal>
  );
};

export default PlayerTagModal;
