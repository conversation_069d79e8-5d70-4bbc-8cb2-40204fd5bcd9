import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { deletePlayerTag, getPlayerTagList, getTagPermission } from '@/api/player';
import RButton from '@/components/RButton';
import RTable from '@/components/RTable';
import useConfirmModal from '@/hooks/useConfirmModal';
import usePagination from '@/hooks/usePagination';
import TableSearchLayout from '@/layout/TableSearchLayout';
import { PlayerTag } from '@/types/playerlist';
import { formatTime } from '@/utils/time';

import TagModal from './TagModal';
import { FormValue } from './TagModal';

const ActionButtons = ({
  data,
  onEdit,
  onDelete
}: {
  data: PlayerTag;
  onEdit: (data: PlayerTag) => void;
  onDelete: (data: PlayerTag) => void;
}) => {
  const { t } = useTranslation();

  const handleDelete = () => {
    onDelete(data);
  };

  return (
    <div className="flex gap-1">
      <RButton
        size="small"
        variant="outlined"
        color="primary"
        type="link"
        onClick={() => onEdit(data)}
      >
        {t('common_edit')}
      </RButton>

      <RButton size="small" variant="outlined" color="red" type="link" onClick={handleDelete}>
        {t('common_delete')}
      </RButton>
    </div>
  );
};

const PlayerTagPage = () => {
  const [open, setOpen] = useState(false);
  const [isReadOnly, setIsReadOnly] = useState(false);
  const { t } = useTranslation();
  const { page, setPage, limit, setLimit } = usePagination({});
  const [initialValues, setInitialValues] = useState<FormValue | undefined>(undefined);
  const { confirmModal } = useConfirmModal();
  const [selectedTagId, setSelectedTagId] = useState<number | undefined>(undefined);

  const queryClient = useQueryClient();
  const { data, isPending } = useQuery({
    queryKey: ['playerTagList', { page, limit }],
    queryFn: () =>
      getPlayerTagList({
        page: page,
        limit: limit
      })
  });

  const { data: tagPermissions, isPending: isTagPermissionsPending } = useQuery({
    queryKey: ['tagPermissions', { id: selectedTagId }],
    queryFn: () => getTagPermission({ id: selectedTagId ?? 0 }),
    enabled: !!selectedTagId
  });

  const { mutate: deletePlayerTagMutation, isPending: isEditStatusPending } = useMutation({
    mutationFn: deletePlayerTag,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['playerTagList'] });
    }
  });

  const handleEdit = (data: PlayerTag) => {
    setIsReadOnly(false);
    setSelectedTagId(data.id);
    setInitialValues({
      id: data.id,
      name: data.name,
      permissions: []
    });
    setOpen(true);
  };

  const handleView = (data: PlayerTag) => {
    setIsReadOnly(true);
    setSelectedTagId(data.id);
    setInitialValues({
      id: data.id,
      name: data.name,
      permissions: []
    });
    setOpen(true);
  };

  // 當 tagPermissions 數據加載完成時更新 initialValues
  useEffect(() => {
    if (tagPermissions?.data && initialValues?.id && !isTagPermissionsPending) {
      setInitialValues((prev) => ({
        ...prev!,
        permissions: tagPermissions.data ?? []
      }));
    }
  }, [tagPermissions?.data, initialValues?.id, isTagPermissionsPending]);
  // console.log('Updated initialValues:', initialValues);

  const handleCloseModal = () => {
    setOpen(false);
    setInitialValues(undefined);
    setSelectedTagId(undefined);
    setIsReadOnly(false);
  };

  const openDeleteConfirm = (data: PlayerTag) => {
    if (!data.id) return;

    confirmModal({
      content: t('common_confirm_delete_name', { name: data.name }),
      onOk: () => {
        deletePlayerTagMutation({ tagId: data.id! });
      }
    });
  };

  const actionColumn = {
    title: 'common_action',
    render: (_: unknown, data: PlayerTag) => (
      <ActionButtons data={data} onEdit={handleEdit} onDelete={openDeleteConfirm} />
    )
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id'
    },
    {
      title: 'pages_player_playerTag_name',
      dataIndex: 'name'
    },
    {
      title: 'pages_playerTag_permission',
      dataIndex: 'permissions',
      render: (_: unknown, record: PlayerTag) => (
        <a type="link" onClick={() => handleView(record)}>
          {t('common_read')}
        </a>
      )
    },
    {
      title: 'pages_playerTag_operationIP',
      dataIndex: 'createdAt',
      render: (createdAt: number, record: PlayerTag) => {
        return (
          <>
            {record?.createdBy || '(-)'}/
            <br />
            {formatTime(createdAt)}
          </>
        );
      }
    },
    {
      title: 'pages_playerTag_updatedBy',
      dataIndex: 'updatedAt',
      render: (updatedAt: number, record: PlayerTag) => {
        return (
          <>
            {record?.updatedBy || '(-)'}/
            <br />
            {formatTime(updatedAt)}
          </>
        );
      }
    }
  ];

  const tableColumns = [...columns, actionColumn].map((column) => ({
    ...column,
    title: t(column.title)
  }));

  const handleChangePage = (page: number, pageSize: number) => {
    setPage(page);
    setLimit(pageSize);
  };

  return (
    <TableSearchLayout>
      <RButton
        className="mb-4"
        type="primary"
        onClick={() => {
          setIsReadOnly(false);
          setOpen(true);
        }}
      >
        {t('common_add_name', { name: t('pages_playerTag') })}
      </RButton>
      <RTable
        loading={isPending || isEditStatusPending}
        rowKey="id"
        dataSource={data?.data?.data || []}
        columns={tableColumns}
        pagination={{
          current: page,
          pageSize: limit,
          total: data?.data?.total || 0,
          showSizeChanger: true,
          onChange: handleChangePage
        }}
      />
      <TagModal
        open={open}
        initialValues={initialValues}
        onClose={handleCloseModal}
        isReadOnly={isReadOnly}
      />
    </TableSearchLayout>
  );
};

export default PlayerTagPage;
