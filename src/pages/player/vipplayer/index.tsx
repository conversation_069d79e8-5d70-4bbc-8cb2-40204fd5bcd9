import { useMutation, useQuery } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';

import { IResponse } from '@/api/services';
import { getVipList, updateVip } from '@/api/vip';
import InfoIcon from '@/assets/img/icon/info.svg?react';
import InformationIcon from '@/assets/img/icon/information.svg?react';
import RButton from '@/components/RButton';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RSwitch from '@/components/RSwitch';
import RTable from '@/components/RTable';
import RTooltip from '@/components/Rtooltip';
import StatusLabel from '@/components/StatusLabel';
import useConfirmModal from '@/hooks/useConfirmModal';
import useNavigationBlocker from '@/hooks/useNavigationBlocker';
import TableSearchLayout from '@/layout/TableSearchLayout';
import { Vip } from '@/types/vip';
import { numberFormat } from '@/utils/numbers';
import { formatTime } from '@/utils/time';

import VipLevelSettingModal from './settingVipLevelModal';

interface VipFormData {
  data: Array<{
    id: number;
    totalAccumulatedValue: number;
    totalAccumulatedBet: number;
    monthAccumulatedValue: number;
    monthAccumulatedBet: number;
    bindPhone: 0 | 1;
    firstTopup: 0 | 1;
  }>;
}

// 首先定義一個常量數組來包含所有數字段
const NUMBER_FIELDS = [
  'monthAccumulatedValue',
  'monthAccumulatedBet',
  'totalAccumulatedValue',
  'totalAccumulatedBet'
] as const;

type VipNumberField = (typeof NUMBER_FIELDS)[number];

// 資訊說明
const InfoAlert = () => {
  const { t } = useTranslation();
  return (
    <div className="text-xs text-text mb-4 bg-[#FF83171A] border border-[#FF8317] rounded w-68 py-1.5 px-2.5">
      <div className="flex items-center gap-1.5">
        <InfoIcon className="w-4.5 h-4.5 fill-[#FF8317]" />
        <span>{t('common_info')}</span>
      </div>
      <div className="my-1 ml-6 w-60">{t('pages_player_vip_levelcondition_description')}</div>
    </div>
  );
};

const VipPage = () => {
  const [state, setState] = useState({
    open: false,
    openLevelSettingModal: false,
    isFormChanged: false,
    pendingPath: null as string | null,
    modalKey: 0
  });
  const { t } = useTranslation();
  const [params] = useState({});
  const [form] = RForm.useForm();
  const { confirmModal } = useConfirmModal();

  // 使用導航攔截 hook（包含 beforeunload 事件處理）
  useNavigationBlocker({
    isFormChanged: state.isFormChanged,
    isEditing: state.open,
    onConfirmNavigation: () => {
      setState((prev) => ({
        ...prev,
        open: false,
        isFormChanged: false
      }));
    }
  });

  const { data, isPending, refetch } = useQuery<IResponse<Vip[]>>({
    queryKey: ['vipList', params],
    queryFn: () => getVipList(params)
  });

  const { mutate: updateVipMutation } = useMutation({
    mutationFn: (values: VipFormData) => updateVip(values.data),
    onSuccess: async () => {
      await refetch();
      setState((prev) => ({
        ...prev,
        open: false,
        modalKey: prev.modalKey + 1
      }));
    }
  });

  // 處理 bindPhone 變更
  const handleBindPhoneChange = useCallback(
    (idx: number, checked: boolean) => {
      const values = form.getFieldValue('data') || [];
      const newData = values.map((row: Vip, i: number) => ({
        ...row,
        bindPhone: i === idx ? (checked ? 1 : 0) : 0
      }));
      form.setFieldsValue({ data: newData });
    },
    [form]
  );

  // 處理 firstTopup 變更
  const handleFirstTopupChange = useCallback(
    (idx: number, checked: boolean) => {
      const values = form.getFieldValue('data') || [];
      const newData = values.map((row: Vip, i: number) => ({
        ...row,
        firstTopup: i === idx ? (checked ? 1 : 0) : 0
      }));
      form.setFieldsValue({ data: newData });
    },
    [form]
  );

  // 處理數字輸入變更
  const handleNumberChange = useCallback(
    (idx: number, field: VipNumberField, value: number) => {
      const values = form.getFieldValue('data') || [];
      const newData = values.map((row: Vip, i: number) => ({
        ...row,
        [field]: i === idx ? value : row[field]
      }));
      form.setFieldsValue({ data: newData });
    },
    [form]
  );

  const columns = useMemo(
    () => [
      {
        title: t('common_level'),
        dataIndex: 'name'
      },
      {
        title: t('common_status'),
        dataIndex: 'status',
        render: (status: 1 | 0) => <StatusLabel status={status} />
      },
      ...['bindPhone', 'firstTopup'].map((field) => ({
        title: (
          <div className="flex items-center gap-1">
            {t(`pages_player_vip_${field}`)}
            <RTooltip title={t(`pages_player_vip_bindPhone_tooltip`)} color="gray">
              <InformationIcon className="w-4.5 h-4.5 fill-info" />
            </RTooltip>
          </div>
        ),
        dataIndex: field,
        render: (value: 1 | 0) => (value === 1 ? t('common_yes') : '-')
      })),
      ...[
        'monthAccumulatedValue',
        'monthAccumulatedBet',
        'totalAccumulatedValue',
        'totalAccumulatedBet'
      ].map((field) => ({
        title: t(`pages_player_vip_${field}`),
        dataIndex: field,
        render: (value: number) => numberFormat(value)
      })),
      {
        title: t('pages_player_vip_editor') + '/' + t('common_lastEditTime'),
        dataIndex: 'updatedAt',
        render: (updatedAt: number, record: Vip) => (
          <>
            <div className="text-text">{record.updatedBy}/</div>
            <div className="text-text">{formatTime(updatedAt)}</div>
          </>
        )
      }
    ],
    [t]
  );

  // 可編輯表格列
  const editableColumns = useMemo(
    () => [
      {
        title: t('common_level'),
        dataIndex: 'name'
      },
      {
        title: t('common_status'),
        dataIndex: 'status',
        render: (_: unknown, __: unknown, idx: number) => (
          <>
            <RForm.Item name={['data', idx, 'id']} hidden>
              <RInput />
            </RForm.Item>
            <StatusLabel status={(data?.data ?? [])[idx]?.status} />
          </>
        )
      },
      {
        title: (
          <>
            <div className="flex items-center gap-1">
              {t('pages_player_vip_bindPhone')}
              <RTooltip title={t('pages_player_vip_bindPhone_tooltip')} color="gray">
                <InformationIcon className="w-4.5 h-4.5 fill-info" />
              </RTooltip>
            </div>
          </>
        ),
        dataIndex: 'bindPhone',
        render: (_: unknown, __: unknown, idx: number) => (
          <RForm.Item name={['data', idx, 'bindPhone']} valuePropName="checked" noStyle>
            <RSwitch
              isBoolean={false}
              disabled={idx === 0}
              onChange={(checked) => handleBindPhoneChange(idx, checked as boolean)}
            />
          </RForm.Item>
        )
      },
      {
        title: (
          <>
            <div className="flex items-center gap-1">
              {t('pages_player_vip_firstTopup')}
              <RTooltip title={t('pages_player_vip_bindPhone_tooltip')} color="gray">
                <InformationIcon className="w-4.5 h-4.5 fill-info" />
              </RTooltip>
            </div>
          </>
        ),
        dataIndex: 'firstTopup',
        render: (_: unknown, __: unknown, idx: number) => (
          <RForm.Item name={['data', idx, 'firstTopup']} valuePropName="checked" noStyle>
            <RSwitch
              isBoolean={false}
              disabled={idx === 0}
              onChange={(checked) => handleFirstTopupChange(idx, checked as boolean)}
            />
          </RForm.Item>
        )
      },
      ...NUMBER_FIELDS.map((field) => ({
        title: t(`pages_player_vip_${field}`),
        dataIndex: field,
        render: (_: unknown, __: unknown, idx: number) => (
          <RForm.Item name={['data', idx, field]} noStyle>
            <RInput
              type="number"
              min={0}
              disabled={idx === 0}
              className="max-h-[32px]"
              onChange={(e) =>
                handleNumberChange(idx, field, e.target.value ? Number(e.target.value) : 0)
              }
            />
          </RForm.Item>
        )
      })),
      {
        title: t('pages_player_vip_editor') + '/' + t('common_lastEditTime'),
        dataIndex: 'updatedAt',
        render: (_: unknown, __: unknown, idx: number) => (
          <>
            <div className="text-text">{(data?.data ?? [])[idx]?.updatedBy}/</div>
            <div className="text-text">{formatTime((data?.data ?? [])[idx]?.updatedAt)}</div>
          </>
        )
      }
    ],
    [t, data?.data, handleBindPhoneChange, handleFirstTopupChange, handleNumberChange]
  );

  // 表單處理
  const handleFinish = (values: VipFormData) => {
    confirmModal({
      title: t('common_alert'),
      content: t('pages_player_vip_notice_title'),
      onOk: () => {
        const payloadData = values.data.slice(1);
        updateVipMutation({ data: payloadData });
        setState((prev) => ({ ...prev, isFormChanged: false }));
      }
    });
  };

  const getInitialFormData = () => ({
    data: (data?.data ?? []).map((item: Vip) => ({
      id: item.id,
      totalAccumulatedValue: item.totalAccumulatedValue,
      totalAccumulatedBet: item.totalAccumulatedBet,
      monthAccumulatedValue: item.monthAccumulatedValue,
      monthAccumulatedBet: item.monthAccumulatedBet,
      bindPhone: item.bindPhone,
      firstTopup: item.firstTopup
    }))
  });

  const getVipInnitialData = () => {
    // 取得最高等級的VIP
    const highestActiveVip = (data?.data ?? [])
      .filter((vip) => vip.status === 1)
      .sort((a, b) => b.level - a.level)[0];

    return {
      id: highestActiveVip?.id ?? 1,
      name: highestActiveVip?.name ?? ''
    };
  };

  const vipLevelData = () =>
    (data?.data ?? []).map((item: Vip) => ({
      id: item.id,
      name: item.name,
      level: item.level,
      levelStatus: item.status
    }));

  return (
    <TableSearchLayout>
      {!state.open ? (
        <>
          <div className="flex items-center gap-2 mb-4">
            <RButton type="primary" onClick={() => setState((prev) => ({ ...prev, open: true }))}>
              {t('common_edit_name', { name: t('pages_player_vip_levelcondition') })}
            </RButton>
            <RButton
              type="default"
              variant="filled"
              className="!bg-green !text-white"
              onClick={() => setState((prev) => ({ ...prev, openLevelSettingModal: true }))}
            >
              {t('pages_player_vip_button_levelSetting')}
            </RButton>
          </div>
          <InfoAlert />
          <RTable loading={isPending} rowKey="id" dataSource={data?.data || []} columns={columns} />
        </>
      ) : (
        <RForm
          form={form}
          initialValues={getInitialFormData()}
          onFinish={handleFinish}
          onValuesChange={() => setState((prev) => ({ ...prev, isFormChanged: true }))}
        >
          <div className="mb-4 text-left">
            <RButton type="primary" htmlType="submit">
              {t('common_save')}
            </RButton>
            <RButton
              type="default"
              className="ml-2"
              onClick={() => {
                if (state.isFormChanged) {
                  confirmModal({
                    title: t('common_alert'),
                    content: `${t('pages_platform_modal_content')}，${t('pages_platform_model_comfirm')}？`,
                    onOk: () => setState((prev) => ({ ...prev, open: false, isFormChanged: false }))
                  });
                } else {
                  setState((prev) => ({ ...prev, open: false }));
                }
              }}
            >
              {t('common_cancel')}
            </RButton>
          </div>
          <InfoAlert />
          <RTable rowKey="id" dataSource={data?.data ?? []} columns={editableColumns} />
        </RForm>
      )}
      <VipLevelSettingModal
        open={state.openLevelSettingModal}
        onClose={() => setState((prev) => ({ ...prev, openLevelSettingModal: false }))}
        vipleveldata={vipLevelData()}
        initialValues={getVipInnitialData()}
        key={state.modalKey}
      />
    </TableSearchLayout>
  );
};

export default VipPage;
