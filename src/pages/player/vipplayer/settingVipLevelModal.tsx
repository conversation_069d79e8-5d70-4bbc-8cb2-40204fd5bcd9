import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';

import { editVipLevelSetting } from '@/api/vip';
import FormModal from '@/components/FormModal';
import RCheckbox from '@/components/RCheckbox';
import RForm from '@/components/RForm';
import RSelect from '@/components/RSelect';
export type FormValue = {
  id: number;
  info?: string[];
};

const noticeOptions = [
  { label: 'pages_player_vip_noitce_options1', value: 'scope' },
  { label: 'pages_player_vip_noitce_options2', value: 'feedback' },
  { label: 'pages_player_vip_noitce_options3', value: 'backup' }
];

export const VipLevelSettingModal = ({
  open,
  onOk,
  onClose,
  initialValues,
  vipleveldata
}: {
  open: boolean;
  onOk?: () => void;
  onClose: () => void;
  initialValues?: FormValue;
  vipleveldata: {
    id: number;
    name: string;
    level: number;
    levelStatus: number;
  }[];
}) => {
  const { t } = useTranslation();
  const [form] = RForm.useForm<FormValue>();
  const [checkedList, setCheckedList] = useState<string[]>([]);
  const queryClient = useQueryClient();

  const handleClose = () => {
    form.resetFields();
    setCheckedList([]);
    onClose();
  };

  const { mutate: editMutation, isPending: isEditPending } = useMutation({
    mutationFn: editVipLevelSetting,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['vipList'] });
      onOk?.();
      handleClose();
    }
  });

  const handleSubmit = (values: FormValue) => {
    editMutation({ vipId: values.id });
  };

  return (
    <FormModal
      title={t('pages_player_vip_button_levelSetting')}
      form={form}
      initialValues={initialValues}
      open={open}
      onClose={handleClose}
      onSubmit={handleSubmit}
      isLoading={isEditPending}
    >
      <div className="flex flex-col gap-2">
        {/* VIP 等級選擇 */}
        <RForm.Item
          label={
            <span className="text-text-secondary">{t('pages_player_vip_vipLevelSetting')}</span>
          }
          name="id"
          rules={[{ required: true, message: '請選擇VIP等級' }]}
        >
          <RSelect
            className="!w-[320px]"
            options={vipleveldata.map((item) => ({
              label: item.name,
              value: item.id
            }))}
          />
        </RForm.Item>
        {/* 說明區塊 */}
        <div className="text-xs text-text bg-bg-primary rounded w-full p-3">
          <div className="font-bold mb-1 text-primary">{t('pages_player_vip_notice')}</div>
          <div>{t('pages_player_vip_notice_description')}</div>
          <p className="mt-4 mb-2">{t('pages_player_vip_notice_warning_description')}</p>
          {/* 注意事項 Checkbox 群組 */}
          <RForm.Item
            name="info"
            rules={[
              {
                validator: (_, value) =>
                  value && value.length === noticeOptions.length
                    ? Promise.resolve()
                    : Promise.reject(new Error(t('pages_player_vip_notice_warning')))
              }
            ]}
          >
            <RCheckbox.Group
              options={noticeOptions.map((item) => ({
                label: t(item.label),
                value: item.value
              }))}
              value={checkedList}
              onChange={setCheckedList}
              className="flex flex-col gap-2"
            />
          </RForm.Item>
          <p className="mt-4">{t('pages_player_vip_notice_info')}</p>
        </div>
      </div>
    </FormModal>
  );
};

export default VipLevelSettingModal;
