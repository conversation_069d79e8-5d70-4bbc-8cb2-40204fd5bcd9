import clsx from 'clsx';
import { useContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { ActionButtons } from '@/components/ActionButtons';
import { AmountCell } from '@/components/cells/AmountCell';
import OperatorCell from '@/components/cells/OperatorCell';
import PublishTypeCell from '@/components/cells/PublishTypeCell';
import DragTable from '@/components/DragTable';
import RButton from '@/components/RButton';
import StatusLabel from '@/components/StatusLabel';
import { MAX_CAROUSEL_AMOUNT } from '@/constants/carousel';
import useFrontendLanguage from '@/hooks/useFrontendLanguage';
import TableSearchLayout from '@/layout/TableSearchLayout';
import { Carousel, CarouselInteractEnum } from '@/types/operation';

import { CarouselContext } from './CarouselContext';
import CarouselModal from './CarouselModal';
import PreviewCarouselModal from './PreviewCarouselModal.tsx';
import { useCarouselActions } from './useCarouselActions';

const BgImageColumn = ({ record }: { value: string; record: Carousel }) => {
  const { defaultFrontendLanguage, setPreviewState } = useContext(CarouselContext);
  const value = record.imgUrlMapping[defaultFrontendLanguage]?.bgPhoto;
  return (
    <img
      src={value}
      alt="bgImage"
      className="object-contain w-20 h-20 cursor-pointer"
      onClick={() => setPreviewState({ open: true, carousel: record })}
    />
  );
};

const TextImageColumn = ({ record }: { value: string; record: Carousel }) => {
  const { defaultFrontendLanguage, setPreviewState } = useContext(CarouselContext);
  const value = record.imgUrlMapping[defaultFrontendLanguage]?.txtPhoto;
  if (!value) return '-';
  return (
    <img
      src={value}
      alt="textImage"
      className="object-contain w-20 h-20 cursor-pointer"
      onClick={() => setPreviewState({ open: true, carousel: record })}
    />
  );
};

export const InteractColumn = ({ record }: { record: Carousel }) => {
  const { t } = useTranslation();
  let text = '';
  switch (record.interact) {
    case CarouselInteractEnum.NONE:
      text = t('common_none');
      break;
    case CarouselInteractEnum.INTERNAL:
      text = t('pages_carousel_internalLink');
      break;
    case CarouselInteractEnum.LINK:
      text = t('pages_carousel_externalLink');
      break;
  }
  return (
    <p className={clsx(record.interact !== CarouselInteractEnum.NONE && 'underline text-info')}>
      {text}
    </p>
  );
};

const columns = [
  {
    title: 'pages_carousel_name',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: 'pages_carousel_bgImage',
    dataIndex: 'bgImage',
    render: (value: string, record: Carousel) => {
      return <BgImageColumn value={value} record={record} />;
    }
  },
  {
    title: 'pages_carousel_textImage',
    dataIndex: 'textImage',
    render: (value: string, record: Carousel) => {
      return <TextImageColumn value={value} record={record} />;
    }
  },
  {
    title: 'pages_carousel_interact',
    dataIndex: 'interact',
    render: (_: number, record: Carousel) => {
      return <InteractColumn record={record} />;
    }
  },
  {
    title: 'components_formTimePeriodSelect_publishType',
    dataIndex: 'publishType',
    render: (value: number, record: Carousel) => {
      return <PublishTypeCell value={value} record={record} />;
    }
  },
  {
    title: 'common_lastOperate',
    dataIndex: 'updatedAt',
    render: (value: number, record: Carousel) => {
      return <OperatorCell record={{ updatedBy: record.updatedBy, updatedAt: value }} />;
    }
  },
  {
    title: 'common_status',
    dataIndex: 'status',
    render: (value: number) => {
      return <StatusLabel status={value} />;
    }
  }
];

const TableWrap = ({
  setCarouselAmount,
  setInitialValues,
  setOpen
}: {
  setCarouselAmount: (value: number) => void;
  setInitialValues: (value: Carousel) => void;
  setOpen: (value: boolean) => void;
}) => {
  const { t } = useTranslation();
  const [dataSource, setDataSource] = useState<Carousel[]>([]);
  const {
    data,
    isUpdateStatusPending,
    handleDeleteCarousel,
    handleUpdateStatus,
    handleUpdateOrder,
    isLoading
  } = useCarouselActions();

  useEffect(() => {
    setDataSource(data || []);
    setCarouselAmount(data?.length || 0);
  }, [data, setCarouselAmount]);

  const handleChangeOrder = (data: Carousel[]) => {
    setDataSource(data);
    handleUpdateOrder(data);
  };

  const actionColumn = {
    title: 'common_action',
    dataIndex: 'action',
    render: (_: string, record: Carousel) => {
      return (
        <ActionButtons
          data={record}
          buttons={['edit', 'delete', 'status']}
          onEdit={() => {
            setInitialValues(record);
            setOpen(true);
          }}
          onEditStatus={() => handleUpdateStatus(record)}
          isEditStatusPending={isUpdateStatusPending}
          onDelete={() => handleDeleteCarousel(record)}
        />
      );
    }
  };

  const tableColumns = [...columns, actionColumn].map((column) => ({
    ...column,
    title: typeof column.title === 'string' ? t(column.title) : column.title
  }));

  return (
    <DragTable
      keyName="id"
      onSortChange={handleChangeOrder}
      loading={isLoading}
      rowKey="id"
      dataSource={dataSource}
      columns={tableColumns}
      pagination={false}
    ></DragTable>
  );
};

const CarouselPage = () => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const [initialValues, setInitialValues] = useState<Carousel>();
  const { frontendLanguageList, defaultFrontendLanguage } = useFrontendLanguage();
  const [carouselAmount, setCarouselAmount] = useState(0);

  const [previewState, setPreviewState] = useState<{ open: boolean; carousel: Carousel | null }>({
    open: false,
    carousel: null
  });

  const handleOpenNew = () => {
    setInitialValues(undefined);
    setOpen(true);
  };

  return (
    <CarouselContext.Provider
      value={{ frontendLanguageList, defaultFrontendLanguage, previewState, setPreviewState }}
    >
      <TableSearchLayout>
        <div className="flex items-end gap-3 mb-4">
          <RButton onClick={handleOpenNew} disabled={carouselAmount >= MAX_CAROUSEL_AMOUNT}>
            {t('common_add_name', { name: t('pages_carousel') })}
          </RButton>
          <AmountCell amount={carouselAmount} />
        </div>
        <TableWrap
          setCarouselAmount={setCarouselAmount}
          setInitialValues={setInitialValues}
          setOpen={setOpen}
        />
      </TableSearchLayout>
      <CarouselModal
        open={open}
        onClose={() => setOpen(false)}
        initialValues={initialValues}
      ></CarouselModal>
      <PreviewCarouselModal />
    </CarouselContext.Provider>
  );
};

export default CarouselPage;
