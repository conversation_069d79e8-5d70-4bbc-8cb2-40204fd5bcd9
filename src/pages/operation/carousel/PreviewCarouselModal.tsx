import clsx from 'clsx';
import { useContext } from 'react';

import RModal from '@/components/RModal';

import { CarouselContext } from './CarouselContext';

const imageWidth = 350;
const imageHeight = 200;

const ImagePreview = ({ image, className }: { image: File | string; className?: string }) => {
  return (
    <img
      src={image instanceof File ? URL.createObjectURL(image) : image}
      alt="image"
      style={{ width: imageWidth, height: imageHeight }}
      className={clsx('object-contain', className)}
    />
  );
};

export const CarouselPreview = ({
  bgImage,
  txtImage
}: {
  bgImage?: string | File;
  txtImage?: string | File;
}) => {
  return (
    <div
      className="relative flex items-center gap-4 "
      style={{ width: imageWidth, height: imageHeight }}
    >
      {bgImage && <ImagePreview image={bgImage} className="absolute top-0 left-0 object-cover" />}
      {txtImage && (
        <ImagePreview image={txtImage} className="absolute top-0 right-0 z-10 object-contain" />
      )}
    </div>
  );
};

const PreviewItem = ({
  title,
  children,
  className
}: {
  title: string;
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <div className={clsx('space-y-1', className)}>
      <p className="text-xs text-text-secondary">{title}</p>
      {children}
    </div>
  );
};

const PreviewCarouselModal = () => {
  const { t } = useTranslation();
  const { previewState, setPreviewState, defaultFrontendLanguage } = useContext(CarouselContext);
  const bgImage = previewState.carousel?.imgUrlMapping[defaultFrontendLanguage]?.bgPhoto || '';
  const textImage = previewState.carousel?.imgUrlMapping[defaultFrontendLanguage]?.txtPhoto;

  return (
    <RModal
      width={800}
      title={t('pages_carousel_preview')}
      open={previewState.open}
      onCancel={() => setPreviewState({ open: false, carousel: null })}
    >
      <div className="flex items-center justify-start gap-4">
        <PreviewItem title={t('pages_carousel_bgImage')}>
          <ImagePreview image={bgImage} />
        </PreviewItem>
        {textImage && (
          <PreviewItem title={t('pages_carousel_textImage')}>
            <ImagePreview image={textImage} />
          </PreviewItem>
        )}
      </div>
      <PreviewItem title={t('pages_carousel_previewImage')} className="mt-4">
        <CarouselPreview bgImage={bgImage} txtImage={textImage} />
      </PreviewItem>
    </RModal>
  );
};

export default PreviewCarouselModal;
