import { useMutation, useQueryClient } from '@tanstack/react-query';

import { createCarousel, updateCarousel } from '@/api/operation';
import InformationIcon from '@/assets/img/icon/information.svg?react';
import FormModal from '@/components/FormModal';
import FormTimePeriodSelect from '@/components/FormTimePeriodSelect';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RSelect from '@/components/RSelect';
import RSwitch from '@/components/RSwitch';
import RTooltip from '@/components/Rtooltip';
import RUploader from '@/components/RUploader';
import useImageUpload from '@/components/RUploader/useImageUpload';
import { Carousel, CarouselInteractEnum } from '@/types/operation';

import { CarouselPreview } from './PreviewCarouselModal';

interface CarouselModalProps {
  open: boolean;
  onClose: () => void;
  initialValues?: Carousel;
}

type FormValue = Pick<
  Carousel,
  'name' | 'status' | 'interact' | 'link' | 'imgUrlMapping' | 'publishType'
> & {
  zhTwBgPhoto?: File | string;
  zhTwTxtPhoto?: File | string;
  timeRange?: [number, number];
};

const CarouselInteractOptions = [
  {
    label: 'common_none',
    value: CarouselInteractEnum.NONE
  },
  {
    label: 'pages_carousel_externalLink',
    value: CarouselInteractEnum.LINK
  },
  {
    label: 'pages_carousel_internalLink',
    value: CarouselInteractEnum.INTERNAL
  }
];

const LinkInformation = ({ interactValue }: { interactValue: CarouselInteractEnum }) => {
  const { t } = useTranslation();

  const tooltipTitle =
    interactValue === CarouselInteractEnum.LINK ? (
      t('pages_carousel_externalLinkNote')
    ) : (
      <p>
        {t('pages_carousel_internalLinkNote')}
        <br />
        {t('pages_carousel_internalLinkNoteUsage')}
      </p>
    );
  return (
    <div className="flex items-center gap-2">
      {interactValue === CarouselInteractEnum.LINK
        ? t('pages_carousel_externalLink')
        : t('pages_carousel_internalLink')}
      <RTooltip placement="top" title={tooltipTitle}>
        <InformationIcon className="w-4 h-4 fill-info" />
      </RTooltip>
    </div>
  );
};
const CarouselModal = ({ open, onClose, initialValues }: CarouselModalProps) => {
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const [form] = RForm.useForm<FormValue>();
  const isEdit = !!initialValues?.id;

  const { accept, beforeUpload } = useImageUpload({
    fileType: 'jpg,jpeg,png,webp',
    maxSize: 1
  });

  const handleClose = () => {
    form.resetFields();
    onClose();
  };

  const onSuccess = () => {
    queryClient.invalidateQueries({ queryKey: ['carousel'] });
    handleClose();
  };

  const { mutate: createCarouselMutate, isPending: isCreateCarouselPending } = useMutation({
    mutationFn: createCarousel,
    onSuccess: onSuccess
  });

  const { mutate: updateCarouselMutate, isPending: isUpdateCarouselPending } = useMutation({
    mutationFn: updateCarousel,
    onSuccess: onSuccess
  });

  const onSubmit = (values: FormValue) => {
    if (isEdit) {
      const data = { ...values, id: initialValues.id };
      if (typeof values.zhTwBgPhoto === 'string') {
        delete data.zhTwBgPhoto;
      }
      if (typeof values.zhTwTxtPhoto === 'string') {
        delete data.zhTwTxtPhoto;
      }
      updateCarouselMutate(data);
    } else {
      createCarouselMutate(values);
    }
  };

  const bgImage = RForm.useWatch(['zhTwBgPhoto'], form);
  const txtImage = RForm.useWatch(['zhTwTxtPhoto'], form);

  const isLoading = isCreateCarouselPending || isUpdateCarouselPending;
  const interactValue = RForm.useWatch('interact', form);

  useEffect(() => {
    if (initialValues) {
      const timeout = setTimeout(() => {
        form.setFieldsValue({
          zhTwBgPhoto: initialValues.imgUrlMapping?.zhTw?.bgPhoto,
          zhTwTxtPhoto: initialValues.imgUrlMapping?.zhTw?.txtPhoto,
          link: initialValues.link
        });
        if (initialValues.endTime) {
          form.setFieldsValue({
            timeRange: [initialValues.startTime, initialValues.endTime]
          });
        }
      }, 100);
      return () => clearTimeout(timeout);
    }
  }, [initialValues, form]);

  return (
    <FormModal
      open={open}
      onClose={handleClose}
      initialValues={initialValues}
      title={
        isEdit
          ? t('common_edit_name', { name: t('pages_carousel') })
          : t('common_add_name', { name: t('pages_carousel') })
      }
      form={form}
      onSubmit={onSubmit}
      isLoading={isLoading}
    >
      <div className="grid grid-cols-2 gap-4">
        <RForm.Item name="id" label={null} noStyle></RForm.Item>
        <RForm.Item name="name" label={t('pages_carousel_name')} rules={[{ required: true }]}>
          <RInput maxLength={30} />
        </RForm.Item>
        <RForm.Item name="status" label={t('common_status')} initialValue={1}>
          <RSwitch isBoolean={false} />
        </RForm.Item>
        <RForm.Item
          name="zhTwBgPhoto"
          label={t('pages_carousel_bgImage')}
          rules={[{ required: true }]}
        >
          <RUploader
            accept={accept}
            beforeUpload={beforeUpload}
            note={t('pages_carousel_imageNote')}
          />
        </RForm.Item>
        <RForm.Item name="zhTwTxtPhoto" label={t('pages_carousel_textImage')}>
          <RUploader
            accept={accept}
            beforeUpload={beforeUpload}
            note={t('pages_carousel_imageNote')}
          />
        </RForm.Item>
        <RForm.Item label={t('pages_carousel_previewImage')}>
          <CarouselPreview bgImage={bgImage} txtImage={txtImage} />
        </RForm.Item>
        <RForm.Item label={t('components_formTimePeriodSelect_publishType')}>
          <FormTimePeriodSelect
            form={form}
            showPermanent={true}
            showWeekly={false}
            includeTimeRange={true}
            classNames="col-span-2"
            fieldPrefix=""
          />
        </RForm.Item>
        <RForm.Item
          name="interact"
          label={t('pages_carousel_interact')}
          required
          initialValue={CarouselInteractEnum.NONE}
        >
          <RSelect
            options={CarouselInteractOptions.map((option) => ({
              label: t(option.label),
              value: option.value
            }))}
          />
        </RForm.Item>
        {interactValue !== CarouselInteractEnum.NONE && (
          <RForm.Item
            name="link"
            label={<LinkInformation interactValue={interactValue} />}
            rules={[{ required: true, message: t('placeholder_input') }]}
          >
            <RInput />
          </RForm.Item>
        )}
      </div>
    </FormModal>
  );
};

export default CarouselModal;
