import { createContext, Dispatch, SetStateAction } from 'react';

import { Carousel } from '@/types/operation';

export const CarouselContext = createContext<{
  frontendLanguageList: string[];
  defaultFrontendLanguage: string;
  previewState: {
    open: boolean;
    carousel: Carousel | null;
  };
  setPreviewState: Dispatch<SetStateAction<{ open: boolean; carousel: Carousel | null }>>;
}>({
  frontendLanguageList: [],
  defaultFrontendLanguage: '',
  previewState: {
    open: false,
    carousel: null
  },
  setPreviewState: () => {}
});
