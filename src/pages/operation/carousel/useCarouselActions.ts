import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import {
  deleteCarousel,
  getCarousel,
  updateCarouselOrder,
  updateCarouselStatus
} from '@/api/operation';
import useActions from '@/hooks/useActions';
import { Carousel } from '@/types/operation';

const CAROUSEL_QUERY_KEY = 'carousel';

export const useCarouselActions = () => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const { handleDelete, handleEditStatus } = useActions();

  // 獲取輪播圖列表
  const { data, isPending } = useQuery({
    queryKey: [CAROUSEL_QUERY_KEY],
    queryFn: () => getCarousel(),
    select: (data) => data?.data?.data?.sort((a, b) => a.order - b.order) || []
  });

  // 更新輪播圖順序
  const { mutate: updateOrder, isPending: isUpdateOrderPending } = useMutation({
    mutationFn: updateCarouselOrder,
    onError: () => {
      // 如果更新失敗，重新獲取數據
      queryClient.invalidateQueries({ queryKey: [CAROUSEL_QUERY_KEY] });
    }
  });

  // 刪除輪播圖
  const { mutate: deleteCarouselMutation, isPending: isDeletePending } = useMutation({
    mutationFn: deleteCarousel,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CAROUSEL_QUERY_KEY] });
    }
  });

  // 更新輪播圖狀態
  const { mutate: updateStatus, isPending: isUpdateStatusPending } = useMutation({
    mutationFn: updateCarouselStatus,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CAROUSEL_QUERY_KEY] });
    }
  });

  // 處理刪除操作
  const handleDeleteCarousel = (carousel: Carousel) => {
    handleDelete(t('pages_carousel'), () => {
      deleteCarouselMutation({ id: carousel.id });
    });
  };

  // 處理狀態更新操作
  const handleUpdateStatus = (carousel: Carousel) => {
    handleEditStatus(t('pages_carousel'), carousel.status, () => {
      updateStatus({ id: carousel.id, status: carousel.status === 1 ? 0 : 1 });
    });
  };

  // 處理順序更新操作
  const handleUpdateOrder = (carousels: Carousel[]) => {
    updateOrder({ orders: carousels.map((item) => item.id) });
  };

  const isLoading = isPending || isUpdateOrderPending || isDeletePending || isUpdateStatusPending;

  return {
    data,
    isPending,
    isUpdateOrderPending,
    isDeletePending,
    isUpdateStatusPending,
    handleDeleteCarousel,
    handleUpdateStatus,
    handleUpdateOrder,
    isLoading
  };
};
