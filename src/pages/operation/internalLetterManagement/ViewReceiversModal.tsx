import { useTranslation } from 'react-i18next';

import CopyIcon from '@/components/CopyIcon';
import RModal from '@/components/RModal';
import RTable from '@/components/RTable';
import RTag from '@/components/RTag';
import {
  InternalLetterReceiver,
  InternalLetterRecord,
  MailReadStatus,
  MailStatusFilter
} from '@/types/mail';
import { formatTime } from '@/utils/time';

type ViewReceiversModalProps = {
  open: boolean;
  onClose: () => void;
  isLoadingContent?: boolean;
  letterRecord?: InternalLetterRecord | null;
  list?: InternalLetterReceiver[];
};

const ViewReceiversModal = ({
  open,
  onClose,
  isLoadingContent,
  letterRecord,
  list
}: ViewReceiversModalProps) => {
  const { t } = useTranslation();

  const columns = [
    {
      title: t('common_account'),
      dataIndex: 'account',
      render: (account: string) => {
        return (
          <div className="flex items-center gap-1">
            <CopyIcon text={account} width={12} height={12} />
            <span className="truncate">{account}</span>
          </div>
        );
      }
    },
    {
      title: t('pages_operation_internalLetterManagement_isRead'),
      dataIndex: 'isRead',
      render: (isRead: MailReadStatus) => {
        if (isRead === MailReadStatus.READ) return <RTag color="green">{t('common_read')}</RTag>;
        if (isRead === MailReadStatus.UNREAD) return <RTag color="red">{t('common_unread')}</RTag>;
      }
    },
    {
      title: t('pages_operation_internalLetterManagement_readingTime'),
      dataIndex: 'readAt',
      render: (readAt: number) => (readAt ? formatTime(readAt) : '-')
    },
    {
      title: t('pages_operation_internalLetterManagement_status'),
      dataIndex: 'statusLabel',
      render: (statusLabel: string, record: InternalLetterReceiver) => (
        <RTag color={record.status === MailStatusFilter.WITHDRAWN ? 'error' : 'success'}>
          {statusLabel}
        </RTag>
      )
    }
  ];

  return (
    <RModal
      title={t('pages_operation_internalLetterManagement_receivePlayer')}
      open={open}
      onCancel={onClose}
      width={800}
      okButtonProps={{ show: false }}
      cancelButtonProps={{ text: t('common_close'), show: true }}
      loading={isLoadingContent}
    >
      <div className="flex flex-col gap-4">
        <div className="space-y-2">
          {/* Title */}
          <div className="flex gap-2">
            <p className="text-gray-400 mb-2">
              {t('pages_operation_internalLetterManagement_title')}:
            </p>
            <div className="">{letterRecord?.title}</div>
          </div>

          {/* Content */}
          <div>
            <p className="text-gray-400 mb-2">
              {t('pages_operation_internalLetterManagement_receivePlayerList')}:
            </p>
            <div className="">
              <RTable
                loading={isLoadingContent}
                rowKey="id"
                dataSource={list || []}
                columns={columns}
                pagination={false}
              />
            </div>
          </div>
        </div>
      </div>
    </RModal>
  );
};

export default ViewReceiversModal;
