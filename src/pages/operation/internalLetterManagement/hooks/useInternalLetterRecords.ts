import { useQuery } from '@tanstack/react-query';

import { getInternalLetterRecords } from '@/api/mail';
import { InternalLetterRecordSearchParams } from '@/types/mail';

// Query keys for internal letter record related queries
export const internalLetterRecordKeys = {
  all: ['internalLetterRecord'] as const,
  records: {
    all: ['internalLetterRecord', 'records'] as const,
    lists: () => ['internalLetterRecord', 'records', 'list'] as const,
    list: (params: InternalLetterRecordSearchParams) =>
      ['internalLetterRecord', 'records', 'list', params] as const
  }
};

export const useInternalLetterRecords = (params: InternalLetterRecordSearchParams) => {
  return useQuery({
    queryKey: internalLetterRecordKeys.records.list(params),
    queryFn: () => getInternalLetterRecords(params),
    select: (data) => data.data
  });
};
