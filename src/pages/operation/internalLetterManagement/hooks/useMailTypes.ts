import { useQuery } from '@tanstack/react-query';

import { getMailTypes } from '@/api/mail';

// Query keys for mail type related queries
export const mailTypeKeys = {
  all: ['mailType'] as const,
  types: {
    all: ['mailType', 'types'] as const,
    list: () => ['mailType', 'types', 'list'] as const
  }
};

export const useMailTypes = () => {
  return useQuery({
    queryKey: mailTypeKeys.types.list(),
    queryFn: () => getMailTypes(),
    select: (data) => data.data
  });
};
