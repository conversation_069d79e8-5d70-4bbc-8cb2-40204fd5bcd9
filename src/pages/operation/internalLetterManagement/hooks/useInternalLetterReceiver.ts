import { useQuery } from '@tanstack/react-query';

import { batchReadMailRecords } from '@/api/mail';

export const internalLetterReceiverKeys = {
  all: ['internalLetterReceiver'] as const,
  detail: (id?: string) => ['internalLetterReceiver', 'detail', id] as const
};

export const useInternalLetterReceiver = (id?: string) => {
  return useQuery({
    queryKey: internalLetterReceiverKeys.detail(id),
    queryFn: () => {
      if (!id) {
        throw new Error('ID is required');
      }
      return batchReadMailRecords(id);
    },
    select: (data) => data.data,
    enabled: !!id
  });
};
