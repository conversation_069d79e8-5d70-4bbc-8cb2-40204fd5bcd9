import { useQuery } from '@tanstack/react-query';

import { getMailRecordDetail } from '@/api/mail';

// Query keys for internal letter record related queries
export const internalLetterRecordContentDetailKeys = {
  all: ['internalLetterRecordContentDetail'] as const,
  detail: (id?: string) => ['internalLetterRecordContentDetail', 'detail', id] as const
};

export const useInternalLetterRecordContentDetail = (id?: string) => {
  return useQuery({
    queryKey: internalLetterRecordContentDetailKeys.detail(id),
    queryFn: () => {
      if (!id) {
        throw new Error('ID is required');
      }
      return getMailRecordDetail(id);
    },
    select: (data) => data.data,
    enabled: !!id
  });
};
