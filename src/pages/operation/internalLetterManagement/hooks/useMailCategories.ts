import { useQuery } from '@tanstack/react-query';

import { getMailCategories } from '@/api/mail';

// Query keys for mail category related queries
export const mailCategoryKeys = {
  all: ['mailCategory'] as const,
  categories: {
    all: ['mailCategory', 'categories'] as const,
    list: () => ['mailCategory', 'categories', 'list'] as const
  }
};

export const useMailCategories = () => {
  return useQuery({
    queryKey: mailCategoryKeys.categories.list(),
    queryFn: () => getMailCategories(),
    select: (data) => data.data
  });
};
