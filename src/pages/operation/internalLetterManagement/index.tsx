import { useMutation } from '@tanstack/react-query';
import dayjs from 'dayjs';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { triggerExportInternalLetterRecords, withdrawMailRecord } from '@/api/mail';
import OperatorCell from '@/components/cells/OperatorCell';
import ExportRecord from '@/components/ExportRecord';
import QuickDateSelect from '@/components/QuickDateSelect';
import RButton from '@/components/RButton';
import RForm from '@/components/RForm';
import RSelect from '@/components/RSelect';
import RTable from '@/components/RTable';
import RTag from '@/components/RTag';
import SearchForm from '@/components/SearchForm';
import useConfirmModal from '@/hooks/useConfirmModal';
import usePagination from '@/hooks/usePagination';
import TableSearchLayout from '@/layout/TableSearchLayout';
import {
  InternalLetterRecord,
  InternalLetterRecordSearchParams,
  MailReadStatusFilter,
  MailStatusFilter
} from '@/types/mail';
import { cleanSearchParams } from '@/utils/object';
import { formatTime } from '@/utils/time';

import ContentDisplayModal from '../frontContent/ContentDisplayModal';
import { useInternalLetterReceiver } from './hooks/useInternalLetterReceiver';
import { useInternalLetterRecordContentDetail } from './hooks/useInternalLetterRecordContentDetail';
import { useInternalLetterRecords } from './hooks/useInternalLetterRecords';
import { useMailCategories } from './hooks/useMailCategories';
import { useMailTypes } from './hooks/useMailTypes';
import ViewReceiversModal from './ViewReceiversModal';

const defaultToday = [dayjs().startOf('day').valueOf(), dayjs().endOf('day').valueOf()] as [
  number,
  number
];

type SearchFormValues = Omit<
  InternalLetterRecordSearchParams,
  'timeStart' | 'timeEnd' | 'page' | 'limit'
> & { start: number; end: number };

const getTableColumns = (
  t: (key: string) => string,
  handleWithdraw: (data: InternalLetterRecord) => void,
  handleDetail: (data: InternalLetterRecord) => void,
  handleClickReceivers: (data: InternalLetterRecord) => void
) => [
  {
    title: t('pages_operation_internalLetterManagement_operator_send_time'),
    dataIndex: 'operatorAndTime',
    render: (_: string, record: InternalLetterRecord) => (
      <OperatorCell record={{ updatedBy: record.createdBy, updatedAt: record.createdAt }} />
    )
  },
  {
    title: t('pages_operation_internalLetterManagement_type'),
    dataIndex: 'categoryLabel'
  },
  {
    title: t('pages_operation_internalLetterManagement_type_detail'),
    dataIndex: 'typeLabel'
  },
  {
    title: t('pages_operation_internalLetterManagement_title'),
    dataIndex: 'title'
  },
  {
    title: t('pages_operation_internalLetterManagement_receivePlayer'),
    dataIndex: 'account',
    render: (account: string, record: InternalLetterRecord) => {
      const isSentToMultiplePlayers = record.account === null;

      if (isSentToMultiplePlayers) {
        return (
          <div className="cursor-pointer" onClick={() => handleClickReceivers(record)}>
            <a className="!underline">{record.readCount + record.unreadCount}</a>
            <div className="text-warning">{`(${t('common_unread')}: ${record.unreadCount})`}</div>
          </div>
        );
      }
      return (
        <div>
          <div>{account}</div>
          {record.isRead === MailReadStatusFilter.UNREAD && (
            <div className="text-warning">{`(${t('common_unread')})`}</div>
          )}
        </div>
      );
    }
  },
  {
    title: t('pages_operation_internalLetterManagement_readingTime'),
    dataIndex: 'readAt',
    render: (readAt: number) => (readAt ? formatTime(readAt) : '-')
  },
  {
    title: t('pages_operation_internalLetterManagement_status'),
    dataIndex: 'statusLabel',
    render: (statusLabel: string, record: InternalLetterRecord) => (
      <RTag color={record.status === MailStatusFilter.WITHDRAWN ? 'error' : 'success'}>
        {statusLabel}
      </RTag>
    )
  },
  {
    title: t('common_action'),
    dataIndex: 'action',
    render: (_: string, record: InternalLetterRecord) => (
      <div className="flex gap-1">
        {record.status === MailStatusFilter.ACTIVE && (
          <RButton
            variant="outlined"
            color="red"
            type="link"
            size="small"
            onClick={() => handleWithdraw(record)}
          >
            {t('pages_transaction_unfinishGiftOrder_withdraw')}
          </RButton>
        )}
        <RButton
          variant="outlined"
          color="green"
          type="link"
          size="small"
          onClick={() => handleDetail(record)}
        >
          {t('common_detail')}
        </RButton>
      </div>
    )
  }
];

const SearchFormWrap = ({
  onSearch,
  onReset
}: {
  onSearch: (values: Omit<InternalLetterRecordSearchParams, 'page' | 'limit'>) => void;
  onReset: () => void;
}) => {
  const { t } = useTranslation();
  const { data: mailCategoriesData } = useMailCategories();
  const { data: mailTypesData } = useMailTypes();

  const categoryOptions = useMemo(() => {
    const allOption = { label: t('common_all'), value: 'all' };
    const apiOptions =
      mailCategoriesData?.map((category) => ({
        label: category.label,
        value: category.id
      })) || [];
    return [allOption, ...apiOptions];
  }, [mailCategoriesData, t]);

  const typeOptions = useMemo(() => {
    const allOption = { label: t('common_all'), value: 'all' };
    const apiOptions =
      mailTypesData?.map((type) => ({
        label: type.label,
        value: type.id
      })) || [];
    return [allOption, ...apiOptions];
  }, [mailTypesData, t]);

  const readStatusOptions = useMemo(() => {
    return [
      { label: t('common_all'), value: MailReadStatusFilter.ALL },
      {
        label: t('common_unread'),
        value: MailReadStatusFilter.UNREAD
      },
      {
        label: t('common_read'),
        value: MailReadStatusFilter.READ
      }
    ];
  }, [t]);

  const handleSearch = (values: SearchFormValues) => {
    const { start, end, ...rest } = values;

    const searchValues = {
      ...cleanSearchParams(rest),
      timeStart: start,
      timeEnd: end
    };

    onSearch(searchValues);
  };

  return (
    <SearchForm<SearchFormValues> onSearch={handleSearch} onReset={onReset} className="">
      <RForm.Item name="date" label={t('common_timeSelect')} initialValue={defaultToday}>
        <QuickDateSelect defaultActiveKey="today" />
      </RForm.Item>
      <RForm.Item name="template" label={t('pages_operation_internalLetterManagement_template')}>
        <RSelect
          placeholder={t('common_please_select', {
            name: t('pages_operation_internalLetterManagement_template')
          })}
          options={categoryOptions}
        />
      </RForm.Item>
      <RForm.Item name="type" label={t('pages_operation_internalLetterManagement_type_detail')}>
        <RSelect
          placeholder={t('common_please_select', {
            name: t('pages_operation_internalLetterManagement_type_detail')
          })}
          options={typeOptions}
        />
      </RForm.Item>
      <RForm.Item name="isRead" label={t('pages_operation_internalLetterManagement_isRead')}>
        <RSelect
          placeholder={t('common_please_select', {
            name: t('pages_operation_internalLetterManagement_isRead')
          })}
          options={readStatusOptions}
        />
      </RForm.Item>
    </SearchForm>
  );
};

const InternalLetterManagementPage = () => {
  const { t } = useTranslation();
  const { page, setPage, limit, setLimit } = usePagination({});
  const [viewingRecord, setViewingRecord] = useState<InternalLetterRecord | null>(null);
  const [viewingReceivers, setViewingReceivers] = useState<InternalLetterRecord | null>(null);
  const [params, setParams] = useState<Omit<InternalLetterRecordSearchParams, 'page' | 'limit'>>({
    timeStart: defaultToday[0],
    timeEnd: defaultToday[1]
  });
  const internalLetterRecords = useInternalLetterRecords({
    page,
    limit,
    ...params
  });
  const internalLetterRecordContentDetailQuery = useInternalLetterRecordContentDetail(
    viewingRecord?.id
  );
  const internalLetterReceiverQuery = useInternalLetterReceiver(viewingReceivers?.id);
  const { confirmModal } = useConfirmModal();
  const withdrawMutation = useMutation({
    mutationFn: withdrawMailRecord,
    onSuccess: () => {
      internalLetterRecords.refetch();
    }
  });

  const handleWithdraw = useCallback(
    (data: InternalLetterRecord) => {
      if (!data.id) return;
      confirmModal({
        content: t('common_confirm_withdraw_name', { name: data.title }),
        onOk: () => {
          withdrawMutation.mutate(data.id);
          console.log('withdraw', data.id);
        }
      });
    },
    [confirmModal, t, withdrawMutation]
  );

  const tableColumns = useMemo(
    () => getTableColumns(t, handleWithdraw, setViewingRecord, setViewingReceivers),
    [handleWithdraw, t]
  );

  const exportMutation = useMutation({
    mutationFn: triggerExportInternalLetterRecords
  });

  const handleChangePage = (page: number, pageSize: number) => {
    setPage(page);
    setLimit(pageSize);
  };

  const handleSearch = (newParams: Omit<InternalLetterRecordSearchParams, 'page' | 'limit'>) => {
    setParams((oldParams) => {
      // @ts-ignore
      const isTheSame = Object.entries(newParams).every(([key, value]) => oldParams[key] === value);
      if (isTheSame) {
        setTimeout(() => {
          internalLetterRecords.refetch();
        }, 100);
      }
      return newParams;
    });
    setPage(1);
  };

  const handleReset = () => {
    const resetParams = {
      timeStart: defaultToday[0],
      timeEnd: defaultToday[1]
    };
    setParams(resetParams);
    setPage(1);
  };

  return (
    <TableSearchLayout
      searchFields={<SearchFormWrap onSearch={handleSearch} onReset={handleReset} />}
    >
      <div>
        <ExportRecord
          onClick={() => exportMutation.mutate(params)}
          id={exportMutation.data?.data?.id ?? undefined}
          isLoading={exportMutation.isPending}
        />

        <RTable
          loading={internalLetterRecords.isPending}
          rowKey="id"
          dataSource={internalLetterRecords.data?.data || []}
          columns={tableColumns}
          pagination={{
            current: page,
            pageSize: limit,
            total: internalLetterRecords.data?.total || 0,
            showSizeChanger: true,
            onChange: handleChangePage
          }}
        />
        <ContentDisplayModal
          isLoadingContent={internalLetterRecordContentDetailQuery.isPending}
          open={!!viewingRecord}
          onClose={() => setViewingRecord(null)}
          contentMapping={internalLetterRecordContentDetailQuery.data?.contentMapping}
        />
        <ViewReceiversModal
          open={!!viewingReceivers}
          onClose={() => setViewingReceivers(null)}
          letterRecord={viewingReceivers}
          list={internalLetterReceiverQuery.data}
          isLoadingContent={internalLetterReceiverQuery.isPending}
        />
      </div>
    </TableSearchLayout>
  );
};

export default InternalLetterManagementPage;
