import { useQuery } from '@tanstack/react-query';

import { getGameLayoutList } from '@/api/game';

// Query keys
export const gameLayoutKeys = {
  all: ['gameLayout'] as const,
  lists: () => [...gameLayoutKeys.all, 'list'] as const,
  list: (params: { type?: string }) => [...gameLayoutKeys.lists(), params] as const,
  details: () => [...gameLayoutKeys.all, 'detail'] as const,
  detail: (id: string) => [...gameLayoutKeys.details(), id] as const
};

interface UseGameLayoutQueryOptions {
  enabled?: boolean;
  type?: string;
}

const useGameLayoutQuery = ({ enabled = true, type }: UseGameLayoutQueryOptions) => {
  const query = useQuery({
    queryKey: gameLayoutKeys.list({ type }),
    queryFn: () => {
      if (!type) {
        throw new Error('Type is required');
      }
      return getGameLayoutList({ type });
    },
    enabled: enabled && !!type
  });

  return query;
};

export default useGameLayoutQuery;
