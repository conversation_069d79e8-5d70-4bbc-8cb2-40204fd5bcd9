import { useMutation } from '@tanstack/react-query';
import { Col, Row } from 'antd';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { updateGameLayout } from '@/api/game';
import RButton from '@/components/RButton';
import RForm from '@/components/RForm';
import RRadio from '@/components/RRadio';
import TableSearchLayout from '@/layout/TableSearchLayout';
import { useGameCategoryQuery } from '@/pages/game/management/hooks';
import { GameLayoutItem, OtherGameLayoutMode } from '@/types/game';

import GameCategorySidebar from './components/GameLayoutSidebar';
import GameLayoutTable from './components/GameLayoutTable';
import useGameLayoutQuery from './hooks/useGameLayoutQuery';

const defaultLayoutConfig = {
  maxItems: 20
};

const GameLayoutTypeHOME = 'home';
const GameLayoutTypePOPULAR = 'popular';
const GameLayoutTypeALL = 'all';

const GameLayoutPage = () => {
  const { t } = useTranslation();
  const [selectedItem, setSelectedItem] = useState<string | undefined>(); // 電子
  const [finalPopularGameLayoutData, setFinalGameLayoutData] = useState<GameLayoutItem[]>([]);
  const [finalAllGameLayoutData, setFinalAllGameLayoutData] = useState<GameLayoutItem[]>([]);
  const [form] = RForm.useForm();

  const gameCategoryQuery = useGameCategoryQuery();

  useEffect(() => {
    if (gameCategoryQuery.data && gameCategoryQuery.data.length > 0) {
      setSelectedItem(gameCategoryQuery.data[0].category);
    }
  }, [gameCategoryQuery.data]);

  const selectedCategory = gameCategoryQuery.data?.find(
    (category) => category.category === selectedItem
  );

  const typeToQuery = useMemo(() => {
    if (!selectedItem) {
      return null;
    }
    if (selectedItem === GameLayoutTypeHOME) {
      return [GameLayoutTypePOPULAR, GameLayoutTypeALL];
    }
    return [selectedItem];
  }, [selectedItem]);

  // Data fetching and mutations
  const popularGameLayoutQuery = useGameLayoutQuery({
    type: typeToQuery && typeToQuery.length > 0 ? typeToQuery[0] : undefined,
    enabled: (typeToQuery && typeToQuery.length > 0) || false
  });

  const allGameLayoutQuery = useGameLayoutQuery({
    type: typeToQuery && typeToQuery.length > 1 ? typeToQuery[1] : undefined,
    enabled: selectedItem === GameLayoutTypeHOME
  });

  const updateGameLayoutMutation = useMutation({
    mutationFn: updateGameLayout
  });

  useEffect(() => {
    if (allGameLayoutQuery.data?.data) {
      const { data } = allGameLayoutQuery.data;
      form.setFieldsValue({
        otherGameOrderMode: data.otherGameOrderMode
      });
      setFinalAllGameLayoutData(data.data);
      if (popularGameLayoutQuery.data?.data) {
        setFinalGameLayoutData(popularGameLayoutQuery.data?.data.data || []);
      }
    } else if (popularGameLayoutQuery.data?.data) {
      const { data } = popularGameLayoutQuery.data;
      form.setFieldsValue({
        otherGameOrderMode: data.otherGameOrderMode
      });
      setFinalGameLayoutData(data.data);
    }
  }, [popularGameLayoutQuery.data, allGameLayoutQuery.data, form]);

  // Handle category selection from sidebar
  const handleCategorySelect = (type: string) => {
    setSelectedItem(type);
    // Reset form when switching categories
    form.resetFields();
  };

  // Handle save
  const handleSave = () => {
    form
      .validateFields()
      .then(async (values) => {
        if (selectedItem === GameLayoutTypeHOME) {
          await updateGameLayoutMutation.mutateAsync({
            type: typeToQuery && typeToQuery.length > 1 ? typeToQuery[1] : '',
            orders: finalAllGameLayoutData.map((item) => item.id),
            otherGameOrderMode: values.otherGameOrderMode
          });
        }
        return updateGameLayoutMutation.mutateAsync({
          type: typeToQuery && typeToQuery.length > 0 ? typeToQuery[0] : '',
          orders: finalPopularGameLayoutData.map((item) => item.id),
          otherGameOrderMode: values.otherGameOrderMode
        });
      })
      .then(() => {
        popularGameLayoutQuery.refetch();
        if (selectedItem === GameLayoutTypeHOME) {
          allGameLayoutQuery.refetch();
        }
      })
      .catch((errorInfo) => {
        console.log('Validation failed:', errorInfo);
      });
  };

  return (
    <TableSearchLayout>
      <Row>
        {/* Left Sidebar */}
        <Col span={4} className="border-r border-gray-200 shadow-sm">
          <GameCategorySidebar
            selectedItem={selectedItem}
            onItemSelect={handleCategorySelect}
            items={[
              {
                key: 'game_category',
                labelKey: t('game_order_game_category'),
                children:
                  gameCategoryQuery.data?.map((category) => ({
                    key: category.category,
                    labelKey: category.name
                  })) || []
              },
              {
                key: 'home_layout',
                labelKey: t('game_order_home_layout'),
                children: [{ key: GameLayoutTypeHOME, labelKey: t('game_category_home') }]
              }
            ]}
          />
        </Col>

        {/* Main Content */}
        <Col span={20} className="border-r border-gray-200 shadow-sm">
          <div className="bg-white p-6">
            {/* Header Section */}
            <div className="mb-6 border-b border-gray-100 flex items-center gap-1">
              <h2 className="text-sm font-semibold text-text">
                {t('game_layout_setting_title', {
                  type: selectedCategory?.name || ''
                })}
              </h2>
              <p className="text-text-secondary text-xs">{t('game_layout_setting_description')}</p>
            </div>

            {/* Layout Mode Section */}
            <RForm form={form}>
              <div className="mb-6">
                <p className="text-xs font-medium text-text">{t('game_layout_sort_mode_title')}</p>
                <p className="text-xs text-text-secondary mb-2">
                  {selectedItem === GameLayoutTypeHOME
                    ? t('game_layout_sort_mode_home_description')
                    : t('game_layout_sort_mode_description')}
                </p>
                <RForm.Item name="otherGameOrderMode" initialValue={OtherGameLayoutMode.UNKNOWN}>
                  <RRadio.Group name="otherGameOrderMode" className="mb-3">
                    <div className="space-y-2">
                      <RRadio value={OtherGameLayoutMode.UNKNOWN} className="block">
                        {t('game_layout_sort_mode_unknown')}
                      </RRadio>
                      <RRadio value={OtherGameLayoutMode.AUTO} className="block">
                        {t('game_layout_sort_mode_auto')}
                      </RRadio>
                    </div>
                  </RRadio.Group>
                </RForm.Item>
              </div>

              {/* Game Layout Table */}
              <div className="mb-6">
                <GameLayoutTable
                  data={finalPopularGameLayoutData}
                  loading={popularGameLayoutQuery.isFetching}
                  maxItems={defaultLayoutConfig.maxItems}
                  onChange={setFinalGameLayoutData}
                  title={
                    selectedItem === GameLayoutTypeHOME
                      ? t('game_layout_sort_top_home_title')
                      : t('game_layout_sort_top_title')
                  }
                  description={
                    selectedItem === GameLayoutTypeHOME
                      ? t('game_layout_sort_top_home_description')
                      : t('game_layout_sort_top_description')
                  }
                  preselectCategoryId={
                    selectedItem !== GameLayoutTypeHOME ? selectedCategory?.id : undefined
                  }
                />
              </div>

              {selectedItem === GameLayoutTypeHOME && (
                <div className="mb-6">
                  <GameLayoutTable
                    data={finalAllGameLayoutData}
                    loading={allGameLayoutQuery.isFetching}
                    maxItems={defaultLayoutConfig.maxItems}
                    onChange={setFinalAllGameLayoutData}
                    title={t('game_layout_sort_all_title')}
                    description={t('game_layout_sort_all_description')}
                  />
                </div>
              )}

              {/* Save Button */}
              <div className="flex justify-start">
                <RButton
                  type="primary"
                  onClick={handleSave}
                  className="px-8"
                  loading={updateGameLayoutMutation.isPending}
                >
                  {t('common_save')}
                </RButton>
              </div>
            </RForm>
          </div>
        </Col>
      </Row>
    </TableSearchLayout>
  );
};

export default GameLayoutPage;
