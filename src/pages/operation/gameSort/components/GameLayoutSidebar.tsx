import { Menu } from 'antd';

interface GameLayoutSidebarProps {
  selectedItem?: string;
  onItemSelect: (type: string) => void;
  items: Item[];
}

interface Item {
  key: string;
  labelKey: string;
  children: Omit<Item, 'children'>[];
}

const GameLayoutSidebar = ({ selectedItem, onItemSelect, items }: GameLayoutSidebarProps) => {
  const itemsWillUse = useMemo(() => {
    return items.map((item) => ({
      key: item.key,
      label: item.labelKey,
      children: item.children.map((child) => ({
        key: child.key,
        label: child.labelKey
      }))
    }));
  }, [items]);

  return (
    <div className="h-full bg-white">
      <Menu
        mode="inline"
        selectedKeys={selectedItem ? [selectedItem] : []}
        activeKey={selectedItem}
        onClick={({ key }) => onItemSelect(key)}
        items={itemsWillUse}
      />
    </div>
  );
};

export default GameLayoutSidebar;
