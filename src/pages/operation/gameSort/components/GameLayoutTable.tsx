import { Button } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import ActionAddIcon from '@/assets/img/icon/action-add.svg?react';
import DragTable from '@/components/DragTable';
import GameSelectionModal from '@/components/GameSelectionModal/GameSelectionModal';
import type { RTableColumnsType } from '@/components/RTable';
import useActions from '@/hooks/useActions';
import { GameLayoutItem } from '@/types/game';

interface GameLayoutTableProps {
  data: GameLayoutItem[];
  loading: boolean;
  maxItems: number;
  onChange?: (data: GameLayoutItem[]) => void;
  title: string;
  description: string;
  preselectCategoryId?: string;
}

const GameLayoutTable = ({
  data,
  loading,
  maxItems,
  onChange,
  title,
  description,
  preselectCategoryId,
}: GameLayoutTableProps) => {
  const { t } = useTranslation();
  const [dataSource, setDataSource] = useState<GameLayoutItem[]>([]);
  const { handleDelete } = useActions();
  const [isOpenGameSelectModal, setIsOpenGameSelectModal] = useState(false);

  const onDelete = (record: GameLayoutItem) => {
    handleDelete(t('game_layout_table_name'), () => {
      changeDataSource(dataSource.filter((item) => item.id !== record.id));
    });
  };

  const changeDataSource = (data: GameLayoutItem[]) => {
    onChange?.(data);
    setDataSource(data);
  };

  const columns: RTableColumnsType<GameLayoutItem> = [
    {
      title: t('game_layout_table_image'),
      dataIndex: 'icon',
      key: 'icon',
      width: 80,
      render: (icon: string, record: GameLayoutItem) => {
        if (record.id === '__add__') {
          return (
            <div
              className="w-12 h-12 flex items-center justify-center transition-colors cursor-pointer"
              onClick={() => setIsOpenGameSelectModal(true)}
            >
              <ActionAddIcon className="text-lg fill-text-secondary"/>
            </div>
          );
        }
        return (
          <div className="w-12 h-12 bg-gray-200 rounded flex items-center justify-center">
            {icon ? (
              <img src={icon} alt="game" className="w-full h-full object-cover rounded" />
            ) : (
              <span className="text-gray-400 text-xs">IMG</span>
            )}
          </div>
        );
      }
    },
    {
      title: t('game_layout_table_name'),
      dataIndex: 'name',
      key: 'name',
      render: (name: string) => {
        return name;
      }
    },
    {
      title: t('game_layout_table_provider'),
      dataIndex: 'provider',
      key: 'provider',
      render: (provider: string, record: GameLayoutItem) => {
        if (record.id === '__add__') {
          return null;
        }
        return provider;
      }
    },
    {
      title: t('game_layout_table_category'),
      dataIndex: 'category',
      key: 'category',
      render: (category: string, record: GameLayoutItem) => {
        if (record.id === '__add__') {
          return null;
        }
        return category;
      }
    },
    {
      title: t('common_action'),
      key: 'action',
      render: (_: unknown, record: GameLayoutItem) => {
        if (record.id === '__add__') {
          return null;
        }
        return (
          <Button
            size="small"
            variant="outlined"
            color="red"
            type="link"
            onClick={() => onDelete(record)}
          >
            {t('common_delete')}
          </Button>
        );
      }
    }
  ];

  useEffect(() => {
    setDataSource(data || []);
  }, [data]);

  const handleChangeOrder = (sortableData: GameLayoutItem[]) => {
    // sortableData only contains the sortable rows (excluding the first row)
    changeDataSource(sortableData);
    // updateFrontContentOrderMutation.mutate({ orders: sortableData.map((item) => item.id) });
  };

  const dataWillRender = useMemo(() => {
    return [{ id: '__add__' }].concat(dataSource) as GameLayoutItem[];
  }, [dataSource]);

  const selectedGameIds = useMemo(() => {
    return dataSource.map((item) => item.id);
  }, [dataSource]);

  return (
    <>
      <div className="flex items-center gap-1">
        <h3 className="text-xs font-medium">{title}</h3>
        <p>
          (<span className="text-warning">{dataSource.length}</span>/{maxItems})
        </p>
      </div>
      <p className="text-xs text-gray-400 mb-4">{description}</p>
      <div className="bg-white rounded-lg border border-gray-200">
        <DragTable
          keyName="id"
          onSortChange={handleChangeOrder}
          loading={loading}
          rowKey="id"
          dataSource={dataWillRender}
          columns={columns}
          pagination={false}
          excludeFirstRowFromDrag={true}
        ></DragTable>
        {isOpenGameSelectModal && (
          <GameSelectionModal
            open={isOpenGameSelectModal}
            onClose={() => setIsOpenGameSelectModal(false)}
            onConfirm={(selectedGames) => {
              changeDataSource(
                dataSource.concat(
                  selectedGames.map(
                    (game) =>
                      ({
                        id: game.id,
                        provider: game.provider,
                        category: game.category,
                        name: game.name,
                        icon: game.icon
                      }) as GameLayoutItem
                  )
                )
              );
              setIsOpenGameSelectModal(false);
            }}
            disabledGameIds={selectedGameIds}
            multiSelect={true}
            maxItems={maxItems}
            preselectCategoryId={preselectCategoryId}
          />
        )}
      </div>
    </>
  );
};

export default GameLayoutTable;
