import { useQuery } from '@tanstack/react-query';

import { getFrontContent } from '@/api/operation';

// Query keys
export const frontContentKeys = {
  all: ['fontContent'] as const,
  lists: () => [...frontContentKeys.all, 'list'] as const,
  details: () => [...frontContentKeys.all, 'detail'] as const,
  detail: (id: number | string) => [...frontContentKeys.details(), id] as const
};

const useFrontContent = () => {
  const query = useQuery({
    queryKey: frontContentKeys.lists(),
    queryFn: () => getFrontContent(),
    select: (data) => data.data?.sort((a, b) => a.order - b.order) || []
  });

  return query;
};

export default useFrontContent;
