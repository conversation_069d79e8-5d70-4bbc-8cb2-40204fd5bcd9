import { useQuery } from '@tanstack/react-query';

import { getContentCategoryList } from '@/api/operation';

// Query keys
export const contentCategoryKeys = {
  all: ['contentCategory'] as const,
  lists: () => [...contentCategoryKeys.all, 'list'] as const,
  details: () => [...contentCategoryKeys.all, 'detail'] as const,
  detail: (id: number | string) => [...contentCategoryKeys.details(), id] as const
};

const useContentCategories = () => {
  const query = useQuery({
    queryKey: contentCategoryKeys.lists(),
    queryFn: () => getContentCategoryList(),
    select: (data) => data.data?.data || []
  });

  return query;
};

export default useContentCategories;
