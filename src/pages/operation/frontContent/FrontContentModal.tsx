import { useMutation } from '@tanstack/react-query';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { createFrontContent, updateFrontContent } from '@/api/operation';
import FormModal from '@/components/FormModal';
import HTMLEditor from '@/components/HTMLEditor';
import LanguageSelectTab from '@/components/LanguageSelectTab';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RSelect from '@/components/RSelect';
import RSwitch from '@/components/RSwitch';
import { FrontContent } from '@/types/operation';

import useContentCategories from './hooks/useContentCategories';
import useFrontContent from './hooks/useFrontContent';

type FrontContentModalProps = {
  open: boolean;
  onClose: () => void;
  initialValues?: Partial<FrontContent>;
};

type FormValue = Pick<FrontContent, 'id' | 'contentMapping' | 'order' | 'status' | 'categoryId'> & {
  timeRange: [number, number];
};

const FrontContentModal = ({ open, onClose, initialValues }: FrontContentModalProps) => {
  const { t } = useTranslation();
  const [form] = RForm.useForm<FormValue>();
  const [languageList, setLanguageList] = useState<string[]>([]);
  const [activeLanguage, setActiveLanguage] = useState('');

  const isEdit = !!initialValues?.id;

  const contentCategoriesQuery = useContentCategories();
  const frontContentQuery = useFrontContent();

  const categoryOptions = useMemo(() => {
    return (
      contentCategoriesQuery.data?.map((category) => ({
        label: category.name,
        value: category.id
      })) || []
    );
  }, [contentCategoriesQuery.data]);

  const handleClose = () => {
    setLanguageList([]);
    setActiveLanguage('');
    onClose();
  };

  const onSuccess = () => {
    frontContentQuery.refetch();
    handleClose();
  };

  const createFrontContentMutation = useMutation({
    mutationFn: createFrontContent,
    onSuccess
  });

  const updateFrontContentMutation = useMutation({
    mutationFn: updateFrontContent,
    onSuccess
  });

  const handleSubmit = (values: FormValue) => {
    if (isEdit) {
      updateFrontContentMutation.mutate({ ...values, id: Number(initialValues.id) });
    } else {
      createFrontContentMutation.mutate({ ...values, order: 1 });
    }
  };

  const handleChangeLanguage = (value: string) => {
    setActiveLanguage(value);
  };

  useEffect(() => {
    if (initialValues) {
      setLanguageList(Object.keys(initialValues.contentMapping || {}));
      const timeout = setTimeout(() => {
        form.setFieldsValue({ ...initialValues });
      }, 100);
      return () => clearTimeout(timeout);
    }
  }, [initialValues, form]);

  const isLoading = createFrontContentMutation.isPending || updateFrontContentMutation.isPending;

  return (
    <FormModal<FormValue>
      title={
        isEdit
          ? t('common_edit_name', { name: t('pages_frontContent') })
          : t('common_add_name', { name: t('pages_frontContent') })
      }
      form={form}
      open={open}
      onClose={handleClose}
      onSubmit={handleSubmit}
      isLoading={isLoading}
      initialValues={initialValues}
      formProps={{
        onFinishFailed: (errorInfo) => {
          const contentMappingError = errorInfo.errorFields.find(
            (field) => field.name[0] === 'contentMapping' && field.name[1] !== activeLanguage
          );

          if (contentMappingError) {
            setActiveLanguage(contentMappingError.name[1] as string);
          }
        }
      }}
    >
      <div className="grid grid-cols-2 gap-x-15">
        <RForm.Item name="id" noStyle></RForm.Item>
        <RForm.Item
          name="categoryId"
          label={t('pages_frontContent_category')}
          rules={[{ required: true }]}
        >
          <RSelect options={categoryOptions} />
        </RForm.Item>
        <RForm.Item
          label={t('common_status')}
          name="status"
          valuePropName="checked"
          initialValue={1}
          required={true}
        >
          <RSwitch />
        </RForm.Item>
      </div>
      <LanguageSelectTab
        languageList={languageList}
        onChange={setLanguageList}
        activeLanguage={activeLanguage}
        setActiveLanguage={handleChangeLanguage}
      />
      {languageList.map((language) => {
        return (
          <div key={language} style={{ display: activeLanguage === language ? 'block' : 'none' }}>
            <RForm.Item
              name={['contentMapping', language, 'title']}
              label={t('pages_frontContent_title')}
              rules={[{ required: true }]}
              className="!mt-4"
            >
              <RInput />
            </RForm.Item>
            <RForm.Item
              name={['contentMapping', language, 'content']}
              label={t('pages_frontContent_content')}
              rules={[{ required: true }]}
            >
              <HTMLEditor />
            </RForm.Item>
          </div>
        );
      })}
    </FormModal>
  );
};

export default FrontContentModal;
