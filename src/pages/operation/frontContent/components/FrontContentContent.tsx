import useFrontendLanguage from '@/hooks/useFrontendLanguage';
import { FrontContent } from '@/types/operation';

type FrontContentContentProps = {
  record: FrontContent;
  onClick?: () => void;
};

const FrontContentContent = ({ record, onClick }: FrontContentContentProps) => {
  const { defaultFrontendLanguage } = useFrontendLanguage();
  const defaultContent =
    record.contentMapping?.[defaultFrontendLanguage] || Object.values(record.contentMapping)[0];

  return (
    <div
      className={`line-clamp-2 ${onClick ? 'cursor-pointer hover:text-blue-600 transition-colors' : ''}`}
      dangerouslySetInnerHTML={{ __html: defaultContent.content }}
      onClick={onClick}
    />
  );
};

export default FrontContentContent;
