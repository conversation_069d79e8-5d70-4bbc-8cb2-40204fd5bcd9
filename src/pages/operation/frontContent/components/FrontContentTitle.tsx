import useFrontendLanguage from '@/hooks/useFrontendLanguage';
import { FrontContent } from '@/types/operation';

const FrontContentTitle = ({ record }: { record: FrontContent }) => {
  const { defaultFrontendLanguage } = useFrontendLanguage();
  const defaultContent =
    record.contentMapping?.[defaultFrontendLanguage] || Object.values(record.contentMapping)[0];
  return <p>{defaultContent.title}</p>;
};

export default FrontContentTitle;
