import { useMutation } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';

import {
  createContentCategory,
  deleteContentCategory,
  updateContentCategory
} from '@/api/operation';
import EditableTable, { EditableTableColumn } from '@/components/EditableTable';
import RModal from '@/components/RModal';
import useActions from '@/hooks/useActions';
import useEditableTable from '@/hooks/useEditableTable';
import { ContentCategory } from '@/types/operation';

import useContentCategories from './hooks/useContentCategories';

type ContentCategoryModalProps = {
  open: boolean;
  onClose: () => void;
};

type FormValue = {
  key: string;
  name: string;
  description: string;
};

const ContentCategoryModal = ({ open, onClose }: ContentCategoryModalProps) => {
  const { t } = useTranslation();
  const { handleDelete } = useActions();

  const categoriesQuery = useContentCategories();

  // Use the reusable hook for managing editable table state
  const {
    form,
    editingId,
    isAdding,
    handleAdd,
    handleEdit,
    handleCancel,
    handleCreateSuccess,
    handleUpdateSuccess,
    handleDeleteSuccess
  } = useEditableTable<FormValue>({
    resetOnModalClose: true,
    modalOpen: open
  });

  const { mutate: createCategoryMutation, isPending: isCreatePending } = useMutation({
    mutationFn: createContentCategory,
    onSuccess: () => {
      categoriesQuery.refetch();
      handleCreateSuccess();
    }
  });

  const { mutate: updateCategoryMutation, isPending: isUpdatePending } = useMutation({
    mutationFn: updateContentCategory,
    onSuccess: () => {
      categoriesQuery.refetch();
      handleUpdateSuccess();
    }
  });

  const deleteCategoryMutation = useMutation({
    mutationFn: deleteContentCategory,
    onSuccess: () => {
      categoriesQuery.refetch();
      handleDeleteSuccess();
    }
  });

  const handleSave = (
    values: Partial<FormValue>,
    isEdit: boolean,
    editingIdParam?: string | number
  ) => {
    // Ensure all required fields are present
    const formData = values as FormValue;

    if (isEdit && editingIdParam !== undefined) {
      updateCategoryMutation({ ...formData, id: editingIdParam as number });
    } else {
      createCategoryMutation(formData);
    }
  };

  const handleDeleteCategory = (category: ContentCategory) => {
    handleDelete(t('pages_contentCategory'), () => {
      deleteCategoryMutation.mutate({ id: category.id });
    });
  };

  // Define columns configuration for the editable table
  const columns: EditableTableColumn<ContentCategory>[] = [
    {
      key: 'key',
      title: t('pages_contentCategory_key'),
      placeholder: t('pages_contentCategory_key_placeholder'),
      rules: [{ required: true }]
    },
    {
      key: 'name',
      title: t('pages_contentCategory_name'),
      placeholder: t('pages_contentCategory_name_placeholder'),
      rules: [{ required: true }]
    },
    {
      key: 'description',
      title: t('pages_contentCategory_description'),
      placeholder: t('pages_contentCategory_description_placeholder'),
      rules: [{ required: true }]
    }
  ];

  return (
    <RModal
      title={t('pages_contentCategory_management')}
      open={open}
      onCancel={onClose}
      width={1000}
      footer={null}
    >
      <EditableTable<ContentCategory, FormValue>
        data={categoriesQuery.data || []}
        columns={columns}
        onSave={handleSave}
        onDelete={handleDeleteCategory}
        loading={{
          create: isCreatePending,
          update: isUpdatePending
        }}
        rowKey="id"
        addButtonText={t('common_add_name', { name: t('pages_contentCategory') })}
        form={form}
        editingId={editingId}
        isAdding={isAdding}
        onEdit={handleEdit}
        onCancel={handleCancel}
        onStartAdd={handleAdd}
      />
    </RModal>
  );
};

export default ContentCategoryModal;
