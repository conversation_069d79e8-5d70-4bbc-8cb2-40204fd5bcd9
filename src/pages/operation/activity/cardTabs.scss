.card-tabs.ant-tabs {
  .ant-tabs-nav {
    .ant-tabs-tab {
      position: relative;
      border-top: none;
      width: 120px;
      justify-content: center;
      padding: 8px;
      border: 1px solid var(--color-component-border);

      &.ant-tabs-tab-active {
        .ant-tabs-tab-btn {
          color: var(--color-text) !important;
        }
        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 3px;
          background: #1d252d;
          border-radius: 8px 8px 0 0;
        }
      }
    }
  }
}

.vertical-form-item {
  display: flex !important;
  flex-direction: column !important;
  align-items: flex-start !important;
  margin-bottom: 0 !important;
  
  .ant-form-item-label {
    display: flex !important;
    align-items: center !important;
    margin-bottom: 8px !important;
    margin-right: 0 !important;
    min-width: fit-content !important;
  }
  
  .ant-form-item-control {
    flex: 1 !important;
    margin-left: 0 !important;
    width: 100% !important;
  }
  
  .ant-form-item {
    display: flex !important;
    flex-direction: column !important;
    align-items: flex-start !important;
    margin-bottom: 0 !important;
    width: 100% !important;
  }
  
  .ant-form-item-row {
    display: flex !important;
    flex-direction: column !important;
    align-items: flex-start !important;
    width: 100% !important;
  }
}



.activity-list-tabs .ant-tabs-nav {
  background-color: #fff;;
}
.shadow-content {
  box-shadow: none !important;
}