import './cardTabs.scss';

import { Spin } from 'antd';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { RButton } from '@/components/RButton';
import RForm from '@/components/RForm';
import RInputNumber from '@/components/RInputNumber';
import RSelect from '@/components/RSelect';
import { RTabs } from '@/components/RTabs';

import { ConditionTab } from './components/ConditionTab';
import { InfoTab } from './components/InfoTab';
import { useActivityForm } from './hooks/useActivityForm';

// 定義表單值的類型，對應API參數
export interface ActivityFormValues {
  templateType: number;
  order: number;
  isReview: 0 | 1;
  zhTwTitle: string;
  zhTwContent: string;
  zhTwPhoto?: File;
  needApply: boolean;
  openCta: boolean;
  joinLevel?: number;
  joinMode: 0 | 1 | 2;
  tags?: number[];
  frozenTime: number;
  withdrawRewardTime: number;
  giftMoneyMode: 1 | 2;
  giftMoneyCondition: {
    min: number | null;
    max: number | null;
    feedback?: number;
    feedbackPercent?: number;
    feedbackUpperLimit?: number;
  }[];
}

export interface CreateActivityModalProps {
  activityTemplate: { id: number; label: string }[];
  onCancel?: () => void;
  activityId?: number;
}

const CreateActivityModal = ({
  activityTemplate,
  onCancel,
  activityId
}: CreateActivityModalProps) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('1');

  const {
    form,
    initialValues,
    validationRules,
    handleFormValuesChange,
    handleSubmit,
    isCreateActivityPending,
    isEditMode,
    isActivityDetailLoading,
    activityDetail
  } = useActivityForm(activityId, onCancel, setActiveTab);

  const tabItems = [
    {
      key: '1',
      label: t('pages_activity_info'),
      children: (
        <InfoTab
          validationRules={validationRules}
          form={form}
          isEditMode={isEditMode}
          activityDetail={activityDetail}
        />
      ),
      forceRender: true
    },

    {
      key: '2',
      label: t('pages_activity_condition'),
      children: <ConditionTab form={form} validationRules={validationRules} />,
      forceRender: true
    }
  ];

  const templateOptions =
    activityTemplate?.map((item: { id: number; label: string }) => ({
      label: item.label,
      value: item.id
    })) || [];

  return (
    <Spin spinning={isActivityDetailLoading}>
      <div className="bg-white p-4 m-3">
        <RForm
          form={form}
          initialValues={initialValues || {}}
          onValuesChange={handleFormValuesChange}
          onFinish={handleSubmit}
        >
          <div className="flex items-center gap-2">
            <RForm.Item
              label={t('pages_activity_templateType')}
              name="templateType"
              required
              rules={[validationRules.required]}
            >
              <RSelect options={templateOptions} />
            </RForm.Item>

            <RForm.Item
              label={t('pages_activity_order')}
              name="order"
              required
              rules={[validationRules.required]}
            >
              <RInputNumber min={1} placeholder={t('placeholder_input')} />
            </RForm.Item>
          </div>

          <RTabs
            type="card"
            items={tabItems}
            className="card-tabs"
            onChange={setActiveTab}
            activeKey={activeTab}
            destroyInactiveTabPane={false}
          />

          {/* 表單操作按鈕 */}
          <div className="mx-auto mt-5 w-66">
            <RButton
              type="primary"
              onClick={handleSubmit}
              loading={isCreateActivityPending}
              className="!w-31"
            >
              {isEditMode ? t('common_save') : t('common_save_draft')}
            </RButton>
            <RButton type="primary" variant="outlined" className="ml-4 !w-31" onClick={onCancel}>
              {t('common_cancel')}
            </RButton>
          </div>
        </RForm>
      </div>
    </Spin>
  );
};

export default CreateActivityModal;
