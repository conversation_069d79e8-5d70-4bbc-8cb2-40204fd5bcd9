export interface TabFieldMapping {
  [tabKey: string]: string[];
}

/**
 * 獲取字段所属的 Tab
 * @param fieldName - 字段
 * @param fieldMapping - 字段映射配置
 * @param defaultTab - 默認Tab
 * @returns Tab 的 key
 */
export const getTabForField = (
  fieldName: string,
  fieldMapping: TabFieldMapping,
  defaultTab: string = '1'
): string => {
  for (const [tabKey, fields] of Object.entries(fieldMapping)) {
    if (fields.includes(fieldName)) {
      return tabKey;
    }
  }

  // 處理嵌套字段，如 giftMoneyCondition[0].min
  for (const [tabKey, fields] of Object.entries(fieldMapping)) {
    const hasNestedField = fields.some((field) => fieldName.startsWith(field));
    if (hasNestedField) {
      return tabKey;
    }
  }

  return defaultTab;
};

/**
 * 活動表單的字段配置表
 */
export const ACTIVITY_FORM_TAB_MAPPING: TabFieldMapping = {
  '1': [
    'zhTwTitle',
    'zhTwContent',
    'zhTwPhoto',
    'needApply',
    'openCta',
    'templateType', // 默認切換到第一個tab
    'order'
  ],
  '2': [
    'isReview',
    'joinLevel',
    'frozenTime',
    'withdrawRewardTime',
    'joinMode',
    'tags',
    'giftMoneyMode',
    'giftMoneyCondition',
    'prop'
  ]
};

export const getActivityTabForField = (fieldName: string): string => {
  return getTabForField(fieldName, ACTIVITY_FORM_TAB_MAPPING, '1');
};
