import { useMutation, useQueryClient } from '@tanstack/react-query';
import { createContext, useContext, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { unpublishActivity } from '@/api/activity';
import { ActionButtons } from '@/components/ActionButtons';
import RButton from '@/components/RButton';
import RForm from '@/components/RForm';
import RSelect from '@/components/RSelect';
import { RTable } from '@/components/RTable';
import RTag from '@/components/RTag';
import SearchForm from '@/components/SearchForm';
import useConfirmModal from '@/hooks/useConfirmModal';
import TableSearchLayout from '@/layout/TableSearchLayout';
import { formatConductWeek } from '@/pages/operation/activity/activity';
import { Activity } from '@/types/activity';
import { formatTime, formatTimeToMinute } from '@/utils/time';

import { ActivityStatus, generateActivityStatusMap } from './components/activityStatusConfig';
import PublishModal from './components/PublishModal';
import ReadModal from './components/ReadModal';
import { useActivityOptions } from './hooks/useActivityContext';
import {
  activityKeys,
  type ActivitySearchParams,
  useActiveActivityQuery
} from './hooks/useActivityQuery';

type SearchFormValues = ActivitySearchParams;

type OptionsContextType = {
  templateOptions: { label: string; value: number }[];
  statusOptions: { label: string; value: number }[];
};

const OptionsContext = createContext<OptionsContextType>({
  templateOptions: [],
  statusOptions: []
});

const getAvailableButtons = (status: number): ('edit' | 'publish' | 'read' | 'unpublish')[] => {
  switch (status) {
    case ActivityStatus.DRAFT: // 草稿
      return ['publish', 'edit', 'read'];
    case ActivityStatus.SCHEDULED: // 已排定
      return ['read', 'unpublish'];
    case ActivityStatus.UPCOMING: // 即將開始
      return ['read', 'unpublish'];
    case ActivityStatus.IN_PROGRESS: // 進行中
      return ['read', 'unpublish'];
    case ActivityStatus.ENDED: // 已結束
      return ['read', 'unpublish'];
    // case ActivityStatus.WITHDRAW_EXPIRED: // 已過期下架
    // case ActivityStatus.WITHDRAW_MANUAL: // 手動下架
    //   return ['read'];
    default:
      return ['read'];
  }
};

const PublishButton = ({ onPublish }: { onPublish: () => void }) => {
  const { t } = useTranslation();
  return (
    <RButton size="small" variant="outlined" color="orange" type="link" onClick={onPublish}>
      {t('common_publish')}
    </RButton>
  );
};

const UnpublishButton = ({ onUnpublish }: { onUnpublish: () => void }) => {
  const { t } = useTranslation();
  return (
    <RButton size="small" variant="outlined" color="red" type="link" onClick={onUnpublish}>
      {t('common_unpublish')}
    </RButton>
  );
};

const SearchFormWrap = ({
  onSearch,
  onReset
}: {
  onSearch: (values: SearchFormValues) => void;
  onReset: () => void;
}) => {
  const { t } = useTranslation();
  const { templateOptions, statusOptions } = useContext(OptionsContext);

  const handleSearch = (values: SearchFormValues) => {
    onSearch(values);
  };

  return (
    <SearchForm<SearchFormValues> onSearch={handleSearch} onReset={onReset} className="">
      <RForm.Item name="templateType" label={t('pages_activity_templateType')}>
        <RSelect options={templateOptions} placeholder={t('common_all')} />
      </RForm.Item>
      <RForm.Item name="status" label={t('common_status')}>
        <RSelect options={statusOptions} placeholder={t('common_all')} />
      </RForm.Item>
      <RForm.Item name="needApply" label={t('pages_activity_needApply')}>
        <RSelect
          options={[
            { label: t('common_yes'), value: 1 as const },
            { label: t('common_no'), value: 0 as const }
          ]}
          placeholder={t('common_all')}
        />
      </RForm.Item>
      <RForm.Item name="isReview" label={t('pages_activity_isReview')}>
        <RSelect
          options={[
            { label: t('common_yes'), value: 1 as const },
            { label: t('common_no'), value: 0 as const }
          ]}
          placeholder={t('common_all')}
        />
      </RForm.Item>
    </SearchForm>
  );
};

const SearchParamsContext = createContext<SearchFormValues>({});

const createColumns = (
  t: (key: string) => string,
  templateOptions: { label: string; value: number }[],
  activityStatusMap: Record<number, { label: string; color: string; textColor: string }>,
  onEdit?: (activity: Activity) => void,
  onPublish?: (activity: Activity) => void,
  onRead?: (activity: Activity) => void,
  onUnpublish?: (activity: Activity) => void
) => [
  {
    title: t('common_order'),
    dataIndex: 'order'
  },
  {
    title: t('pages_activity_templateType'),
    dataIndex: 'templateType',
    render: (templateType: number) => {
      const template = templateOptions.find(
        (item: { value: number }) => item.value === templateType
      );
      return template?.label || '-';
    }
  },
  {
    title: t('pages_activity_name'),
    dataIndex: 'contentMapping',
    render: (contentMapping: Activity['contentMapping']) => {
      const title = contentMapping?.zhTw?.title;
      return title || '-';
    }
  },
  {
    title: t('components_formTimePeriodSelect_publishType'),
    dataIndex: 'shelfStartTime',
    render: (shelfStartTime: number, record: Activity) => {
      if (!shelfStartTime) return '-';
      return record.shelfEndTime
        ? `${formatTimeToMinute(shelfStartTime)} - ${formatTimeToMinute(record.shelfEndTime)}`
        : `${formatTimeToMinute(shelfStartTime)} - `;
    }
  },
  {
    title: t('pages_activity_period'),
    dataIndex: 'conductStartTime',
    render: (conductStartTime: number, record: Activity) => {
      // 處理星期的邏輯
      const conductWeekText = formatConductWeek(record.conductWeek, t);
      if (conductWeekText) {
        return conductWeekText;
      }

      if (!conductStartTime) return '-';
      return record.conductEndTime
        ? `${formatTimeToMinute(conductStartTime)} - ${formatTimeToMinute(record.conductEndTime)}`
        : `${formatTimeToMinute(conductStartTime)} - `;
    }
  },
  {
    title: t('pages_activity_needApply'),
    dataIndex: 'needApply',
    render: (needApply: number) => {
      return needApply ? t('common_yes') : t('common_no');
    }
  },
  {
    title: t('pages_activity_isReview'),
    dataIndex: 'isReview',
    render: (isReview: number) => {
      return isReview ? t('common_yes') : t('common_no');
    }
  },
  {
    title: t('common_lastOperate'),
    dataIndex: 'updatedBy',
    render: (updatedBy: string, record: Activity) => {
      return `${updatedBy} / ${formatTime(record.updatedAt)}`;
    }
  },
  {
    title: t('common_status'),
    dataIndex: 'status',
    render: (status: number) => {
      const config = activityStatusMap[status];
      return config ? (
        <RTag color={config.color} textColor={config.textColor}>
          {config.label}
        </RTag>
      ) : (
        '-'
      );
    }
  },
  {
    title: t('common_action'),
    dataIndex: 'action',
    render: (_: string, record: Activity) => {
      const availableButtons = getAvailableButtons(record.status);
      const actionButtons = availableButtons.filter(
        (button) => button === 'edit' || button === 'read'
      );

      return (
        <div className="flex gap-1">
          <ActionButtons data={record} buttons={actionButtons} onEdit={onEdit} onRead={onRead} />
          {availableButtons.includes('publish') && (
            <PublishButton onPublish={() => onPublish?.(record)} />
          )}
          {availableButtons.includes('unpublish') && (
            <UnpublishButton onUnpublish={() => onUnpublish?.(record)} />
          )}
        </div>
      );
    }
  }
];

const ActivityListTable = ({
  searchParams,
  onEdit,
  onPublish,
  onRead,
  onUnpublish
}: {
  searchParams: SearchFormValues;
  onEdit?: (activity: Activity) => void;
  onPublish?: (activity: Activity) => void;
  onRead?: (activity: Activity) => void;
  onUnpublish?: (activity: Activity) => void;
}) => {
  const { t } = useTranslation();
  const { data: activityList, isLoading } = useActiveActivityQuery(searchParams);
  const { templateOptions, statusOptions } = useContext(OptionsContext);

  // 動態生成活動狀態配置
  const activityStatusMap = useMemo(() => {
    if (statusOptions.length > 0) {
      const statusData = statusOptions.map((option) => ({
        id: option.value,
        label: option.label
      }));
      return generateActivityStatusMap(statusData);
    }
    return {};
  }, [statusOptions]);

  // 使用 useMemo 來緩存 columns 創建函數
  const columns = useMemo(
    () =>
      createColumns(t, templateOptions, activityStatusMap, onEdit, onPublish, onRead, onUnpublish),
    [t, templateOptions, activityStatusMap, onEdit, onPublish, onRead, onUnpublish]
  );

  return (
    <RTable
      rowKey="id"
      dataSource={activityList || []}
      columns={columns}
      pagination={false}
      loading={isLoading}
    />
  );
};

interface ActivityListProps {
  setShowCreatePage?: (show: boolean) => void;
  setEditingActivityId?: (id: number | undefined) => void;
}

const ActivityList = ({ setShowCreatePage, setEditingActivityId }: ActivityListProps) => {
  const { t } = useTranslation();
  const [searchParams, setSearchParams] = useState<SearchFormValues>({});
  const { templateOptions, statusOptions } = useActivityOptions();
  const [showPublishModal, setShowPublishModal] = useState(false);
  const [showReadModal, setShowReadModal] = useState(false);
  const [selectedActivity, setSelectedActivity] = useState<Activity | null>(null);
  const { confirmModal } = useConfirmModal();
  const queryClient = useQueryClient();

  const { mutate: unpublishActivityMutate } = useMutation({
    mutationFn: unpublishActivity,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: activityKeys.listWithParams('active', {}) });
    }
  });

  const handleSearch = (values: SearchFormValues) => {
    setSearchParams(values);
  };

  const handleReset = () => {
    setSearchParams({});
  };

  const handleEdit = (activity: Activity) => {
    if (setEditingActivityId) {
      setEditingActivityId(activity.id);
    }
    if (setShowCreatePage) {
      setShowCreatePage(true);
    }
  };

  const handlePublish = (activity: Activity) => {
    setSelectedActivity(activity);
    setShowPublishModal(true);
  };

  const handleRead = (activity: Activity) => {
    setSelectedActivity(activity);
    setShowReadModal(true);
  };

  const handleUnpublish = (activity: Activity) => {
    setSelectedActivity(activity);
    confirmModal({
      content: t('pages_activity_confirm_unpublish'),
      onOk: () => {
        unpublishActivityMutate({ id: activity.id });
      }
    });
  };

  const handleClosePublishModal = () => {
    setShowPublishModal(false);
    setSelectedActivity(null);
  };

  const handleCloseReadModal = () => {
    setShowReadModal(false);
    setSelectedActivity(null);
  };

  return (
    <OptionsContext.Provider value={{ templateOptions, statusOptions }}>
      <SearchParamsContext.Provider value={searchParams}>
        <TableSearchLayout
          searchFieldsContainerClassName=""
          searchFields={
            <>
              <SearchFormWrap onSearch={handleSearch} onReset={handleReset} />
            </>
          }
        >
          <RButton className="mb-5" onClick={() => setShowCreatePage && setShowCreatePage(true)}>
            {t('common_add_name', { name: t('pages_activity') })}
          </RButton>
          <ActivityListTable
            searchParams={searchParams}
            onEdit={handleEdit}
            onPublish={handlePublish}
            onRead={handleRead}
            onUnpublish={handleUnpublish}
          />
        </TableSearchLayout>
        <PublishModal
          open={showPublishModal}
          onClose={handleClosePublishModal}
          activity={selectedActivity}
        />
        <ReadModal
          open={showReadModal}
          onClose={handleCloseReadModal}
          activity={selectedActivity}
        />
      </SearchParamsContext.Provider>
    </OptionsContext.Provider>
  );
};

export default ActivityList;
