import { useTranslation } from 'react-i18next';

import { RTable } from '@/components/RTable';
import usePagination from '@/hooks/usePagination';
import { formatConductWeek } from '@/pages/operation/activity/activity';
import { DisActiveActivity } from '@/types/activity';
import { formatTime } from '@/utils/time';

import { useActivityOptions } from './hooks/useActivityContext';
import { useInactiveActivityQuery } from './hooks/useActivityQuery';

const columns = (
  t: (key: string) => string,
  templateOptions: { label: string; value: number }[],
  statusOptions: { label: string; value: number }[]
) => [
  {
    title: t('pages_activity_templateType'),
    dataIndex: 'templateType',
    render: (templateType: number) => {
      const template = templateOptions.find(
        (item: { value: number }) => item.value === templateType
      );
      return template?.label || '-';
    }
  },
  {
    title: t('pages_activity_name'),
    dataIndex: 'contentMapping',
    render: (contentMapping: DisActiveActivity['contentMapping']) => {
      const title = contentMapping?.zhTw?.title;
      return title || '-';
    }
  },
  {
    title: t('pages_activity_period'),
    dataIndex: 'conductStartTime',
    render: (conductStartTime: number, record: DisActiveActivity) => {
      const conductWeekText = formatConductWeek(record.conductWeek, t);
      if (conductWeekText) {
        return conductWeekText;
      }

      if (!conductStartTime) return '-';
      return record.conductEndTime
        ? `${formatTime(conductStartTime)} - ${formatTime(record.conductEndTime)}`
        : `${formatTime(conductStartTime)} -`;
    }
  },
  {
    title: t('common_status'),
    dataIndex: 'status',
    render: (status: number) => {
      const statusOption = statusOptions.find((item: { value: number }) => item.value === status);
      return statusOption?.label || '-';
    }
  },
  {
    title: t('common_lastOperate'),
    dataIndex: 'updatedBy',
    render: (updatedBy: string, record: DisActiveActivity) => {
      return `${updatedBy} / ${formatTime(record.updatedAt)}`;
    }
  }
];

const DisactiveListPage = () => {
  const { t } = useTranslation();
  const { page, setPage, limit, setLimit } = usePagination({});
  const { data: disactiveActivityList, isPending } = useInactiveActivityQuery(page, limit);
  const { templateOptions, statusOptions } = useActivityOptions();

  const handleChangePage = (page: number, pageSize: number) => {
    setPage(page);
    setLimit(pageSize);
  };

  return (
    <div className="px-4 mt-5">
      <RTable
        loading={isPending}
        rowKey="id"
        dataSource={disactiveActivityList?.data || []}
        columns={columns(t, templateOptions, statusOptions)}
        pagination={{
          current: page,
          pageSize: limit,
          total: disactiveActivityList?.total || 0,
          showSizeChanger: true,
          onChange: handleChangePage
        }}
      />
    </div>
  );
};

export default DisactiveListPage;
