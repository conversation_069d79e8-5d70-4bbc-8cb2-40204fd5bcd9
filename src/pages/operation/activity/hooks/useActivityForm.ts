import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';

import { createActivity, getActivityDetail, updateActivity } from '@/api/activity';
import RForm from '@/components/RForm';
import { getActivityTabForField } from '@/pages/operation/activity/form';

import { ActivityFormValues } from '../CreateActivityPage';
import { activityKeys } from './useActivityQuery';

const defaultGiftMoneyCondition = [
  {
    min: null,
    max: null,
    feedback: undefined,
    feedbackPercent: undefined,
    feedbackUpperLimit: undefined
  }
];

export const useActivityForm = (
  activityId?: number,
  onCancel?: () => void,
  setActiveTab?: (key: string) => void
) => {
  const { t } = useTranslation();
  const [form] = RForm.useForm<ActivityFormValues>();
  const queryClient = useQueryClient();
  const isEditMode = !!activityId;

  const isInit = useRef(false);

  const { data: activityDetail, isLoading: isActivityDetailLoading } = useQuery({
    queryKey: ['activityDetail', activityId],
    queryFn: () => getActivityDetail({ id: activityId! }),
    enabled: isEditMode && !!activityId
  });

  // 計算表單初始值，包含編輯模式的數據
  const getInitialValues = () => {
    if (isEditMode) {
      // 編輯模式下，如果 activityDetail 還沒加載完成，返回 null
      if (!activityDetail) {
        return null;
      }

      const activity = (activityDetail as any).data || activityDetail;

      // 處理 giftMoneyCondition
      let processedGiftMoneyCondition = defaultGiftMoneyCondition;
      if (activity.giftMoneyCondition && Array.isArray(activity.giftMoneyCondition)) {
        processedGiftMoneyCondition = activity.giftMoneyCondition.map((item: any) => ({
          min: item.min,
          max: item.max,
          feedback: item.feedback,
          feedbackPercent: item.feedbackPercent,
          feedbackUpperLimit: item.feedbackUpperLimit
        }));
      }

      // 根據API數據判斷giftMoneyMode
      let giftMoneyMode = activity.giftMoneyMode as 1 | 2;
      if (processedGiftMoneyCondition.length > 0) {
        const hasFeedback = processedGiftMoneyCondition.some(
          (item: any) => item.feedback !== undefined && item.feedback !== null
        );
        if (hasFeedback) {
          giftMoneyMode = 2; // 固定金額模式
        } else {
          giftMoneyMode = 1; // 比例模式
        }
      }

      return {
        templateType: activity.templateType,
        order: activity.order,
        isReview: activity.isReview as 0 | 1,
        zhTwTitle: activity.contentMapping?.zhTw?.title || '',
        zhTwContent: activity.contentMapping?.zhTw?.content || '',
        needApply: !!activity.needApply,
        openCta: !!activity.openCta,
        joinLevel: activity.joinLevel,
        joinMode: activity.joinMode as 0 | 1 | 2,
        tags: activity.tags.map((tag: any) => tag.id),
        frozenTime: activity.frozenTime,
        withdrawRewardTime: activity.withdrawRewardTime,
        giftMoneyMode: giftMoneyMode,
        giftMoneyCondition: processedGiftMoneyCondition
      };
    }

    // 新增模式的預設值
    return {
      templateType: 1,
      isReview: 0 as 0 | 1,
      joinMode: 0 as 0 | 1 | 2,
      giftMoneyMode: 1 as 1 | 2,
      needApply: false,
      openCta: false,
      giftMoneyCondition: defaultGiftMoneyCondition
    };
  };

  const initialValues = getInitialValues();

  // 重置 isInit
  useEffect(() => {
    if (activityId || (activityDetail && isEditMode)) {
      isInit.current = false;
    }
  }, [activityId, activityDetail, isEditMode]);

  useEffect(() => {
    if (initialValues && Object.keys(initialValues).length > 0 && !isInit.current) {
      form.setFieldsValue(initialValues);
      isInit.current = true;
    }
  }, [initialValues, form, isInit, activityDetail, isEditMode]);

  const { mutate: createActivityMutate, isPending: isCreateActivityPending } = useMutation({
    mutationFn: createActivity,
    onSuccess: () => {
      form.resetFields();
      onCancel?.();
      queryClient.invalidateQueries({ queryKey: activityKeys.listWithParams('active', {}) });
    },
    onError: (error) => {
      console.error('Failed to create activity:', error);
      console.error('Error details:', error);
    }
  });

  const { mutate: updateActivityMutate, isPending: isUpdateActivityPending } = useMutation({
    mutationFn: updateActivity,
    onSuccess: () => {
      form.resetFields();
      onCancel?.();
      queryClient.invalidateQueries({ queryKey: activityKeys.listWithParams('active', {}) });
    },
    onError: (error) => {
      console.error('Failed to update activity:', error);
      console.error('Error details:', error);
    }
  });

  // 通用驗證規則
  const validationRules = {
    required: { required: true, message: t('common_required') },
    number: { type: 'number' as const, min: 0, message: t('pages_platform_input_error') }
  };

  // 處理表單值變化
  const handleFormValuesChange = (changed: any, all: any) => {
    // 當 giftMoneyMode 改變時，清空相關字段
    if (changed.giftMoneyMode !== undefined) {
      const currentCondition = all.giftMoneyCondition || [];
      const resetConditionTemplate = {
        min: null,
        max: null,
        feedback: undefined,
        feedbackPercent: undefined,
        feedbackUpperLimit: undefined
      };

      const clearedCondition = currentCondition.map(() => ({ ...resetConditionTemplate }));
      form.setFieldsValue({
        giftMoneyCondition: clearedCondition
      });
    }

    // 當 giftMoneyCondition 改變時，執行自動更新邏輯
    if (changed.giftMoneyCondition) {
      // 找出有變動的 max 欄位並自動更新下一組的 min
      changed.giftMoneyCondition.forEach((item: any, idx: number) => {
        if (item && item.max !== undefined) {
          const newArr = [...all.giftMoneyCondition];
          if (newArr[idx + 1]) {
            newArr[idx + 1].min = item.max;
            form.setFieldsValue({ giftMoneyCondition: newArr });
          }
        }
      });
    }
  };

  const handleSubmit = () => {
    form
      .validateFields()
      .then((values) => {
        const giftMoneyConditionArr = Array.isArray(values.giftMoneyCondition)
          ? values.giftMoneyCondition
          : [values.giftMoneyCondition];

        const formData = {
          ...values,
          isReview: (values.isReview ? 1 : 0) as 0 | 1,
          needApply: (values.needApply ? 1 : 0) as 0 | 1,
          openCta: (values.openCta ? 1 : 0) as 0 | 1,
          giftMoneyCondition: giftMoneyConditionArr
            .filter((item: any) => item != null) // 過濾掉 null 和 undefined
            .map((item: any, index: number) => ({
              min: item?.min ?? null,
              max:
                giftMoneyConditionArr.length > 1 && index === giftMoneyConditionArr.length - 1
                  ? null
                  : (item?.max ?? null),
              ...(values.giftMoneyMode === 1 && {
                feedback_percent: item?.feedbackPercent,
                feedback_upper_limit: item?.feedbackUpperLimit
              }),
              ...(values.giftMoneyMode === 2 && {
                feedback: item?.feedback
              })
            }))
        };

        if (isEditMode && activityId) {
          // 編輯模式
          updateActivityMutate({
            id: activityId,
            ...formData,
            joinLevel: formData.joinLevel || 0, // 提供預設值
            joinMode: formData.joinMode || 0 // 提供預設值
          });
        } else {
          // 新增模式
          createActivityMutate(formData);
        }
      })
      .catch((errorInfo) => {
        console.log('Validation failed:', errorInfo);
        console.log('Form validation errors:', errorInfo.errorFields);

        // 當驗證失敗時，自動切換至有驗證錯誤的欄位所在的 tab
        if (setActiveTab && errorInfo.errorFields && errorInfo.errorFields.length > 0) {
          // 獲取第一個錯誤字段的名稱
          const firstErrorField = errorInfo.errorFields[0];
          const fieldNameArray = firstErrorField.name;

          // 處理嵌套字段名稱，如 ['giftMoneyCondition', 0, 'min']
          let fieldName = '';
          if (Array.isArray(fieldNameArray)) {
            if (fieldNameArray.length === 1) {
              fieldName = fieldNameArray[0];
            } else if (fieldNameArray.length > 1) {
              // 對於嵌套字段，取第一個元素作為主要字段名
              fieldName = fieldNameArray[0];
            }
          } else {
            fieldName = fieldNameArray;
          }

          // 判斷應該切換到哪個 tab
          const targetTab = getActivityTabForField(fieldName);
          setActiveTab(targetTab);
        }
      });
  };

  return {
    form,
    initialValues,
    validationRules,
    handleFormValuesChange,
    handleSubmit,
    isCreateActivityPending: isCreateActivityPending || isUpdateActivityPending,
    isEditMode,
    isActivityDetailLoading,
    activityDetail
  };
};
