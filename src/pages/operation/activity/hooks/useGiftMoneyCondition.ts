import RForm from '@/components/RForm';

interface UseGiftMoneyConditionProps {
  form: any;
}

export const useGiftMoneyCondition = ({ form }: UseGiftMoneyConditionProps) => {
  const { t } = useTranslation();
  const [selectedGifts, setSelectedGifts] = useState<string[]>(['giftMoney']);
  const [giftMoneyConditionType, setGiftMoneyConditionType] = useState<'single' | 'multiple'>(
    'single'
  );
  const [groupCount, setGroupCount] = useState<number>(3);
  const [hasUserInteraction, setHasUserInteraction] = useState(false);

  const giftMoneyMode = RForm.useWatch('giftMoneyMode', form);
  const giftMoneyCondition = RForm.useWatch('giftMoneyCondition', form);

  // 初始化邏輯：根據form中的giftMoneyCondition設置條件類型和組數
  useEffect(() => {
    // 如果用戶已經交互過，則不執行
    if (hasUserInteraction) {
      return;
    }

    // 從form中獲取當前的giftMoneyCondition值
    const currentCondition = form.getFieldValue('giftMoneyCondition');

    if (!currentCondition) {
      return;
    }

    // 根據條件數量設置類型
    const conditionCount = currentCondition.length;

    if (conditionCount === 1) {
      setGiftMoneyConditionType('single');
      setGroupCount(3);
    } else if (conditionCount > 1) {
      setGiftMoneyConditionType('multiple');
      setGroupCount(conditionCount);
    }
  }, [form, hasUserInteraction, giftMoneyCondition]);

  // 當 joinMode 改變時重置 tags 值
  const handleJoinModeChange = () => {
    form.setFieldsValue({ tags: undefined });
  };

  const resetConditionTemplate = {
    min: null,
    max: null,
    feedback: undefined,
    feedbackPercent: undefined,
    feedbackUpperLimit: undefined
  };

  // 當 giftMoneyMode 改變時清空所有相關字段
  const handleGiftMoneyModeChange = () => {
    // 設置用戶交互標誌
    setHasUserInteraction(true);

    // 立即執行清空操作
    const currentCondition = form.getFieldValue('giftMoneyCondition') || [];
    const clearedCondition = currentCondition.map(() => ({ ...resetConditionTemplate }));

    form.setFieldsValue({
      giftMoneyCondition: clearedCondition
    });

    setTimeout(() => {
      const actualValue = form.getFieldValue('giftMoneyCondition');
      if (
        actualValue &&
        actualValue.some(
          (item: any) =>
            item.min !== undefined ||
            item.max !== undefined ||
            item.feedback !== undefined ||
            item.feedbackPercent !== undefined ||
            item.feedbackUpperLimit !== undefined
        )
      ) {
        form.setFieldsValue({
          giftMoneyCondition: clearedCondition
        });
      }
    }, 50);
  };

  // 當條件類型改變時清空條件
  const handleConditionTypeChange = (type: 'single' | 'multiple') => {
    // 首先設置用戶交互標誌
    setHasUserInteraction(true);

    // 立即執行清空操作，不等待狀態更新
    if (type === 'single') {
      const newCondition = [{ ...resetConditionTemplate }];

      form.setFieldsValue({
        giftMoneyCondition: newCondition
      });

      // 驗證設置是否成功
      setTimeout(() => {
        const actualValue = form.getFieldValue('giftMoneyCondition');

        // 如果不一致，嘗試強制更新
        if (JSON.stringify(newCondition) !== JSON.stringify(actualValue)) {
          form.setFieldsValue({
            giftMoneyCondition: newCondition
          });
        }
      }, 100);
    } else {
      // 多組模式，根據當前組數清空
      const conditions = Array.from({ length: groupCount }, () => ({ ...resetConditionTemplate }));

      form.setFieldsValue({ giftMoneyCondition: conditions });

      // 驗證設置是否成功
      setTimeout(() => {
        const actualValue = form.getFieldValue('giftMoneyCondition');

        // 如果不一致，嘗試強制更新
        if (JSON.stringify(conditions) !== JSON.stringify(actualValue)) {
          form.setFieldsValue({
            giftMoneyCondition: conditions
          });
        }
      }, 100);
    }

    // 設置條件類型，useEffect 會監聽變化但不會重複執行清空
    setGiftMoneyConditionType(type);
  };

  // 當組數改變時重新生成條件
  const handleGroupCountChange = (count: number) => {
    setHasUserInteraction(true);

    setGroupCount(count);
    if (giftMoneyConditionType === 'multiple') {
      const conditions = Array.from({ length: count }, () => ({ ...resetConditionTemplate }));
      form.setFieldsValue({ giftMoneyCondition: conditions });
    }
  };

  // 當選擇的贈送項目改變時重置相關字段
  const handleSelectedGiftsChange = (values: string[]) => {
    if (values.length === 0) {
      return;
    }
    setSelectedGifts(values);
    if (!values.includes('prop')) {
      form.setFieldsValue({ prop: undefined });
    }
    if (!values.includes('giftMoney')) {
      form.setFieldsValue({ giftMoneyCondition: undefined });
    }
  };

  const generateValidationRules = (index: number, field: string) => {
    const baseRules = [
      ({ getFieldValue }: any) => ({
        validator(_: any, value: any) {
          const arr = getFieldValue('giftMoneyCondition') || [];
          const isLast = index === arr.length - 1;
          const trigger = isLast
            ? getFieldValue(['giftMoneyCondition', index, 'min'])
            : getFieldValue(['giftMoneyCondition', index, 'max']);

          if (trigger === undefined || trigger === null || trigger === '') {
            return Promise.resolve();
          }
          if (value === undefined || value === null || value === '') {
            return Promise.reject(new Error(t('pages_platform_input_error')));
          }
          return Promise.resolve();
        }
      })
    ];

    // 針對 max 欄位的特殊驗證
    if (field === 'max') {
      return [
        ({ getFieldValue }: any) => ({
          validator(_: any, value: any) {
            if (index === 0) return Promise.resolve();
            const prevMax = getFieldValue(['giftMoneyCondition', index - 1, 'max']);
            if (value === undefined || value === null) return Promise.resolve();
            if (value > prevMax) return Promise.resolve();
            return Promise.reject(new Error(t('pages_activity_max_must_gt_prev')));
          }
        })
      ];
    }

    return baseRules;
  };

  return {
    selectedGifts,
    giftMoneyConditionType,
    groupCount,
    giftMoneyMode,
    handleJoinModeChange,
    handleGiftMoneyModeChange,
    handleConditionTypeChange,
    handleGroupCountChange,
    handleSelectedGiftsChange,
    generateValidationRules
  };
};
