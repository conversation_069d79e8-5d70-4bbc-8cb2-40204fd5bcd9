import { useQuery } from '@tanstack/react-query';

import {
  getActivityStatus,
  getActivityTemplate,
  getCounductTimeType,
  getPublishTimeType
} from '@/api/activity';

import { ActivityFilter } from '../enum';

export const useActivityTemplate = () => {
  return useQuery({
    queryKey: ['activityTemplate'],
    queryFn: getActivityTemplate,
    select: (data: any) => data.data || []
  });
};

export const useActivityStatus = (filter: ActivityFilter) => {
  return useQuery({
    queryKey: ['activityStatus', filter],
    queryFn: () => getActivityStatus({ filter }),
    select: (data: any) => data.data || []
  });
};

export const usePublishTimeType = () => {
  return useQuery({
    queryKey: ['publishTimeType'],
    queryFn: getPublishTimeType,
    select: (data: any) => data.data || []
  });
};

export const useCounductTimeType = () => {
  return useQuery({
    queryKey: ['conductTimeType'],
    queryFn: getCounductTimeType,
    select: (data: any) => data.data || []
  });
};
