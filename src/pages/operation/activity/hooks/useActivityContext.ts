import { createContext, useContext } from 'react';

// 創建一個 Context 來共享選項數據
type ActivityOptionsContextType = {
  templateOptions: { label: string; value: number }[];
  statusOptions: { label: string; value: number }[];
};

export const ActivityOptionsContext = createContext<ActivityOptionsContextType>({
  templateOptions: [],
  statusOptions: []
});

export const ActivityTemplateContext = createContext<{ label: string; value: number }[]>([]);
export const ActivityStatusContext = createContext<{ label: string; value: number }[]>([]);

// 導出 Context 供子組件使用
export const useActivityOptions = () => useContext(ActivityOptionsContext);
export const useActivityTemplateOptions = () => useContext(ActivityTemplateContext);
export const useActivityStatusOptions = () => useContext(ActivityStatusContext);
