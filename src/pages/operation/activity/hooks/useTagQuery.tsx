import { queryOptions, useQuery } from '@tanstack/react-query';

import { getPlayerTagListOption } from '@/api/player';
const TAG_QUERY_KEY = ['tagList'];

const tagQueryOptions = () => {
  return queryOptions({
    queryKey: TAG_QUERY_KEY,
    queryFn: () => getPlayerTagListOption(),
    select: (data) => data.data
  });
};

export const useTagQuery = () => {
  return useQuery(tagQueryOptions());
};
