import { useQuery } from '@tanstack/react-query';

import { getAcitveActivity, getDisActiveActivity } from '@/api/activity';

export interface ActivitySearchParams {
  // 活動列表搜尋參數類型
  templateType?: number;
  needApply?: 0 | 1;
  isReview?: 0 | 1;
  status?: number;
  // 已下架活動分頁參數
  page?: number;
  limit?: number;
}
// Query keys
export const activityKeys = {
  listWithParams: (type: 'active' | 'inactive', params: ActivitySearchParams) =>
    ['activity', 'list', type, params] as const
};

interface UseActivityQueryOptions {
  enabled?: boolean;
}

// 分別的 hooks 以處理不同的型別
export const useActiveActivityQuery = (
  searchParams: ActivitySearchParams = {},
  options: UseActivityQueryOptions = {}
) => {
  const { enabled = true } = options;

  return useQuery({
    queryKey: activityKeys.listWithParams('active', searchParams),
    queryFn: () => getAcitveActivity(searchParams),
    enabled,
    select: (data: any) => data.data || []
  });
};

export const useInactiveActivityQuery = (
  page: number = 1,
  limit: number = 10,
  options: UseActivityQueryOptions = {}
) => {
  const { enabled = true } = options;

  return useQuery({
    queryKey: activityKeys.listWithParams('inactive', { page, limit }),
    queryFn: () => getDisActiveActivity({ page, limit }),
    enabled,
    select: (data: any) => data.data || []
  });
};
