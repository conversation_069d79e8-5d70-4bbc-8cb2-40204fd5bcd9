const WEEK_MAP = {
  0: 'pages_activity_sundays',
  1: 'pages_activity_mondays',
  2: 'pages_activity_tuesdays',
  3: 'pages_activity_wednesdays',
  4: 'pages_activity_thursdays',
  5: 'pages_activity_fridays',
  6: 'pages_activity_saturdays'
};

export const formatConductWeek = (
  conductWeek: string | any,
  t: (key: string) => string
): string | null => {
  if (!conductWeek) return null;

  try {
    // 解析 JSON 字符串
    const weekData = typeof conductWeek === 'string' ? JSON.parse(conductWeek) : conductWeek;

    const weekInfo = Array.isArray(weekData) ? weekData[0] : weekData;

    if (weekInfo && weekInfo.weekStart !== undefined && weekInfo.weekEnd !== undefined) {
      const weekStartText = t(WEEK_MAP[weekInfo.weekStart as keyof typeof WEEK_MAP] || '');
      const weekEndText = t(WEEK_MAP[weekInfo.weekEnd as keyof typeof WEEK_MAP] || '');
      const timeStart = weekInfo.timeStart || '';
      const timeEnd = weekInfo.timeEnd || '';

      return `${weekStartText} ${timeStart} ~ ${weekEndText} ${timeEnd}`;
    }
  } catch (error) {
    console.error('Failed to parse conductWeek JSON:', error);
  }

  return null;
};
