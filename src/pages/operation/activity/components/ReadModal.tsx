import { useTranslation } from 'react-i18next';

import mockup from '@/assets/img/mockup.webp';
import RModal from '@/components/RModal';
import { Activity } from '@/types/activity';
import { formatTimeToMinute } from '@/utils/time';

// 判斷文件類型的函數
const getFileType = (url: string): 'image' | 'video' | 'unknown' => {
  if (!url) return 'unknown';

  const extension = url.split('.').pop()?.toLowerCase();

  // 圖片格式
  const imageExtensions = ['jpg', 'png', 'apng', 'webp'];
  // 影片格式
  const videoExtensions = ['mp4'];

  if (imageExtensions.includes(extension || '')) {
    return 'image';
  } else if (videoExtensions.includes(extension || '')) {
    return 'video';
  }

  return 'unknown';
};

// 渲染媒體內容的組件
const MediaContent = ({ src, alt }: { src: string; alt: string }) => {
  const fileType = getFileType(src);

  if (fileType === 'image') {
    return <img src={src} alt={alt} className="w-full h-full object-cover rounded-lg" />;
  } else if (fileType === 'video') {
    return (
      <video src={src} className="w-full object-contain rounded-lg" controls preload="metadata">
        <source src={src} type={`video/${src.split('.').pop()}`} />
        Your browser does not support the video tag.
      </video>
    );
  }

  // 如果無法判斷類型，預設顯示為圖片
  return <img src={src} alt={alt} className="w-full object-contain rounded-lg" />;
};

interface ReadModalProps {
  open: boolean;
  onClose: () => void;
  activity: Activity | null;
}

const ReadModal = ({ open, onClose, activity }: ReadModalProps) => {
  const { t } = useTranslation();

  return (
    <RModal
      open={open}
      title={`${t('common_read')}${t('pages_activity')}`}
      onCancel={onClose}
      width={780}
      okButtonProps={{ show: false }}
      cancelButtonProps={{ text: t('common_close'), show: true }}
    >
      <p className="text-warning text-center">{t('pages_activity_read_title')}</p>
      <div className="w-full h-[680px] relative">
        <img src={mockup} alt="mockup" className="w-full h-full object-contain" />
        <div className="absolute top-20 left-52 w-[320px] h-full">
          <div className="text-sm text-gray-500 mx-auto w-full h-[180px]">
            {activity?.contentMapping?.zhTw?.photo && (
              <MediaContent src={activity.contentMapping.zhTw.photo} alt="activity media" />
            )}
          </div>
          <div className="text-sm font-bold mt-1.5 mb-1">
            {activity?.contentMapping?.zhTw?.title}
          </div>
          <div className="text-[#4B5EFF]">
            {(() => {
              const hasStartTime = activity?.conductStartTime;
              const hasEndTime = activity?.conductEndTime;

              if (hasStartTime && hasEndTime) {
                return `${formatTimeToMinute(activity.conductStartTime)} ~ ${formatTimeToMinute(activity.conductEndTime)}`;
              } else if (hasStartTime && !hasEndTime) {
                return `${formatTimeToMinute(activity.conductStartTime)} ${t('common_started')}`;
              } else {
                return 'XXXX-XX-XX 00:00';
              }
            })()}
          </div>
          <div
            className="mt-3.5 h-[220px] overflow-y-auto"
            dangerouslySetInnerHTML={{ __html: activity?.contentMapping?.zhTw?.content || '' }}
          />
        </div>
      </div>
    </RModal>
  );
};

export default ReadModal;
