import { useTranslation } from 'react-i18next';

import RCheckbox from '@/components/RCheckbox';
import RForm from '@/components/RForm';
import RInputNumber from '@/components/RInputNumber';
import RRadio from '@/components/RRadio';
import RSelect from '@/components/RSelect';
import { useVipQuery } from '@/pages/transaction/giftSetting/hooks/useVipQuery';
import { PlayerTag } from '@/types/playerlist';
import { Vip } from '@/types/vip';

import { useGiftMoneyCondition } from '../hooks/useGiftMoneyCondition';
import { useTagQuery } from '../hooks/useTagQuery';
import { GiftMoneyCondition } from './GiftMoneyCondition';

interface ConditionTabProps {
  form: any;
  validationRules: {
    required: { required: boolean; message: string };
  };
}

export const ConditionTab = ({ form, validationRules }: ConditionTabProps) => {
  const { t } = useTranslation();
  const { data: vipList, isLoading: isVipListLoading } = useVipQuery();
  const { data: tagList, isLoading: isTagListLoading } = useTagQuery();
  const isLoading = isVipListLoading || isTagListLoading;

  const {
    selectedGifts,
    giftMoneyConditionType,
    groupCount,
    giftMoneyMode,
    handleJoinModeChange,
    handleGiftMoneyModeChange,
    handleConditionTypeChange,
    handleGroupCountChange,
    handleSelectedGiftsChange,
    generateValidationRules
  } = useGiftMoneyCondition({
    form
  });

  // 監聽表單值變化
  const joinMode = RForm.useWatch('joinMode', form);

  // 調試信息

  const getVipOptions = (vipList: Vip[]) => {
    return vipList
      .filter((item) => item.status === 1)
      .map((item) => ({
        label: item.name,
        value: item.id
      }));
  };

  const vipOptions = getVipOptions(vipList || []);
  const tagOptions = tagList?.map((tag: PlayerTag) => ({ label: tag.name, value: tag.id })) || [];

  return (
    <div className="p-4 border-x border-b border-gray-200">
      <div className="flex">
        <div className="w-1/2 pr-4">
          <div className="mb-2">{t('pages_activity_gift')}</div>
          <RCheckbox.Group
            value={selectedGifts}
            onChange={handleSelectedGiftsChange}
            options={[
              { label: t('pages_activity_giftmoney'), value: 'giftMoney' },
              { label: t('pages_activity_prop'), value: 'prop' }
            ]}
          />

          {selectedGifts.includes('giftMoney') && (
            <GiftMoneyCondition
              giftMoneyMode={giftMoneyMode}
              giftMoneyConditionType={giftMoneyConditionType}
              groupCount={groupCount}
              onGiftMoneyModeChange={handleGiftMoneyModeChange}
              onConditionTypeChange={handleConditionTypeChange}
              onGroupCountChange={handleGroupCountChange}
              generateValidationRules={generateValidationRules}
            />
          )}

          {selectedGifts.includes('prop') && (
            <div className="p-3 bg-bg-primary mt-4">
              <p className="my-2">{t('pages_activity_prop')}</p>
              <RForm.Item name="prop" label={t('pages_activity_prop')}>
                <RSelect options={[]} placeholder={t('placeholder_select')} className="!w-1/2" />
              </RForm.Item>
            </div>
          )}
        </div>

        <div className='w-1/2 pl-4 relative before:content-[""] before:absolute before:left-0 before:top-0 before:w-px before:h-full before:bg-gray-200'>
          <RForm.Item
            label={t('pages_activity_isReview')}
            name="isReview"
            className="vertical-form-item"
            required
            rules={[validationRules.required]}
          >
            <RRadio.Group
              options={[
                { label: t('common_no'), value: 0 },
                { label: t('common_yes'), value: 1 }
              ]}
            />
          </RForm.Item>

          <RForm.Item
            label={t('pages_activity_joinLevel')}
            name="joinLevel"
            className="vertical-form-item"
            required
            rules={[validationRules.required]}
          >
            <RSelect loading={isLoading} options={vipOptions} className="!w-1/2" />
          </RForm.Item>

          <RForm.Item
            label={t('pages_activity_frozenTime')}
            name="frozenTime"
            className="vertical-form-item"
            required
            rules={[validationRules.required]}
          >
            <RInputNumber
              min={0}
              precision={0}
              className="!w-1/2"
              placeholder={t('placeholder_input')}
            />
          </RForm.Item>

          <RForm.Item
            label={t('pages_activity_withdrawRewardTime')}
            name="withdrawRewardTime"
            className="vertical-form-item"
            required
            rules={[validationRules.required]}
          >
            <RInputNumber
              min={0}
              precision={0}
              className="!w-1/2"
              placeholder={t('placeholder_input')}
            />
          </RForm.Item>

          <RForm.Item
            label={t('pages_activity_joinMode')}
            name="joinMode"
            className="vertical-form-item"
          >
            <RRadio.Group
              onChange={handleJoinModeChange}
              options={[
                { label: t('game_limit_none'), value: 0 },
                { label: t('pages_activity_availableTags'), value: 1 },
                { label: t('pages_activity_unavailableTags'), value: 2 }
              ]}
            />
          </RForm.Item>

          {(joinMode === 1 || joinMode === 2) && (
            <RForm.Item
              name="tags"
              className="vertical-form-item"
              rules={[validationRules.required]}
            >
              <RSelect
                options={tagOptions}
                mode="multiple"
                placeholder={t('placeholder_select')}
                className="!w-1/2"
              />
            </RForm.Item>
          )}
        </div>
      </div>
    </div>
  );
};
