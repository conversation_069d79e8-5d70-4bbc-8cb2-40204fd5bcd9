import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';

import { releaseActivity } from '@/api/activity';
import InfoIcon from '@/assets/img/icon/info.svg?react';
import InformationIcon from '@/assets/img/icon/information.svg?react';
import FormModal from '@/components/FormModal';
import FormTimePeriodSelect from '@/components/FormTimePeriodSelect';
import RForm from '@/components/RForm';
import RTooltip from '@/components/Rtooltip';
import WeeklyTimeSelect from '@/components/WeeklyTimeSelect';
import { Activity } from '@/types/activity';
import { PublishTypeEnum } from '@/types/common';

import { useCounductTimeType, usePublishTimeType } from '../hooks/useActivityOptions';
import { activityKeys } from '../hooks/useActivityQuery';

interface PublishModalProps {
  open: boolean;
  onClose: () => void;
  activity: Activity | null;
}

type FormValue = {
  shelfType: PublishTypeEnum;
  shelfStartTime?: number | null;
  shelfEndTime?: number | null;
  shelfTimeRange?: [number, number];
  conductType: number;
  conductStartTime?: number | null;
  conductEndTime?: number | null;
  conductTimeRange?: [number, number];
  weekStart?: number;
  weekEnd?: number;
  timeStart?: string;
  timeEnd?: string;
};

// API 數據格式類型
type APIPublishData = {
  id: number;
  shelfType: number;
  shelfStartTime: number | null;
  shelfEndTime: number | null;
  conductType: number;
  conductStartTime: number | null;
  conductEndTime: number | null;
  conductWeek?: {
    weekStart: number;
    weekEnd: number;
    timeStart: string;
    timeEnd: string;
  };
};

const initialValues: FormValue = {
  shelfType: 1,
  shelfStartTime: null,
  shelfEndTime: null,
  conductType: 1,
  conductStartTime: null,
  conductEndTime: null
};

const PublishModal = ({ open, onClose, activity }: PublishModalProps) => {
  const { t } = useTranslation();
  const [form] = RForm.useForm<FormValue>();
  const { data: publishTimeType } = usePublishTimeType();
  const { data: conductTimeType } = useCounductTimeType();
  const queryClient = useQueryClient();

  const conductType = RForm.useWatch('conductType', form);
  const shelfType = RForm.useWatch('shelfType', form);

  // 當 shelfType 改變時，檢查並重置 conductType
  useEffect(() => {
    if (shelfType && conductTimeType) {
      const currentConductType = form.getFieldValue('conductType');

      let allowedConductTypes: number[] = [];
      if (shelfType === 1 || shelfType === 2) {
        allowedConductTypes = [1, 3, 4];
      } else {
        allowedConductTypes = conductTimeType.map((item: { id: number }) => item.id);
      }

      // 如果當前選擇的 conductType 不在允許的選項中，則重置
      if (currentConductType && !allowedConductTypes.includes(currentConductType)) {
        form.setFieldsValue({ conductType: undefined });
      }
    }
  }, [shelfType, conductTimeType, form]);

  const onSuccess = () => {
    queryClient.invalidateQueries({ queryKey: activityKeys.listWithParams('active', {}) });
    onClose();
  };

  const { mutate: publishActivityMutate } = useMutation({
    mutationFn: releaseActivity,
    onSuccess: onSuccess
  });

  // 格式化數據為 API 要求格式
  const formatDataForAPI = (values: FormValue): APIPublishData => {
    if (!activity) {
      throw new Error('Activity is required');
    }

    const formattedData: APIPublishData = {
      id: activity.id,
      shelfType: values.shelfType,
      shelfStartTime: values.shelfStartTime ?? null,
      shelfEndTime: values.shelfEndTime ?? null,
      conductType: values.conductType,
      conductStartTime: values.conductStartTime ?? null,
      conductEndTime: values.conductEndTime ?? null
    };

    // 每週重複類型需要特殊處理
    if (values.conductType === PublishTypeEnum.WEEKLY) {
      if (
        values.weekStart !== undefined &&
        values.weekEnd !== undefined &&
        values.timeStart &&
        values.timeEnd
      ) {
        formattedData.conductWeek = {
          weekStart: values.weekStart,
          weekEnd: values.weekEnd,
          timeStart: values.timeStart,
          timeEnd: values.timeEnd
        };
      }
    }

    return formattedData;
  };
  const onSubmit = () => {
    form
      .validateFields()
      .then((values) => {
        console.log('Original form values:', values);

        const formattedData = formatDataForAPI(values);

        publishActivityMutate(formattedData);
      })
      .catch((errorInfo) => {
        console.log('Validation failed:', errorInfo);
      });
  };

  const handleClose = () => {
    form.resetFields();
    onClose();
  };

  const publishTypeOptions = publishTimeType?.map((item: { id: number; label: string }) => ({
    label: item.label,
    value: item.id
  }));

  const filteredConductTypeOptions = useMemo(() => {
    if (!conductTimeType) return [];

    if (shelfType === 1 || shelfType === 2) {
      // 當 shelfType 是 立即 或 隨上架時間 時，排除活動實際執行時間 "指定開始時間，隨上架結束時間結束"
      return conductTimeType
        .filter(
          (item: { id: number; label: string }) => item.id === 1 || item.id === 3 || item.id === 4
        )
        .map((item: { id: number; label: string }) => ({
          label: item.label,
          value: item.id
        }));
    }

    return conductTimeType?.map((item: { id: number; label: string }) => ({
      label: item.label,
      value: item.id
    }));
  }, [conductTimeType, shelfType]);

  return (
    <FormModal
      open={open}
      onClose={handleClose}
      title={t('common_publish')}
      form={form}
      onSubmit={onSubmit}
      initialValues={initialValues}
    >
      <div className="text-text mb-4 bg-[#FF83171A] border border-[#FF8317] rounded py-1.5 px-2.5 w-fit">
        <div className="flex gap-1">
          <InfoIcon className="w-4.5 h-4.5 fill-[#FF8317]" />
          <p>{t('pages_activity_publish_description')}</p>
        </div>
      </div>

      {/* 發布時間選擇 */}
      <div className="flex items-center gap-2 mb-1">
        <div className="h-4.5">{t('pages_activity_publish_time')}</div>
        <RTooltip title={t('pages_activity_publish_time_tooltip')} color="gray">
          <InformationIcon className="w-4.5 h-4.5 fill-info" />
        </RTooltip>
      </div>
      <FormTimePeriodSelect
        form={form}
        fieldPrefix="shelf"
        showPermanent={false}
        showWeekly={false}
        customOptions={publishTypeOptions}
        classNames="w-1/2"
      />

      {/* 活動進行時間選擇 */}
      <div className="flex items-center gap-2 mb-1 mt-4">
        <div className="h-4.5">{t('pages_activity_conduct_time')}</div>
        <RTooltip title={t('pages_activity_conduct_time_tooltip')} color="gray">
          <InformationIcon className="w-4.5 h-4.5 fill-info" />
        </RTooltip>
      </div>
      <FormTimePeriodSelect
        form={form}
        fieldPrefix="conduct"
        showPermanent={false}
        showWeekly={true}
        customOptions={filteredConductTypeOptions}
        classNames="w-1/2"
      />

      {/* 每週重複時間選擇 */}
      {conductType === PublishTypeEnum.WEEKLY && <WeeklyTimeSelect form={form} />}
    </FormModal>
  );
};

export default PublishModal;
