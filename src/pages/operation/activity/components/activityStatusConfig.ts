type ActivityStatusConfig = {
  label: string;
  color: string;
  textColor: string;
};

export enum ActivityStatus {
  DRAFT = 1, //草稿
  UPCOMING = 2, //即將開始
  IN_PROGRESS = 3, //進行中
  WITHDRAW_EXPIRED = 4, //已過期下架
  WITHDRAW_MANUAL = 5, //手動下架
  SCHEDULED = 6, //已排定
  ENDED = 7 //已結束
}

export const ACTIVITY_STATUS_COLORS: Record<number, Omit<ActivityStatusConfig, 'label'>> = {
  [ActivityStatus.DRAFT]: {
    color: 'var(--tag-bg-else)',
    textColor: 'var(--color-text-secondary)'
  },
  [ActivityStatus.UPCOMING]: {
    color: '#FF831729',
    textColor: '#FF8317'
  },
  [ActivityStatus.IN_PROGRESS]: {
    color: 'var(--tag-bg-enabled)',
    textColor: 'var(--color-success)'
  },
  //   [ActivityStatus.WITHDRAW_EXPIRED]: {
  //     color: 'var(--tag-bg-disabled)',
  //     textColor: 'var(--color-warning)'
  //   },
  //   [ActivityStatus.WITHDRAW_MANUAL]: {
  //     color: 'var(--tag-bg-disabled)',
  //     textColor: 'var(--color-warning)'
  //   },
  [ActivityStatus.SCHEDULED]: {
    color: 'var(--tag-bg-enabled)',
    textColor: 'var(--color-success)'
  },
  [ActivityStatus.ENDED]: {
    color: 'var(--tag-bg-disabled)',
    textColor: 'var(--color-warning)'
  }
};

// 動態生成 ACTIVITY_STATUS_MAP 的函數
// 這個函數會根據後端回傳的狀態數據來生成完整的配置
export const generateActivityStatusMap = (statusData: { id: number; label: string }[]) => {
  const statusMap: Record<number, ActivityStatusConfig> = {};

  statusData.forEach((status) => {
    const colorConfig = ACTIVITY_STATUS_COLORS[status.id];
    if (colorConfig) {
      statusMap[status.id] = {
        label: status.label,
        ...colorConfig
      };
    } else {
      // 如果沒有預設的顏色配置，使用預設值
      statusMap[status.id] = {
        label: status.label,
        color: 'var(--tag-bg-else)',
        textColor: 'var(--color-text-secondary)'
      };
    }
  });

  return statusMap;
};

// 預設的 ACTIVITY_STATUS_MAP（用於向後兼容）
export const ACTIVITY_STATUS_MAP: Record<number, ActivityStatusConfig> = {};
