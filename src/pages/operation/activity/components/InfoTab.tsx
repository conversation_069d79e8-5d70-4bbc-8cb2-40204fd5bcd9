import { message, Upload } from 'antd';
import type { RcFile } from 'antd/es/upload/interface';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import InformationIcon from '@/assets/img/icon/information.svg?react';
import HTMLEditor from '@/components/HTMLEditor';
import RCheckbox from '@/components/RCheckbox';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RTooltip from '@/components/Rtooltip';
import RUploader from '@/components/RUploader';

interface InfoTabProps {
  validationRules: {
    required: { required: boolean; message: string };
  };
  form: any;
  isEditMode?: boolean;
  activityDetail?: any;
}

export const InfoTab = ({
  validationRules,
  form,
  isEditMode = false,
  activityDetail
}: InfoTabProps) => {
  const { t } = useTranslation();
  const [localNeedApply, setLocalNeedApply] = useState(false);
  const [localOpenCta, setLocalOpenCta] = useState(false);
  const [photoPreview, setPhotoPreview] = useState<string>('');

  const needApply = RForm.useWatch('needApply', form);
  const openCta = RForm.useWatch('openCta', form);
  const zhTwPhoto = RForm.useWatch('zhTwPhoto', form);

  // 檔案上傳前的驗證
  const beforeUpload = (file: RcFile) => {
    const isImage = file.type.startsWith('image/');
    const isMp4 = file.type === 'video/mp4';

    if (!isImage && !isMp4) {
      message.error(t('components_uploader_invalid_file_type'));
      return Upload.LIST_IGNORE;
    }

    // 檢查檔案大小
    if (isImage) {
      // 檢查圖片格式
      const allowedImageTypes = [
        'image/png',
        'image/jpeg',
        'image/jpg',
        'image/webp',
        'image/apng'
      ];
      if (!allowedImageTypes.includes(file.type)) {
        message.error(t('components_uploader_invalid_image_type'));
        return Upload.LIST_IGNORE;
      }

      // 圖片檔案限制為 500KB
      const isLt500KB = file.size / 1024 < 500;
      if (!isLt500KB) {
        message.error(t('components_uploader_image_too_large', { size: 500 }));
        return Upload.LIST_IGNORE;
      }

      // 檢查圖片尺寸
      // return new Promise((resolve, reject) => {
      //   const img = new Image();
      //   img.onload = () => {
      //     const { width, height } = img;
      //     if (width !== 558 || height !== 320) {
      //       message.error(t('components_uploader_invalid_image_size', { width: 558, height: 320 }));
      //       reject(Upload.LIST_IGNORE);
      //     } else {
      //       resolve(true);
      //     }
      //   };
      //   img.onerror = () => {
      //     message.error(t('components_uploader_image_load_error'));
      //     reject(Upload.LIST_IGNORE);
      //   };
      //   img.src = URL.createObjectURL(file);
      // });
    } else if (isMp4) {
      // 影片檔案限制為 5MB
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        message.error(t('components_audioUploader_limit', { size: 5 }));
        return Upload.LIST_IGNORE;
      }
    }

    return true;
  };

  // 同步本地狀態和表單狀態
  useEffect(() => {
    if (needApply !== undefined) {
      setLocalNeedApply(!!needApply);
    }
    if (openCta !== undefined) {
      setLocalOpenCta(!!openCta);
    }
  }, [needApply, openCta]);

  // 處理 photo
  useEffect(() => {
    if (zhTwPhoto) {
      // 如果是 File 對象，創建預覽 URL
      if (zhTwPhoto instanceof File) {
        const url = URL.createObjectURL(zhTwPhoto);
        setPhotoPreview(url);
        return () => URL.revokeObjectURL(url);
      }
      // 字符串 URL
      setPhotoPreview(zhTwPhoto);
    } else if (isEditMode && activityDetail) {
      const activity = (activityDetail as any).data || activityDetail;
      const photoUrl = activity.contentMapping?.zhTw?.photo;
      if (photoUrl) {
        setPhotoPreview(photoUrl);
      }
    } else {
      setPhotoPreview('');
    }
  }, [zhTwPhoto, isEditMode, activityDetail]);

  // 檢查是否為影片檔案
  const isVideoFile = (url: string) => {
    // 如果是 blob URL，需要檢查原始檔案類型
    if (url.startsWith('blob:')) {
      if (zhTwPhoto instanceof File) {
        return zhTwPhoto.type === 'video/mp4';
      }
    }
    return url.toLowerCase().includes('.mp4');
  };

  // 渲染預覽內容
  const renderPreview = () => {
    if (!photoPreview) return null;

    if (isVideoFile(photoPreview)) {
      return (
        <div className="w-1/4 h-1/4">
          <video
            src={photoPreview}
            controls
            className="w-full h-full object-cover"
            preload="metadata"
          >
            Your browser does not support the video tag.
          </video>
        </div>
      );
    } else {
      return (
        <div className="w-1/4 h-1/4">
          <img src={photoPreview} alt="preview" className="w-full h-full object-cover" />
        </div>
      );
    }
  };

  const handleNeedApplyChange = (checked: boolean) => {
    setLocalNeedApply(checked);
    form.setFieldsValue({ needApply: checked });
  };

  const handleOpenCtaChange = (checked: boolean) => {
    setLocalOpenCta(checked);
    form.setFieldsValue({ openCta: checked });
  };

  return (
    <div className="p-4 border-x border-b border-gray-200">
      <RForm.Item
        label={t('pages_activity_name')}
        name="zhTwTitle"
        required
        className="vertical-form-item"
        rules={[validationRules.required]}
      >
        <RInput className="!w-1/4" />
      </RForm.Item>
      <div className="flex items-center gap-4 mt-2">
        <RForm.Item
          label={t('pages_activity_photo')}
          name="zhTwPhoto"
          required={!isEditMode} // 編輯模式下不是必填
          className="vertical-form-item"
          rules={isEditMode ? [] : [validationRules.required]} // 編輯模式下不需要驗證
        >
          <RUploader
            showPlaceholder={false}
            accept="image/png,image/jpeg,image/jpg,image/webp,image/apng,video/mp4"
            beforeUpload={beforeUpload}
          />
        </RForm.Item>
        {renderPreview()}
      </div>

      <p className="text-warning mt-1.5">{t('pages_activity_photo_hint')}</p>

      <RForm.Item
        label={t('pages_platform_content')}
        name="zhTwContent"
        required
        className="vertical-form-item"
        rules={[validationRules.required]}
      >
        <HTMLEditor />
      </RForm.Item>

      <div className="flex items-center gap-10">
        <RForm.Item name="needApply" valuePropName="checked">
          <div className="flex items-center gap-2">
            <RCheckbox
              checked={localNeedApply}
              onChange={(e) => handleNeedApplyChange(e.target.checked)}
            />
            <span className="text-sm">{t('pages_activity_isApply')}</span>
          </div>
        </RForm.Item>

        <RForm.Item name="openCta" valuePropName="checked">
          <div className="flex items-center gap-2">
            <RCheckbox
              checked={localOpenCta}
              onChange={(e) => handleOpenCtaChange(e.target.checked)}
            />
            <div className="flex items-center gap-1">
              <span className="text-sm">{t('pages_activity_CTA')}</span>
              <RTooltip title={t('pages_activity_CTA_tooltip')} color="gray">
                <InformationIcon className="w-4.5 h-4.5 fill-info" />
              </RTooltip>
            </div>
          </div>
        </RForm.Item>
      </div>
    </div>
  );
};
