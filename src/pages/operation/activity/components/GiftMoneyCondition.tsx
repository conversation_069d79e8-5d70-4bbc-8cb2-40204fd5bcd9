import InformationIcon from '@/assets/img/icon/information.svg?react';
import RForm from '@/components/RForm';
import RInputNumber from '@/components/RInputNumber';
import RRadio from '@/components/RRadio';
import RTooltip from '@/components/Rtooltip';

interface GiftMoneyConditionProps {
  giftMoneyMode: number;
  giftMoneyConditionType: 'single' | 'multiple';
  groupCount: number;
  onGiftMoneyModeChange: () => void;
  onConditionTypeChange: (type: 'single' | 'multiple') => void;
  onGroupCountChange: (count: number) => void;
  generateValidationRules: (index: number, field: string) => any[];
}

export const GiftMoneyCondition = ({
  giftMoneyMode,
  giftMoneyConditionType,
  groupCount,
  onGiftMoneyModeChange,
  onConditionTypeChange,
  onGroupCountChange,
  generateValidationRules
}: GiftMoneyConditionProps) => {
  const { t } = useTranslation();

  const renderSingleCondition = () => {
    if (giftMoneyMode === 1) {
      // 比例
      return (
        <>
          <div className="flex items-center gap-2 mb-2">
            <span>{t('pages_activity_percent_info')}</span>
            <RForm.Item name={['giftMoneyCondition', 0, 'feedbackPercent']} className="!mb-0">
              <RInputNumber
                min={0}
                max={100}
                className="!w-40"
                placeholder={t('placeholder_input')}
              />
            </RForm.Item>
            <span>%，{t('pages_activity_feedbackUpperLimit_info')}</span>
            <RForm.Item name={['giftMoneyCondition', 0, 'feedbackUpperLimit']} className="!mb-0">
              <RInputNumber min={0} className="!w-40" placeholder={t('placeholder_input')} />
            </RForm.Item>
          </div>
          <div className="flex items-center gap-2">
            <span>{t('pages_activity_min_singleInfo')}</span>
            <RForm.Item name={['giftMoneyCondition', 0, 'min']} className="!mb-0">
              <RInputNumber min={0} className="!w-40" placeholder={t('placeholder_input')} />
            </RForm.Item>
          </div>
          <RForm.Item name={['giftMoneyCondition', 0, 'max']} className="!mb-0">
            <RInputNumber value={null} className="!hidden" />
          </RForm.Item>
        </>
      );
    }

    if (giftMoneyMode === 2) {
      // 固定金額
      return (
        <div className="flex items-center gap-2">
          <span>{t('pages_activity_feedback')}</span>
          <RForm.Item name={['giftMoneyCondition', 0, 'feedback']} className="!mb-0">
            <RInputNumber placeholder={t('placeholder_input')} min={0} className="!w-full" />
          </RForm.Item>
          <span>，{t('pages_activity_min_singleInfo')}</span>
          <RForm.Item name={['giftMoneyCondition', 0, 'min']} className="!mb-0">
            <RInputNumber placeholder={t('placeholder_input')} min={0} className="!w-full" />
          </RForm.Item>
          <RForm.Item name={['giftMoneyCondition', 0, 'max']} className="!mb-0">
            <RInputNumber value={null} className="!hidden" />
          </RForm.Item>
        </div>
      );
    }

    return null;
  };

  const renderMultipleCondition = () => {
    if (giftMoneyMode === 1) {
      return (
        <>
          <div className="grid grid-cols-4 gap-4 items-center mb-2">
            <span className="col-span-2">{t('pages_activity_topup_gap')}</span>
            <div className="flex items-center gap-1">
              <RTooltip title={t('pages_activity_feedback_ratio_tooltip')} color="gray">
                <InformationIcon className="w-4.5 h-4.5 fill-info" />
              </RTooltip>
              <span>{t('pages_activity_feedback_ratio')}</span>
            </div>
            <span>{t('pages_activity_feedback_upper_limit')}</span>
          </div>
          {Array.from({ length: groupCount }, (_, index) => {
            const isLast = index === groupCount - 1;
            return (
              <div key={index} className="grid grid-cols-4 gap-4 items-center mb-2">
                <div className="flex items-center gap-2 col-span-2">
                  <RForm.Item name={['giftMoneyCondition', index, 'min']} className="!mb-0">
                    <RInputNumber
                      disabled
                      className="!w-32"
                      placeholder={index === 0 ? '0' : t('pages_activity_previous_input')}
                    />
                  </RForm.Item>
                  <span>-</span>
                  {isLast ? (
                    <RInputNumber value={null} disabled className="!w-32" placeholder="MAX" />
                  ) : (
                    <RForm.Item
                      name={['giftMoneyCondition', index, 'max']}
                      className="!mb-0"
                      rules={generateValidationRules(index, 'max')}
                    >
                      <RInputNumber
                        min={0}
                        className="!w-32"
                        placeholder={t('placeholder_input')}
                      />
                    </RForm.Item>
                  )}
                  {!isLast && <span>({t('pages_activity_not_include')})</span>}
                </div>
                <RForm.Item
                  name={['giftMoneyCondition', index, 'feedbackPercent']}
                  className="!mb-0"
                  rules={generateValidationRules(index, 'feedbackPercent')}
                >
                  <RInputNumber
                    min={0}
                    max={100}
                    className="!w-full"
                    placeholder={t('placeholder_input')}
                  />
                </RForm.Item>
                <RForm.Item
                  name={['giftMoneyCondition', index, 'feedbackUpperLimit']}
                  className="!mb-0"
                  rules={generateValidationRules(index, 'feedbackUpperLimit')}
                >
                  <RInputNumber min={0} className="!w-full" placeholder={t('placeholder_input')} />
                </RForm.Item>
              </div>
            );
          })}
        </>
      );
    }

    if (giftMoneyMode === 2) {
      return (
        <>
          <div className="grid grid-cols-3 gap-2 items-center mb-2">
            <span className="col-span-2">{t('pages_activity_topup_gap')}</span>
            <span>{t('pages_activity_feedback')}</span>
          </div>
          {Array.from({ length: groupCount }, (_, index) => {
            const isLast = index === groupCount - 1;
            return (
              <div key={index} className="grid grid-cols-3 gap-4 items-center mb-2">
                <div className="flex items-center gap-2 col-span-2">
                  <RForm.Item name={['giftMoneyCondition', index, 'min']} className="!mb-0">
                    <RInputNumber
                      disabled
                      className="!w-32"
                      placeholder={index === 0 ? '0' : t('pages_activity_previous_input')}
                    />
                  </RForm.Item>
                  <span>-</span>
                  {isLast ? (
                    <RInputNumber value={null} disabled className="!w-32" placeholder="MAX" />
                  ) : (
                    <RForm.Item
                      name={['giftMoneyCondition', index, 'max']}
                      className="!mb-0"
                      rules={generateValidationRules(index, 'max')}
                    >
                      <RInputNumber
                        min={0}
                        className="!w-32"
                        placeholder={t('placeholder_input')}
                      />
                    </RForm.Item>
                  )}
                  {!isLast && <span>({t('pages_activity_not_include')})</span>}
                </div>
                <RForm.Item
                  name={['giftMoneyCondition', index, 'feedback']}
                  className="!mb-0"
                  rules={generateValidationRules(index, 'feedback')}
                >
                  <RInputNumber min={0} className="!w-40" placeholder={t('placeholder_input')} />
                </RForm.Item>
              </div>
            );
          })}
        </>
      );
    }

    return null;
  };

  return (
    <div className="p-3 bg-bg-primary mt-4">
      <p className="my-2">{t('pages_activity_giftmoney_setting')}</p>

      <RForm.Item label={t('pages_activity_giftmoney_amount')} name="giftMoneyMode">
        <RRadio.Group
          onChange={onGiftMoneyModeChange}
          options={[
            { label: t('pages_activity_giftmoney_amount_ratio'), value: 1 },
            { label: t('pages_activity_giftmoney_amount_fixed'), value: 2 }
          ]}
        />
      </RForm.Item>

      <RForm.Item label={t('pages_activity_giftCondition')}>
        <RRadio.Group
          onChange={(e) => onConditionTypeChange(e.target.value)}
          value={giftMoneyConditionType}
          options={[
            { label: t('pages_activity_giftmoney_single'), value: 'single' }, // 獎勵條件單組
            {
              label: (
                <div className="flex items-center gap-2 break-keep">
                  {t('pages_activity_giftmoney_multiple')}
                  <RInputNumber
                    min={2}
                    max={10}
                    value={groupCount}
                    onChange={(value) => onGroupCountChange(Number(value) || 3)}
                  />
                  {t('pages_activity_giftmoney_group')}
                </div>
              ),
              value: 'multiple' // 獎勵條件多組
            }
          ]}
        />
      </RForm.Item>

      <div className="space-y-2 relative before:content-[''] before:absolute before:top-[-14px] before:left-0 before:right-0 before:h-px before:bg-gray-200">
        {giftMoneyConditionType === 'single' ? renderSingleCondition() : renderMultipleCondition()}
      </div>
    </div>
  );
};
