import './cardTabs.scss';

import { useTranslation } from 'react-i18next';

import ArrowLeftIcon from '@/assets/img/icon/arrow-left.svg?react';
import { RTabs } from '@/components/RTabs';
import ActivityList from '@/pages/operation/activity/activityListPage';
import DisactiveListPage from '@/pages/operation/activity/disactiveListPage';
import { ActivityFilter } from '@/pages/operation/activity/enum';
import {
  ActivityOptionsContext,
  ActivityStatusContext,
  ActivityTemplateContext
} from '@/pages/operation/activity/hooks/useActivityContext';
import {
  useActivityStatus,
  useActivityTemplate
} from '@/pages/operation/activity/hooks/useActivityOptions';

import CreateActivityPage from './CreateActivityPage';

// 模板選項 Provider - 不會因為 tab 切換而重新渲染
const ActivityTemplateProvider = ({ children }: { children: React.ReactNode }) => {
  const { data: activityTemplate } = useActivityTemplate();

  const templateOptions = useMemo(
    () =>
      activityTemplate?.map((item: { id: number; label: string }) => ({
        label: item.label,
        value: item.id
      })) || [],
    [activityTemplate]
  );

  return (
    <ActivityTemplateContext.Provider value={templateOptions}>
      {children}
    </ActivityTemplateContext.Provider>
  );
};

// 狀態選項 Provider - 會根據 tab 切換而更新
const ActivityStatusProvider = ({
  children,
  currentFilter
}: {
  children: React.ReactNode;
  currentFilter: ActivityFilter;
}) => {
  const { data: activityStatus } = useActivityStatus(currentFilter);

  const statusOptions = useMemo(
    () =>
      activityStatus?.map((item: { id: number; label: string }) => ({
        label: item.label,
        value: item.id
      })) || [],
    [activityStatus]
  );

  return (
    <ActivityStatusContext.Provider value={statusOptions}>
      {children}
    </ActivityStatusContext.Provider>
  );
};

// 組合 Provider - 提供完整的選項數據
const ActivityOptionsProvider = ({
  children,
  currentFilter
}: {
  children: React.ReactNode;
  currentFilter: ActivityFilter;
}) => {
  const { data: templateOptions } = useActivityTemplate();
  const { data: statusOptions } = useActivityStatus(currentFilter);

  const contextValue = useMemo(
    () => ({
      templateOptions:
        templateOptions?.map((item: { id: number; label: string }) => ({
          label: item.label,
          value: item.id
        })) || [],
      statusOptions:
        statusOptions?.map((item: { id: number; label: string }) => ({
          label: item.label,
          value: item.id
        })) || []
    }),
    [templateOptions, statusOptions]
  );

  return (
    <ActivityOptionsContext.Provider value={contextValue}>
      {children}
    </ActivityOptionsContext.Provider>
  );
};

const Activity = () => {
  const { t } = useTranslation();

  const [activeTabKey, setActiveTabKey] = useState(ActivityFilter.CONTINUED);
  const [showCreatePage, setShowCreatePage] = useState(false);
  const [editingActivityId, setEditingActivityId] = useState<number | undefined>(undefined);

  // 取得 activityTemplate options
  const { data: activityTemplate } = useActivityTemplate();
  const templateOptions = useMemo(
    () =>
      activityTemplate?.map((item: { id: number; label: string }) => ({
        id: item.id,
        label: item.label
      })) || [],
    [activityTemplate]
  );

  // 使用 useMemo 來避免每次渲染都重新創建 tabItems
  const tabItems = useMemo(
    () => [
      {
        key: ActivityFilter.CONTINUED,
        label: t('pages_activity_list'),
        children: (
          <ActivityList
            setShowCreatePage={setShowCreatePage}
            setEditingActivityId={setEditingActivityId}
          />
        )
      },
      {
        key: ActivityFilter.DISCONTINUED,
        label: t('pages_activity_list_discontinued'),
        children: <DisactiveListPage />
      }
    ],
    [t]
  );

  const handleTabChange = (key: ActivityFilter | string) => {
    setActiveTabKey(key as ActivityFilter);
  };

  const handleCloseModal = () => {
    setShowCreatePage(false);
    setEditingActivityId(undefined);
  };

  return (
    <>
      <div className="relative">
        {showCreatePage && (
          <div className="w-full bg-white shadow-content flex items-center px-4 py-3 absolute z-10 top-[-65px]">
            <ArrowLeftIcon onClick={handleCloseModal} className="w-6 h-6" />
            <span className="font-bold text-sm">
              {editingActivityId
                ? t('common_edit_name', { name: t('pages_activity') })
                : t('common_add_name', { name: t('pages_activity') })}
            </span>
          </div>
        )}

        {showCreatePage ? (
          <CreateActivityPage
            activityTemplate={templateOptions}
            onCancel={handleCloseModal}
            activityId={editingActivityId}
          />
        ) : (
          <ActivityTemplateProvider>
            <ActivityStatusProvider currentFilter={activeTabKey}>
              <ActivityOptionsProvider currentFilter={activeTabKey}>
                <RTabs
                  size="middle"
                  activeKey={activeTabKey}
                  onChange={handleTabChange}
                  items={tabItems}
                  indicator={{ size: 100, align: 'center' }}
                  tabBarGutter={40}
                  className="game-tabs activity-list-tabs"
                />
                <Outlet />
              </ActivityOptionsProvider>
            </ActivityStatusProvider>
          </ActivityTemplateProvider>
        )}
      </div>
    </>
  );
};

export default Activity;
