import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';

import {
  deleteAnnouncement,
  getAnnouncement,
  updateAnnouncementOrder,
  updateAnnouncementStatus
} from '@/api/operation';
import { ActionButtons } from '@/components/ActionButtons';
import OperatorCell from '@/components/cells/OperatorCell';
import PublishTypeCell from '@/components/cells/PublishTypeCell';
import DragTable from '@/components/DragTable';
import RButton from '@/components/RButton';
import StatusLabel from '@/components/StatusLabel';
import useActions from '@/hooks/useActions';
import useFrontendLanguage from '@/hooks/useFrontendLanguage';
import TableSearchLayout from '@/layout/TableSearchLayout';
import { Announcement } from '@/types/operation';

import AnnouncementModal from './AnncouncementModal';

const AnnouncementTitle = ({ record }: { record: Announcement }) => {
  const { defaultFrontendLanguage } = useFrontendLanguage();
  const defaultContent =
    record.contentMapping?.[defaultFrontendLanguage] || Object.values(record.contentMapping)[0];
  return <p>{defaultContent.title}</p>;
};

const AnnouncementContent = ({ record }: { record: Announcement }) => {
  const { defaultFrontendLanguage } = useFrontendLanguage();
  const defaultContent =
    record.contentMapping?.[defaultFrontendLanguage] || Object.values(record.contentMapping)[0];
  return (
    <div className="line-clamp-2" dangerouslySetInnerHTML={{ __html: defaultContent.content }} />
  );
};

const columns = [
  {
    title: 'pages_announcement_title',
    dataIndex: 'title',
    key: 'title',
    render: (_: string, record: Announcement) => {
      return <AnnouncementTitle record={record} />;
    }
  },
  {
    title: 'pages_announcement_content',
    dataIndex: 'content',
    key: 'content',
    width: 500,
    render: (_: string, record: Announcement) => {
      return <AnnouncementContent record={record} />;
    }
  },
  {
    title: 'components_formTimePeriodSelect_publishType',
    dataIndex: 'publishType',
    render: (value: number, record: Announcement) => {
      return <PublishTypeCell value={value} record={record} />;
    }
  },
  {
    title: 'common_lastOperate',
    dataIndex: 'updatedAt',
    render: (_: number, record: Announcement) => {
      return <OperatorCell record={record} />;
    }
  },
  {
    title: 'common_status',
    dataIndex: 'status',
    render: (value: number) => {
      return <StatusLabel status={value} />;
    }
  }
];

const TableWrap = ({
  setInitialValues,
  setOpen
}: {
  setInitialValues: (value: Announcement) => void;
  setOpen: (value: boolean) => void;
}) => {
  const { t } = useTranslation();
  const [dataSource, setDataSource] = useState<Announcement[]>([]);
  const queryClient = useQueryClient();
  const { handleDelete, handleEditStatus } = useActions();

  const { data, isPending } = useQuery({
    queryKey: ['announcement'],
    queryFn: () => getAnnouncement(),
    select: (data) => data.data?.sort((a, b) => a.order - b.order) || []
  });

  useEffect(() => {
    setDataSource(data || []);
  }, [data]);

  const { mutate: updateAnnouncementOrderMutation, isPending: isUpdateOrderPending } = useMutation({
    mutationFn: updateAnnouncementOrder,
    onError: () => {
      setDataSource(data || []);
    }
  });

  const handleChangeOrder = (data: Announcement[]) => {
    setDataSource(data);
    updateAnnouncementOrderMutation({ orders: data.map((item) => item.id) });
  };

  const { mutate: deleteAnnouncementMutation, isPending: isDeletePending } = useMutation({
    mutationFn: deleteAnnouncement,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['announcement'] });
    }
  });

  const { mutate: updateAnnouncementMutation, isPending: isUpdatePending } = useMutation({
    mutationFn: updateAnnouncementStatus,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['announcement'] });
    }
  });

  const actionColumn = {
    title: 'common_action',
    dataIndex: 'action',
    render: (_: string, record: Announcement) => {
      return (
        <ActionButtons
          data={record}
          buttons={['edit', 'delete', 'status']}
          onEdit={() => {
            setInitialValues(record);
            setOpen(true);
          }}
          onEditStatus={() => {
            handleEditStatus(t('pages_announcement'), record.status, () => {
              updateAnnouncementMutation({ id: record.id, status: record.status === 1 ? 0 : 1 });
            });
          }}
          isEditStatusPending={isUpdatePending}
          onDelete={() => {
            handleDelete(t('pages_announcement'), () => {
              deleteAnnouncementMutation({ id: record.id });
            });
          }}
        />
      );
    }
  };

  const tableColumns = [...columns, actionColumn].map((column) => ({
    ...column,
    title: typeof column.title === 'string' ? t(column.title) : column.title
  }));

  return (
    <DragTable
      keyName="id"
      onSortChange={handleChangeOrder}
      loading={isPending || isUpdateOrderPending || isDeletePending}
      rowKey="id"
      dataSource={dataSource}
      columns={tableColumns}
      pagination={false}
    ></DragTable>
  );
};

const AnnouncementPage = () => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const [initialValues, setInitialValues] = useState<Announcement>();

  const handleAdd = () => {
    setOpen(true);
    setInitialValues(undefined);
  };

  return (
    <>
      <TableSearchLayout>
        <RButton className="mb-4" onClick={handleAdd}>
          {t('common_add_name', { name: t('pages_announcement') })}
        </RButton>
        <TableWrap setInitialValues={setInitialValues} setOpen={setOpen} />
      </TableSearchLayout>
      <AnnouncementModal
        open={open}
        onClose={() => setOpen(false)}
        initialValues={initialValues}
      ></AnnouncementModal>
    </>
  );
};

export default AnnouncementPage;
