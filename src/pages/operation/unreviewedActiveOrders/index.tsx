import { useMutation } from '@tanstack/react-query';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { updateUnreviewedActiveOrderReviewStatus } from '@/api/activity';
import AutoRefreshControl from '@/components/AutoRefreshControl';
import OperatorCell from '@/components/cells/OperatorCell';
import RButton from '@/components/RButton';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RSelect from '@/components/RSelect';
import RTable from '@/components/RTable';
import RTag from '@/components/RTag';
import { TagColor } from '@/components/RTag/types';
import SearchForm from '@/components/SearchForm';
import useConfirmModal from '@/hooks/useConfirmModal';
import usePagination from '@/hooks/usePagination';
import TableSearchLayout from '@/layout/TableSearchLayout';
import { UnreviewedActiveOrder, UnreviewedActiveOrderSearchParams } from '@/types/activity';
import { cleanSearchParams } from '@/utils/object';

import useActivityTemplates from './hooks/useActivityTemplates';
import useUnreviewedActiveOrders from './hooks/useUnreviewedActiveOrders';

type SearchFormValues = Omit<UnreviewedActiveOrderSearchParams, 'page' | 'limit'>;

const tagColors: TagColor[] = ['magenta', 'volcano', 'purple'];

const SearchFormWrap = ({
  onSearch,
  onReset,
  onRefresh
}: {
  onSearch: (values: Omit<UnreviewedActiveOrderSearchParams, 'page' | 'limit'>) => void;
  onReset: () => void;
  onRefresh: () => void;
}) => {
  const { t } = useTranslation();
  const { data: activityTemplates, isPending: isTemplatesLoading } = useActivityTemplates();

  const templateTypeOptions = useMemo(() => {
    const allOption = { label: t('common_all'), value: 'all' };
    if (!activityTemplates) {
      return [allOption];
    }

    const templateOptions = activityTemplates.map((template) => ({
      label: template.label,
      value: template.id
    }));

    return [allOption, ...templateOptions];
  }, [t, activityTemplates]);

  const handleSearch = (values: SearchFormValues) => {
    const searchValues = cleanSearchParams(values);
    onSearch(searchValues);
  };

  return (
    <div className="space-y-4">
      <SearchForm<SearchFormValues> onSearch={handleSearch} onReset={onReset} className="">
        <div className="flex items-center gap-3 mr-4">
          <div>{t('common_autoRefresh')}:</div>
          <AutoRefreshControl
            onRefresh={onRefresh}
            enabled={true}
            interval={30000}
            isShowManualRefresh={true}
          />
        </div>
        <RForm.Item
          name="templateType"
          label={t('pages_operation_unreviewedActiveOrders_template_type')}
        >
          <RSelect
            placeholder={t('common_please_select', {
              name: t('pages_operation_unreviewedActiveOrders_template_type')
            })}
            options={templateTypeOptions}
            loading={isTemplatesLoading}
            allowClear
          />
        </RForm.Item>
        <RForm.Item name="account" label={t('pages_operation_unreviewedActiveOrders_account')}>
          <RInput
            placeholder={t('common_please_enter', {
              name: t('common_account')
            })}
          />
        </RForm.Item>
      </SearchForm>
    </div>
  );
};

const UnreviewedActiveOrdersPage = () => {
  const { t } = useTranslation();

  const { confirmModal } = useConfirmModal();
  const { page, setPage, limit, setLimit } = usePagination({});
  const [params, setParams] = useState<Omit<UnreviewedActiveOrderSearchParams, 'page' | 'limit'>>(
    {}
  );
  const [approvingRecord, setApprovingRecord] = useState<UnreviewedActiveOrder | null>(null);
  const [rejectingRecord, setRejectingRecord] = useState<UnreviewedActiveOrder | null>(null);

  const unreviewedActiveOrdersQuery = useUnreviewedActiveOrders({
    page,
    limit,
    ...params
  });

  const updateUnreviewedActiveOrderReviewStatusMutation = useMutation({
    mutationFn: updateUnreviewedActiveOrderReviewStatus,
    onSuccess: () => {
      unreviewedActiveOrdersQuery.refetch();
    }
  });

  const formatRewards = (rewards: UnreviewedActiveOrder['reward']) => {
    if (!rewards || rewards.length === 0) return '-';
    return rewards
      .map((reward) => `${reward.type}: ${reward.amount} (ID: ${reward.rewardId})`)
      .join(', ');
  };

  const getLocalizedContent = (
    contentMapping: UnreviewedActiveOrder['contentMapping'],
    locale: string,
    field: keyof UnreviewedActiveOrder['contentMapping'][string]
  ) => {
    const content = contentMapping?.[locale];
    if (content) {
      return content[field];
    }

    return undefined;
  };

  const handleApprove = useCallback(
    (record: UnreviewedActiveOrder) => {
      confirmModal({
        content: t('common_confirm_approve_name', { name: record.id }),
        onOk: () => {
          setApprovingRecord(record);
          updateUnreviewedActiveOrderReviewStatusMutation.mutate({ id: record.id, review: 1 });
        }
      });
    },
    [confirmModal, t, updateUnreviewedActiveOrderReviewStatusMutation]
  );

  const handleReject = useCallback(
    (record: UnreviewedActiveOrder) => {
      confirmModal({
        content: t('common_confirm_reject_name', { name: record.id }),
        onOk: () => {
          setRejectingRecord(record);
          updateUnreviewedActiveOrderReviewStatusMutation.mutate({ id: record.id, review: 0 });
        }
      });
    },
    [confirmModal, t, updateUnreviewedActiveOrderReviewStatusMutation]
  );

  const tableColumns = useMemo(
    () => [
      {
        title: t('pages_operation_unreviewedActiveOrders_orderTime'),
        dataIndex: 'id',
        key: 'id',
        render: (_: number, record: UnreviewedActiveOrder) => {
          return <OperatorCell record={{ updatedBy: record.id, updatedAt: record.createdAt }} />;
        }
      },
      {
        title: t('pages_operation_unreviewedActiveOrders_event_template'),
        dataIndex: 'templateTypeLabel',
        key: 'templateTypeLabel'
      },
      {
        title: t('pages_operation_unreviewedActiveOrders_title'),
        dataIndex: 'contentMapping',
        key: 'contentMapping',
        width: 200,
        ellipsis: true,
        render: (contentMapping: UnreviewedActiveOrder['contentMapping']) => {
          const content = getLocalizedContent(contentMapping, 'zhTw', 'title');
          return <div className="line-clamp-2">{content}</div>;
        }
      },
      {
        title: t('common_account'),
        dataIndex: 'account',
        key: 'account'
      },
      {
        title: t('pages_operation_unreviewedActiveOrders_tags'),
        dataIndex: 'tags',
        key: 'tags',
        width: 150,
        ellipsis: true,
        render: (tags: string[]) => {
          return (
            <div>
              {tags.map((tag, index) => (
                <RTag key={index} color={tagColors[index % tagColors.length]}>
                  {tag}
                </RTag>
              ))}
            </div>
          );
        }
      },
      {
        title: t('pages_operation_unreviewedActiveOrders_rewards'),
        dataIndex: 'reward',
        key: 'reward',
        width: 200,
        ellipsis: true,
        render: (rewards: UnreviewedActiveOrder['reward']) => {
          const formattedRewards = formatRewards(rewards);
          return <div className="line-clamp-2">{formattedRewards}</div>;
        }
      },
      {
        title: t('common_action'),
        dataIndex: 'action',
        render: (_: string, record: UnreviewedActiveOrder) => (
          <div className="flex gap-1">
            <RButton
              variant="outlined"
              color="red"
              type="link"
              size="small"
              loading={
                approvingRecord?.id === record.id &&
                updateUnreviewedActiveOrderReviewStatusMutation.isPending
              }
              onClick={() => handleApprove(record)}
            >
              {t('common_approve')}
            </RButton>
            <RButton
              variant="outlined"
              color="green"
              type="link"
              size="small"
              loading={
                rejectingRecord?.id === record.id &&
                updateUnreviewedActiveOrderReviewStatusMutation.isPending
              }
              onClick={() => handleReject(record)}
            >
              {t('common_reject')}
            </RButton>
          </div>
        )
      }
    ],
    [
      approvingRecord?.id,
      handleApprove,
      handleReject,
      rejectingRecord?.id,
      t,
      updateUnreviewedActiveOrderReviewStatusMutation.isPending
    ]
  );

  const handleChangePage = (page: number, pageSize: number) => {
    setPage(page);
    setLimit(pageSize);
  };

  const handleSearch = (newParams: Omit<UnreviewedActiveOrderSearchParams, 'page' | 'limit'>) => {
    setParams((oldParams) => {
      // @ts-ignore
      const isTheSame = Object.entries(newParams).every(([key, value]) => oldParams[key] === value);
      if (isTheSame) {
        setTimeout(() => {
          unreviewedActiveOrdersQuery.refetch();
        }, 100);
      }
      return newParams;
    });
    setPage(1);
  };

  const handleReset = () => {
    const resetParams = {};
    setParams(resetParams);
    setPage(1);
  };

  const handleRefresh = () => {
    unreviewedActiveOrdersQuery.refetch();
  };

  // Render table view
  return (
    <TableSearchLayout
      searchFields={
        <SearchFormWrap onSearch={handleSearch} onReset={handleReset} onRefresh={handleRefresh} />
      }
    >
      <RTable
        loading={unreviewedActiveOrdersQuery.isPending}
        rowKey="id"
        dataSource={unreviewedActiveOrdersQuery.data?.data?.data || []}
        columns={tableColumns}
        pagination={{
          current: page,
          pageSize: limit,
          total: unreviewedActiveOrdersQuery.data?.data?.total || 0,
          showSizeChanger: true,
          onChange: handleChangePage
        }}
      />
    </TableSearchLayout>
  );
};

export default UnreviewedActiveOrdersPage;
