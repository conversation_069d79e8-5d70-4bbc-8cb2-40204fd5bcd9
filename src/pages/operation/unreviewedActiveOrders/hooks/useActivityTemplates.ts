import { useQuery } from '@tanstack/react-query';

import { getActivityTemplates } from '@/api/activity';

// Query keys for activity template related queries
export const activityTemplateKeys = {
  all: ['activityTemplate'] as const,
  templates: {
    all: ['activityTemplate', 'templates'] as const,
    list: () => ['activityTemplate', 'templates', 'list'] as const
  }
};

export const useActivityTemplates = () => {
  return useQuery({
    queryKey: activityTemplateKeys.templates.list(),
    queryFn: () => getActivityTemplates(),
    select: (data) => data.data
  });
};

export default useActivityTemplates;
