import { useQuery } from '@tanstack/react-query';

import { getUnreviewedActiveOrderList } from '@/api/activity';
import { UnreviewedActiveOrderSearchParams } from '@/types/activity';

// Query keys
export const unreviewedActiveOrderKeys = {
  all: ['unreviewedActiveOrder'] as const,
  lists: () => [...unreviewedActiveOrderKeys.all, 'list'] as const,
  list: (params: UnreviewedActiveOrderSearchParams) =>
    [...unreviewedActiveOrderKeys.lists(), params] as const
};

export const useUnreviewedActiveOrders = (params: UnreviewedActiveOrderSearchParams) => {
  return useQuery({
    queryKey: unreviewedActiveOrderKeys.list(params),
    queryFn: () => getUnreviewedActiveOrderList(params)
  });
};

export default useUnreviewedActiveOrders;
