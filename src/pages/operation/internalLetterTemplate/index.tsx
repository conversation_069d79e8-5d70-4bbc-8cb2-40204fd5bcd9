import { useMutation } from '@tanstack/react-query';
import { Spin } from 'antd';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { deleteMailTemplate, updateMailTemplateOrder } from '@/api/mail';
import ArrowLeft from '@/assets/img/icon/arrow-left.svg?react';
import OperatorCell from '@/components/cells/OperatorCell';
import DragTable from '@/components/DragTable';
import RButton from '@/components/RButton';
import useConfirmModal from '@/hooks/useConfirmModal';
import useToggleElementVisibility from '@/hooks/useToggleElementVisibility';
import TableSearchLayout from '@/layout/TableSearchLayout';
import { MailTemplate, MailTemplateAddingType, MailTemplateWithContentMapping } from '@/types/mail';

import MailTemplateForm from './components/MailTemplateForm';
import useMailTemplateDetails from './hooks/useMailTemplateDetails';
import useMailTemplates from './hooks/useMailTemplates';

const TableWrap = ({
  handleEdit,
  handleCopy,
  handleDelete,
  list,
  isLoadingList
}: {
  handleEdit: (record: MailTemplate) => void;
  handleCopy: (record: MailTemplate) => void;
  handleDelete: (record: MailTemplate) => void;
  list?: MailTemplate[];
  isLoadingList?: boolean;
}) => {
  const { t } = useTranslation();
  const [dataSource, setDataSource] = useState<MailTemplate[]>([]);

  useEffect(() => {
    setDataSource(list || []);
  }, [list]);

  const { mutate: updateMailTemplateOrderMutation, isPending: isUpdateOrderPending } = useMutation({
    mutationFn: updateMailTemplateOrder,
    onError: () => {
      setDataSource(list || []);
    }
  });

  const handleChangeOrder = (data: MailTemplate[]) => {
    setDataSource(data);
    updateMailTemplateOrderMutation({ orders: data.map((item) => item.id) });
  };

  const tableColumns = useMemo(
    () => [
      {
        title: t('internal_letter_template_id'),
        dataIndex: 'templateId',
        key: 'templateId'
      },
      {
        title: t('internal_letter_template_category'),
        dataIndex: 'categoryLabel',
        key: 'categoryLabel'
      },
      {
        title: t('internal_letter_template_type'),
        dataIndex: 'typeLabel',
        key: 'typeLabel'
      },

      {
        title: t('internal_letter_template_title'),
        dataIndex: 'title',
        key: 'title',
        width: 200,
        ellipsis: true
      },
      {
        title: t('common_lastOperate'),
        dataIndex: 'updatedAt',
        render: (_: number, record: MailTemplate) => {
          return (
            <OperatorCell record={{ updatedBy: record.createdBy, updatedAt: record.createdAt }} />
          );
        }
      },
      {
        title: t('common_note'),
        dataIndex: 'description',
        key: 'description',
        width: 200,
        ellipsis: true,
        render: (description: string) => {
          if (!description) return '-';
          return <div className="line-clamp-2">{description}</div>;
        }
      },
      {
        title: t('common_action'),
        dataIndex: 'action',
        render: (_: string, record: MailTemplate) => {
          return (
            <div className="flex gap-1">
              <RButton
                variant="outlined"
                color="primary"
                type="link"
                size="small"
                onClick={() => handleEdit(record)}
              >
                {t('common_edit')}
              </RButton>
              <RButton
                variant="outlined"
                color="green"
                type="link"
                size="small"
                onClick={() => handleCopy(record)}
              >
                {t('common_copy')}
              </RButton>
              <RButton
                variant="outlined"
                color="red"
                type="link"
                size="small"
                onClick={() => handleDelete(record)}
              >
                {t('common_delete')}
              </RButton>
            </div>
          );
        }
      }
    ],
    [handleCopy, handleDelete, handleEdit, t]
  );

  return (
    <DragTable
      keyName="id"
      onSortChange={handleChangeOrder}
      loading={isLoadingList || isUpdateOrderPending}
      rowKey="id"
      dataSource={dataSource}
      columns={tableColumns}
      pagination={false}
    />
  );
};

const InternalLetterTemplatePage = () => {
  const { t } = useTranslation();
  const { confirmModal } = useConfirmModal();
  const [currentView, setCurrentView] = useState<MailTemplateAddingType>(
    MailTemplateAddingType.NONE
  );
  const [initialValues, setInitialValues] = useState<
    Partial<MailTemplateWithContentMapping> | undefined
  >();
  const [contentLoadingId, setContentLoadingId] = useState<number | undefined>();
  const mailTemplateDetailsQuery = useMailTemplateDetails(contentLoadingId);
  const mailTemplatesQuery = useMailTemplates({});
  const deleteMutation = useMutation({
    mutationFn: deleteMailTemplate,
    onSuccess: () => {
      mailTemplatesQuery.refetch();
    }
  });

  useToggleElementVisibility('base-layout-header', currentView === MailTemplateAddingType.NONE);

  const handleAdd = () => {
    setInitialValues(undefined);
    setContentLoadingId(undefined);
    setCurrentView(MailTemplateAddingType.ADD);
  };

  const handleEdit = (record: MailTemplate) => {
    setContentLoadingId(record.id);
    setCurrentView(MailTemplateAddingType.EDIT);
  };

  const handleCopy = (record: MailTemplate) => {
    setContentLoadingId(record.id);
    setCurrentView(MailTemplateAddingType.COPY);
  };

  const handleDelete = (record: MailTemplate) => {
    confirmModal({
      content: t('common_confirm_delete_name', { name: record.title }),
      onOk: () => {
        deleteMutation.mutate({ id: record.id });
      }
    });
  };

  const handleBackToTable = () => {
    setCurrentView(MailTemplateAddingType.NONE);
    setInitialValues(undefined);
    setContentLoadingId(undefined);
  };

  const handleFormSuccess = () => {
    handleBackToTable();
  };

  const formTitle = useMemo(() => {
    let action: string = '';
    switch (currentView) {
      case MailTemplateAddingType.ADD:
        action = t('common_add');
        break;
      case MailTemplateAddingType.EDIT:
        action = t('common_edit');
        break;
      case MailTemplateAddingType.COPY:
        action = t('common_copy');
        break;
      default:
        break;
    }
    return t('{action}_internal_message_template', {
      action
    });
  }, [currentView, t]);

  // Render form view
  if (currentView !== MailTemplateAddingType.NONE) {
    return (
      <div>
        <div className="flex gap-2 justify-start items-center px-4 py-4.5 w-full bg-white shadow-content z-1">
          <ArrowLeft
            className="cursor-pointer fill-gray-400 h-[20px]"
            onClick={handleBackToTable}
          />
          <h2 className="m-0 text-sm font-bold">{formTitle}</h2>
        </div>
        <TableSearchLayout>
          {mailTemplateDetailsQuery.isLoading ||
          ((currentView === MailTemplateAddingType.COPY ||
            currentView === MailTemplateAddingType.EDIT) &&
            !initialValues) ? (
            <Spin spinning={true} />
          ) : (
            <MailTemplateForm
              initialValues={initialValues}
              onBack={handleBackToTable}
              onSuccess={handleFormSuccess}
              addingType={currentView}
            />
          )}
        </TableSearchLayout>
      </div>
    );
  }

  // Render table view
  return (
    <TableSearchLayout>
      <RButton className="mb-4" onClick={handleAdd}>
        {t('common_add_name', { name: t('internal_letter_template') })}
      </RButton>
      <TableWrap
        handleEdit={handleEdit}
        handleCopy={handleCopy}
        handleDelete={handleDelete}
        list={mailTemplatesQuery.data?.data?.data}
        isLoadingList={mailTemplatesQuery.isPending}
      />
    </TableSearchLayout>
  );
};

export default InternalLetterTemplatePage;
