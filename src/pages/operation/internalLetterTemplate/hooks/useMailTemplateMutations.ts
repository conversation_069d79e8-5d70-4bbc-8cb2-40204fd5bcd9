import { useMutation, useQueryClient } from '@tanstack/react-query';

import {
  createMailTemplate,
  deleteMailTemplate,
  updateMailTemplate,
  updateMailTemplateOrder
} from '@/api/mail';

import { mailTemplateKeys } from './useMailTemplates';

export const useMailTemplateMutations = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();

  const invalidateQueries = () => {
    queryClient.invalidateQueries({ queryKey: mailTemplateKeys.all });
  };

  const onSuccessHandler = () => {
    invalidateQueries();
    onSuccess?.();
  };

  const createMutation = useMutation({
    mutationFn: createMailTemplate,
    onSuccess: onSuccessHandler
  });

  const updateMutation = useMutation({
    mutationFn: updateMailTemplate,
    onSuccess: onSuccessHandler
  });

  const deleteMutation = useMutation({
    mutationFn: deleteMailTemplate,
    onSuccess: onSuccessHandler
  });

  const updateOrderMutation = useMutation({
    mutationFn: updateMailTemplateOrder,
    onSuccess: onSuccessHandler
  });

  return {
    createMutation,
    updateMutation,
    deleteMutation,
    updateOrderMutation
  };
};

export default useMailTemplateMutations;
