import { useQuery } from '@tanstack/react-query';

import { getMailVariables } from '@/api/mail';

// Query keys for mail category related queries
export const mailVariablesKeys = {
  all: ['mailVariables'] as const,
  categories: {
    all: ['mailVariables', 'variables'] as const,
    list: () => ['mailVariables', 'variables', 'list'] as const
  }
};

export const useMailVariables = () => {
  return useQuery({
    queryKey: mailVariablesKeys.categories.list(),
    queryFn: () => getMailVariables(),
    select: (data) => data.data
  });
};
