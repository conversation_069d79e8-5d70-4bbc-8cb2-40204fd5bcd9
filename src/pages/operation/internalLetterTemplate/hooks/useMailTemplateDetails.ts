import { useQuery } from '@tanstack/react-query';

import { getMailTemplateDetails } from '@/api/mail';

// Query keys
export const mailTemplateKeys = {
  all: ['mailTemplate'] as const,
  details: () => [...mailTemplateKeys.all, 'detail'] as const,
  detail: (id?: number) => [...mailTemplateKeys.details(), id] as const
};

export const useMailTemplateDetails = (id?: number) => {
  return useQuery({
    queryKey: mailTemplateKeys.detail(id),
    queryFn: () => {
      if (!id) {
        throw new Error('ID is required');
      }
      return getMailTemplateDetails(id);
    },
    enabled: !!id
  });
};

export default useMailTemplateDetails;
