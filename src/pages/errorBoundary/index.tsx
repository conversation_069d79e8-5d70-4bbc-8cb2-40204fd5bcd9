// import { useAsset } from '@/hooks/useAsset';
import { useTranslation } from 'react-i18next';

import { RButton } from '@/components/RButton';

export default function ErrorBoundaryPage() {
  const { t } = useTranslation();

  const handleReload = () => {
    window.location.reload();
  };

  return (
    <div className="flex h-screen w-screen flex-col items-center justify-center bg-primary">
      {/* <img src={useAsset('maintain.webp')} alt="errorBoundary" width={160} height={160} className="mb-6" /> */}
      <p className="text-base font-bold text-white mb-6">{t('pages_errorBoundary_title')}</p>

      <RButton
        onClick={handleReload}
        type="default"
        className="bg-white text-primary font-semibold hover:bg-gray-100 border-white hover:border-gray-100"
      >
        {t('common_reload')}
      </RButton>
    </div>
  );
}
