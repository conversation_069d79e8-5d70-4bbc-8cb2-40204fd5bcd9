import { useMutation } from '@tanstack/react-query';

import { login, LoginResponseData, otpLogin, updatePassword } from '@/api/auth';
import { FormValue, PasswordResetComponent } from '@/components/_pages/login/FirstLoginChangePsw';
import { LoginForm, LoginFormComponent } from '@/components/_pages/login/LoginForm';
import OtpCodeMode from '@/components/_pages/login/OtpCodeModal';
import SwitchLanguage from '@/components/SwitchLanguage';
import OTPVerificationComponent from '@/components/VerificationComponent';
import { usePermissionStore } from '@/store/permissionStore';
import { useUserStore } from '@/store/userStore';

// 密碼確認的驗證函數
const createConfirmPasswordValidator = (t: (key: string) => string) => {
  return ({ getFieldValue }: { getFieldValue: (field: string) => string }) => ({
    validator(_: unknown, value: string) {
      if (!value || getFieldValue('newPassword') === value) {
        return Promise.resolve();
      }
      return Promise.reject(new Error(t('login_error_password_not_match')));
    }
  });
};

// 處理瀏覽器返回按鈕的函數
const setupBrowserBackButtonHandler = () => {
  window.history.pushState(null, '', window.location.pathname);
  const handlePopState = () => {
    window.history.pushState(null, '', window.location.pathname);
  };

  window.addEventListener('popstate', handlePopState);
  return () => {
    window.removeEventListener('popstate', handlePopState);
  };
};

function IndexPage() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [firstLogin, setFirstLogin] = useState(false);
  const [otpVerification, setOtpVerification] = useState(false);
  const [otpInput, setOtpInput] = useState(false);
  const [, setOtp] = useState('');
  const [loginData, setLoginData] = useState<LoginResponseData | null>(null);
  const { setToken, userInfo, clearUser, fetchUserInfo, isLoading } = useUserStore();
  const { setPermissions, getDefaultRoute } = usePermissionStore();

  // 保存表單值
  const loginFormRef = useRef<LoginForm>({
    username: '',
    password: ''
  });
  const passwordFormRef = useRef<FormValue>({
    newPassword: '',
    confirmPassword: ''
  });

  // 輸入帳密登入
  const loginMutation = useMutation({
    mutationFn: (params: { account: string; password: string }) => login(params),
    onSuccess: ({ code, data }) => {
      if (code === 200 && data) {
        setLoginData(data);
        if (data.firstLogin) {
          setFirstLogin(true);
        } else if (data.rebindOtp) {
          setOtpVerification(true);
        } else {
          setOtpInput(true);
        }
      }
    }
  });

  // OTP 登入
  const otpLoginMutation = useMutation({
    mutationFn: (params: {
      account: string;
      password: string;
      newPassword?: string;
      newPasswordConfirmation?: string;
      otp: string;
    }) => otpLogin(params),
    onSuccess: async ({ code, data }) => {
      if (code === 200 && data?.token) {
        try {
          setToken(data.token);
          await fetchUserInfo();
        } catch (error) {
          console.error('Login process error:', error);
          clearUser();
        }
      }
    }
  });

  // 首次登入需更新密碼
  const updatePasswordMutation = useMutation({
    mutationFn: (params: { password: string; passwordConfirmation: string }) =>
      updatePassword(params),
    onSuccess: ({ code }) => {
      if (code === 200) {
        setFirstLogin(false);
        // 更新密碼成功後，進行 OTP 驗證
        setOtpVerification(true);
      }
    }
  });

  // 首次Google OTP 驗證後的登入
  const handleOtpLogin = (otpValue: string) => {
    const otpLoginParams = {
      account: loginFormRef.current.username,
      password: loginFormRef.current.password,
      newPassword: passwordFormRef.current.newPassword,
      newPasswordConfirmation: passwordFormRef.current.confirmPassword,
      otp: otpValue
    };

    otpLoginMutation.mutate(otpLoginParams);
  };

  const handlePasswordUpdate = (values: FormValue) => {
    passwordFormRef.current = values;

    updatePasswordMutation.mutate({
      password: values.newPassword,
      passwordConfirmation: values.confirmPassword
    });
  };

  // 當 OTP 驗證完成時的處理函數
  const handleOtpVerificationComplete = (otpValue: string) => {
    setOtp(otpValue);
    // 使用收到的 OTP 與儲存的表單數據進行登入
    handleOtpLogin(otpValue);
  };

  // OTP 直接輸入完成處理函數
  const handleOtpInputSubmit = (values: { otp: string }) => {
    handleOtpLogin(values.otp);
  };

  const onFinish = (values: LoginForm) => {
    loginFormRef.current = values;

    loginMutation.mutate({
      account: values.username,
      password: values.password
    });
  };

  // 使用 history API 處理瀏覽器返回按鈕
  useEffect(() => {
    return setupBrowserBackButtonHandler();
  }, []);

  // 監聽 loading 狀態
  useEffect(() => {
    if (!isLoading && userInfo) {
      const permissions = userInfo.permissions ?? [];
      if (permissions.length > 0) {
        setPermissions(permissions);
        navigate(getDefaultRoute());
      } else {
        clearUser();
      }
    }
  }, [isLoading, userInfo, clearUser, navigate, setPermissions, getDefaultRoute]);

  // 密碼確認的表單規則
  const confirmPasswordRules = [
    { required: true, message: t('login_error_password_required') },
    createConfirmPasswordValidator(t)
  ];

  return (
    <>
      <div className="relative flex flex-col h-screen w-screen items-center justify-center bg-primary">
        <div className=" flex justify-end bg-primary absolute top-10 right-10">
          <SwitchLanguage />
        </div>
        <h1 className="mb-6 text-white text-center text-[32px] font-bold">Admin system</h1>
        <div className="h-[270px]">
          {!otpInput && (
            <LoginFormComponent onFinish={onFinish} isLoading={loginMutation.isPending} />
          )}
        </div>
      </div>
      {/* 首次登入 googleOTP驗證 */}
      {otpVerification && (
        <OTPVerificationComponent
          setOtpVerification={setOtpVerification}
          setOtp={handleOtpVerificationComplete}
          qrcodeUrl={loginData?.qrcodeUrl}
        />
      )}
      {/* 第一次登入修改密碼 */}
      {firstLogin && (
        <PasswordResetComponent
          onFinish={handlePasswordUpdate}
          isLoading={updatePasswordMutation.isPending}
          confirmPasswordRules={confirmPasswordRules}
        />
      )}
      {/* otp驗證 */}
      {otpInput && (
        <OtpCodeMode
          handleOtpInputSubmit={handleOtpInputSubmit}
          isLoading={isLoading}
          isPending={otpLoginMutation.isPending}
        />
      )}
    </>
  );
}

export default IndexPage;
