import { useMutation } from '@tanstack/react-query';
import { useState } from 'react';

import { manualReleaseFrozenGiftItem } from '@/api/gift';
import useConfirmModal from '@/hooks/useConfirmModal';
import TableSearchLayout from '@/layout/TableSearchLayout';
import { GiftQuerySearchFormValues, GiftQuotaFrozenInfo } from '@/types/gift';

import GiftQueryContent from './Content';
import useGiftQuotaCheck from './hooks/useGiftQuotaCheck';
import SearchFormGiftQuery from './SearchForm';

const AccountGiftQueryPage = () => {
  const { t } = useTranslation();
  const [params, setParams] = useState<{
    account?: string;
  }>({
    account: undefined
  });

  const giftQuotaCheckQuery = useGiftQuotaCheck(params.account);

  const releaseFrozenMutation = useMutation({
    mutationFn: manualReleaseFrozenGiftItem,
    onSuccess: () => {
      giftQuotaCheckQuery.refetch();
    }
  });

  const { confirmModal } = useConfirmModal();

  const onPressRelease = useCallback(
    (record: GiftQuotaFrozenInfo) => {
      confirmModal({
        content: t('common_confirm_release_frozen'),
        onOk: () => {
          releaseFrozenMutation.mutate({ id: record.id });
        }
      });
    },
    [confirmModal, releaseFrozenMutation, t]
  );

  const handleSearch = (newParams: GiftQuerySearchFormValues) => {
    setParams((oldParams) => {
      // @ts-ignore
      const isTheSame = Object.entries(newParams).every(([key, value]) => oldParams[key] === value);
      if (isTheSame) {
        setTimeout(() => {
          giftQuotaCheckQuery.refetch();
        }, 100);
      }
      return newParams;
    });
  };

  const handleReset = () => {
    const resetParams = {
      account: undefined
    };
    setParams(resetParams);
  };

  return (
    <TableSearchLayout
      searchFields={<SearchFormGiftQuery onSearch={handleSearch} onReset={handleReset} />}
    >
      <GiftQueryContent
        isSearched={!!params.account}
        data={giftQuotaCheckQuery.data?.data}
        isLoading={giftQuotaCheckQuery.isLoading}
        error={giftQuotaCheckQuery.error}
        onPressRelease={onPressRelease}
        isReleaseLoading={releaseFrozenMutation.isPending}
        releasingId={releaseFrozenMutation.variables?.id}
      />
    </TableSearchLayout>
  );
};

export default AccountGiftQueryPage;
