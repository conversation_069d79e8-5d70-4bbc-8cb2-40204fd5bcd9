import RButton from '@/components/RButton';
import RTable from '@/components/RTable';
import RTag from '@/components/RTag';
import { useAsset } from '@/hooks/useAsset';
import { useVipListQuery } from '@/pages/game/management/hooks';
import { FrozenStatus, GiftQuotaCheckResponse, GiftQuotaFrozenInfo } from '@/types/gift';
import { formatTime } from '@/utils/time';

const listTagColor = ['magenta', 'purple', 'volcano', 'gray', 'default', 'green'];

const getTagColor = (tag: string) => {
  const sumOfTagAscii = tag.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
  const index = sumOfTagAscii % listTagColor.length;
  return listTagColor[index];
};

type GiftQueryContentProps = {
  isSearched?: boolean;
  data?: GiftQuotaCheckResponse;
  isLoading?: boolean;
  error?: Error | null;
  onPressRelease: (record: GiftQuotaFrozenInfo) => void;
  isReleaseLoading?: boolean;
  releasingId?: string;
};

export default function GiftQueryContent({
  isSearched,
  data,
  isLoading,
  error,
  onPressRelease,
  isReleaseLoading,
  releasingId
}: GiftQueryContentProps) {
  const { t } = useTranslation();
  const placeholderImage = useAsset('account_gift_query_placeholder.webp');
  const noDataImage = useAsset('account_gift_query_no_data.webp');
  const noFrozenRecordImage = useAsset('no_data.webp');
  const allVipLv = useVipListQuery();

  const currentVipName = useMemo(() => {
    return allVipLv.data?.find((vip) => vip.level === data?.playerInfo.level)?.name;
  }, [allVipLv.data, data?.playerInfo.level]);

  const frozenColumn = useMemo(
    () => [
      {
        title: t('pages_transaction_accountGiftQuery_frozenType'), // 項目 // project
        dataIndex: 'type'
      },
      {
        title: t('pages_transaction_accountGiftQuery_frozenDetail'), // 詳情 // Details
        dataIndex: 'title'
      },
      {
        title: t('pages_transaction_accountGiftQuery_frozenAmount'), // 點數 // Points
        dataIndex: 'amount',
        render: (amount: number) => <div>{amount.toLocaleString()}</div>
      },
      {
        title: t('pages_transaction_accountGiftQuery_frozenCreatedAt'), // 開始凍結時間 // Start freezing time
        dataIndex: 'createdAt',
        render: (createdAt: number) => formatTime(createdAt)
      },
      {
        title: t('pages_transaction_accountGiftQuery_frozenReleasedAt'), // 結束凍結時間 // End freeze time
        dataIndex: 'releasedAt',
        render: (releasedAt: number) => formatTime(releasedAt)
      },
      {
        title: t('pages_transaction_accountGiftQuery_frozenStatus'), // 狀態 // state
        dataIndex: 'statusLabel'
      },
      {
        title: t('common_action'), // 操作 // operate
        dataIndex: 'status',
        render: (status: FrozenStatus, record: GiftQuotaFrozenInfo) => (
          <div>
            {status === FrozenStatus.FROZEN && (
              <RButton
                variant="outlined"
                color="red"
                size="small"
                onClick={() => onPressRelease(record)}
                loading={isReleaseLoading && releasingId === record.id}
              >
                {t('pages_transaction_accountGiftQuery_frozenRelease')}
              </RButton>
            )}
            {status === FrozenStatus.MANUAL_RELEASED && (
              <RButton
                variant="outlined"
                color="green"
                size="small"
                onClick={() => onPressRelease(record)}
                loading={isReleaseLoading && releasingId === record.id}
              >
                {t('pages_transaction_accountGiftQuery_restoreFrozen')}
              </RButton>
            )}
          </div>
        )
      }
    ],
    [isReleaseLoading, onPressRelease, releasingId, t]
  );

  if (!isSearched) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <img src={placeholderImage} alt="403" width={160} height={160} className="mb-6" />
        <div>{t('pages_transaction_accountGiftQuery_instruction')}</div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <div>{t('pages_platform_loading_message')}...</div>
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <img src={noDataImage} alt="403" width={160} height={160} className="mb-6" />
        <div>{t('pages_transaction_accountGiftQuery_noData')}</div>
      </div>
    );
  }

  const { playerInfo, giftInfo, frozenInfo } = data;

  return (
    <div className="p-6 space-y-6 w-full bg-white">
      <div className="flex gap-12">
        <div className="flex flex-col gap-4">
          <div className="text-lg font-semibold">
            {t('pages_transaction_accountGiftQuery_playerInfo')}
          </div>
          <div className="p-6 h-full flex flex-col justify-between rounded-lg border border-gray-200 bg-white min-w-[300px]">
            <div className="flex justify-between">
              <div className="flex flex-col justify-between items-center gap-2">
                <span className="text-gray-600">
                  {t('pages_transaction_accountGiftQuery_playerName')}
                </span>
                <span className="font-medium">{playerInfo.name}</span>
              </div>
              <div className="flex flex-col justify-between items-center gap-2">
                <span className="text-gray-600">{t('pages_giftSetting_vip')}</span>
                <span className="font-medium">{currentVipName}</span>
              </div>
            </div>
            <div className="flex flex-col justify-between gap-2">
              <div className="text-secondary">{t('pages_player_playerTag')}</div>
              <div className="flex">
                {playerInfo.tags.map((tag, index) => (
                  <RTag key={index} color={getTagColor(tag)}>
                    {tag}
                  </RTag>
                ))}
              </div>
            </div>
          </div>
        </div>
        <div className="flex flex-col gap-4">
          <div className="text-lg font-semibold">
            {t('pages_transaction_accountGiftQuery_quotaInfo')}
          </div>
          <div className="p-6 rounded-lg border border-gray-200 bg-white min-w-[300px]">
            <div className="space-y-6">
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-gray-600">
                    {t('pages_transaction_accountGiftQuery_todayTimes')}
                  </span>
                  <span className="text-xs text-gray-500">{giftInfo.todayTimes}</span>
                </div>
                <div className="text-sm text-gray-700 mb-1">
                  {giftInfo.todayTimes} / {giftInfo.levelTimes}
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-black h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${Math.min((giftInfo.todayTimes / giftInfo.levelTimes) * 100, 100)}%`
                    }}
                  ></div>
                </div>
              </div>
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-gray-600">
                    {t('pages_transaction_accountGiftQuery_todayAmount')}
                  </span>
                  <span className="text-xs text-yellow-600">★</span>
                </div>
                <div className="text-sm text-gray-700 mb-1">
                  {giftInfo.todayAmount.toLocaleString()} /{' '}
                  {parseFloat(giftInfo.levelAmount).toLocaleString()}
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-black h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${Math.min((giftInfo.todayAmount / parseFloat(giftInfo.levelAmount)) * 100, 100)}%`
                    }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Frozen Information */}
      <div className="flex flex-col gap-4">
        <div className="text-lg font-semibold">
          {t('pages_transaction_accountGiftQuery_frozenInfo')}
        </div>
        {frozenInfo.length === 0 && (
          <div className="flex flex-col items-center justify-center">
            <img src={noFrozenRecordImage} alt="403" width={160} height={160} className="mb-6" />
            <div>{t('pages_transaction_accountGiftQuery_noFrozenRecord')}</div>
          </div>
        )}
        {frozenInfo.length > 0 && (
          <>
            <div>
              {t('pages_transaction_accountGiftQuery_frozenTotal', {
                total: parseFloat(data.playerInfo.remainFrozen).toLocaleString()
              })}
            </div>
            <RTable
              rowKey="id"
              scroll={{ x: 600 }}
              dataSource={frozenInfo}
              columns={frozenColumn}
              pagination={false}
            />
          </>
        )}
      </div>
    </div>
  );
}
