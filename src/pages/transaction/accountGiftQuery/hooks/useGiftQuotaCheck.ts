import { useQuery } from '@tanstack/react-query';

import { checkGiftQuota } from '@/api/gift';

// Query keys
export const giftQuotaKeys = {
  all: ['giftQuota'] as const,
  checks: () => [...giftQuotaKeys.all, 'check'] as const,
  check: (playerAccount?: string) => [...giftQuotaKeys.checks(), playerAccount] as const
};

export const useGiftQuotaCheck = (playerAccount?: string) => {
  return useQuery({
    queryKey: giftQuotaKeys.check(playerAccount),
    queryFn: () => {
      if (!playerAccount) {
        throw new Error('Player account is required');
      }
      return checkGiftQuota({ account: playerAccount });
    },
    enabled: !!playerAccount
  });
};

export default useGiftQuotaCheck;
