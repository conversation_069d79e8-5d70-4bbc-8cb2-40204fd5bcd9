import { PlusCircleOutlined } from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import { Spin } from 'antd';
import type { FormInstance } from 'antd/es/form';

import { getTopupChannel, getTopupItem } from '@/api/topup';
import popularTopupDefault from '@/assets/img/popular-topup-default.webp';
import popularTopupHighlight from '@/assets/img/popular-topup-highlight.webp';
import { ActionButtons } from '@/components/ActionButtons';
import DragTable from '@/components/DragTable';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RSelect from '@/components/RSelect';
import RSwitch from '@/components/RSwitch';
import RTag from '@/components/RTag';
import RUploader from '@/components/RUploader';
import useImageUpload from '@/components/RUploader/useImageUpload';
import useFrontendLanguage from '@/hooks/useFrontendLanguage';
import type { TopupCategory } from '@/types/topup';
import { TopupPopular } from '@/types/topup';
import { numberFormat } from '@/utils/numbers';

import { useTopupSettingAction } from './useTopupSettingAction';

type SpecialRow = { id: string };

interface FormValues {
  zhTwTitle: string;
  categoryKey: string;
  channelKey: string;
  itemId: number;
  highlight: number;
  zhTwPhoto?: File | string;
  zhTwContent: string;
}

const useTopupData = (record: TopupPopular) => {
  const { defaultFrontendLanguage } = useFrontendLanguage();
  const { data: channels } = useQuery({
    queryKey: ['topupChannel', record.categoryKey],
    queryFn: () => getTopupChannel({ categoryKey: record.categoryKey }),
    enabled: !!record.categoryKey
  });

  const { data: items } = useQuery({
    queryKey: ['topupItem', record.channelKey],
    queryFn: () => getTopupItem({ channelKey: record.channelKey }),
    enabled: !!record.channelKey
  });

  return {
    defaultFrontendLanguage,
    channels,
    items
  };
};

const PopularGroup = ({ record }: { record: TopupPopular }) => {
  const { defaultFrontendLanguage } = useFrontendLanguage();
  const value = record.contentMapping[defaultFrontendLanguage]?.title;
  return value ? <p>{value}</p> : '-';
};

const PopularTopupPic = ({ record }: { record: TopupPopular }) => {
  const { defaultFrontendLanguage } = useFrontendLanguage();
  const value = record.imgUrlMapping[defaultFrontendLanguage]?.photo;
  return value ? (
    <img src={value} alt="popularTopupPic" className="object-contain w-12 h-12 cursor-pointer" />
  ) : (
    '-'
  );
};

const TagCell = ({ record }: { record: TopupPopular }) => {
  const { defaultFrontendLanguage } = useFrontendLanguage();
  const value = record.contentMapping[defaultFrontendLanguage]?.content;
  return value ? <p>{value}</p> : '-';
};

const CategoryCell = ({
  record,
  topupCategory
}: {
  record: TopupPopular;
  topupCategory: TopupCategory[] | undefined;
}) => {
  const { defaultFrontendLanguage } = useFrontendLanguage();
  const category = topupCategory?.find((cate) => cate.key === record.categoryKey);
  return category ? <p>{category.contentMapping[defaultFrontendLanguage]?.title}</p> : '-';
};

const ChannelCell = ({ record }: { record: TopupPopular }) => {
  const { channels, defaultFrontendLanguage } = useTopupData(record);
  const channel = channels?.data?.find((ch) => ch.key === record.channelKey);
  return channel ? <p>{channel.contentMapping[defaultFrontendLanguage]?.title}</p> : '-';
};

const ItemCell = ({ record }: { record: TopupPopular }) => {
  const { t } = useTranslation();
  const { items } = useTopupData(record);
  const item = items?.data?.find((i) => i.id === record.itemId);
  return item ? (
    <p>
      {`$${numberFormat(item.buyPrice)} / ${numberFormat(item.amount)} ${t('pages_transaction_transactionRecord_amount')}`}
    </p>
  ) : (
    '-'
  );
};

const TopupCategorySelect = ({
  topupCategory,
  form
}: {
  topupCategory: TopupCategory[] | undefined;
  form: FormInstance<FormValues>;
}) => {
  const { defaultFrontendLanguage } = useFrontendLanguage();
  const { t } = useTranslation();

  return (
    <RForm.Item name="categoryKey" rules={[{ required: true, message: t('placeholder_select') }]}>
      <RSelect
        className="!w-[140px]"
        options={topupCategory?.map((cate) => ({
          label: cate.contentMapping[defaultFrontendLanguage]?.title,
          value: cate.key
        }))}
        onChange={() => form.setFieldsValue({ channelKey: undefined, itemId: undefined })}
      />
    </RForm.Item>
  );
};

const TopupChannelSelect = ({
  categoryKey,
  form
}: {
  categoryKey: string;
  form: FormInstance<FormValues>;
}) => {
  const { t } = useTranslation();
  const { data: channels } = useQuery({
    queryKey: ['topupChannel', categoryKey],
    queryFn: () => getTopupChannel({ categoryKey }),
    enabled: !!categoryKey
  });
  const { defaultFrontendLanguage } = useFrontendLanguage();

  return (
    <RForm.Item name="channelKey" rules={[{ required: true, message: t('placeholder_select') }]}>
      <RSelect
        className="!w-[140px]"
        options={channels?.data?.map((channel) => ({
          label: channel.contentMapping[defaultFrontendLanguage]?.title,
          value: channel.key
        }))}
        disabled={!categoryKey}
        onChange={() => form.setFieldsValue({ itemId: undefined })}
      />
    </RForm.Item>
  );
};

const TopupItemSelect = ({ channelKey }: { channelKey: string }) => {
  const { t } = useTranslation();
  const { data: items } = useQuery({
    queryKey: ['topupItem', channelKey],
    queryFn: () => getTopupItem({ channelKey }),
    enabled: !!channelKey
  });

  return (
    <RForm.Item name="itemId" rules={[{ required: true, message: t('placeholder_select') }]}>
      <RSelect
        className="!w-[140px]"
        options={items?.data?.map((item) => ({
          label: `$${item.buyPrice} / ${item.amount} ${t('pages_transaction_transactionRecord_amount')}`,
          value: item.id,
          disabled: item.status === 0
        }))}
        disabled={!channelKey}
      />
    </RForm.Item>
  );
};

const renderFormItem = (
  dataIndex: string,
  form: FormInstance<FormValues>,
  categoryKey: string,
  channelKey: string,
  accept: string,
  beforeUpload: (file: File) => boolean,
  t: (key: string) => string,
  topupCategory: TopupCategory[] | undefined
) => {
  switch (dataIndex) {
    case 'groupName':
      return (
        <RForm.Item name="zhTwTitle" rules={[{ required: true, message: t('placeholder_input') }]}>
          <RInput
            placeholder={t('pages_transaction_topupsetting_popular_group')}
            className="w-32 !h-8"
          />
        </RForm.Item>
      );
    case 'categoryKey':
      return <TopupCategorySelect topupCategory={topupCategory} form={form} />;
    case 'channelKey':
      return <TopupChannelSelect categoryKey={categoryKey} form={form} />;
    case 'itemId':
      return <TopupItemSelect channelKey={channelKey} />;
    case 'popularTopupPic':
      return (
        <RForm.Item name="zhTwPhoto">
          <RUploader accept={accept} beforeUpload={beforeUpload} showPlaceholder={false} />
        </RForm.Item>
      );
    case 'tag':
      return (
        <RForm.Item name="zhTwContent">
          <RInput className="!w-32" placeholder={t('placeholder_input')} />
        </RForm.Item>
      );
    case 'highlight':
      return (
        <RForm.Item name="highlight" valuePropName="checked">
          <RSwitch
            isBoolean={false}
            defaultCheckValue={1}
            onChange={(checked) => form.setFieldsValue({ highlight: checked ? 1 : 0 })}
          />
        </RForm.Item>
      );
    default:
      return null;
  }
};

const useTableColumns = (
  form: FormInstance<FormValues>,
  topupCategory: TopupCategory[] | undefined,
  categoryKey: string,
  channelKey: string,
  editingId: number | null,
  dataSource: TopupPopular[],
  handleAdd: () => void,
  handleSubmit: () => void,
  handleCancel: () => void,
  handleEdit: (record: TopupPopular) => void,
  handleDeleteTopupPopular: (record: TopupPopular) => void
) => {
  const { t } = useTranslation();
  const { accept, beforeUpload } = useImageUpload({
    fileType: 'jpg,jpeg,png,webp',
    maxSize: 1
  });

  const baseColumns = [
    {
      title: 'pages_transaction_topupsetting_popular_group',
      dataIndex: 'groupName',
      render: (_: string, record: TopupPopular) => <PopularGroup record={record} />,
      width: 200
    },
    {
      title: 'pages_transaction_topupsetting_popular_category',
      dataIndex: 'categoryKey',
      render: (_: string, record: TopupPopular) => (
        <CategoryCell record={record} topupCategory={topupCategory} />
      )
    },
    {
      title: 'pages_transaction_topupsetting_popular_channel',
      dataIndex: 'channelKey',
      render: (_: string, record: TopupPopular) => <ChannelCell record={record} />
    },
    {
      title: 'pages_transaction_topupsetting_popular_item',
      dataIndex: 'itemId',
      render: (_: string, record: TopupPopular) => <ItemCell record={record} />
    },
    {
      title: 'pages_transaction_topupsetting_popular_pic',
      dataIndex: 'popularTopupPic',
      render: (_: string, record: TopupPopular) => <PopularTopupPic record={record} />
    },
    {
      title: 'pages_playerTag',
      dataIndex: 'tag',
      render: (_: string, record: TopupPopular) => <TagCell record={record} />
    },
    {
      title: 'pages_transaction_topupsetting_popular_isHighlight',
      dataIndex: 'highlight',
      render: (highlight: number) => (
        <RTag color={highlight === 1 ? 'green' : 'red'}>
          {highlight === 1 ? t('common_yes') : t('common_no')}
        </RTag>
      )
    }
  ];

  const columnsWithSpecialRender = baseColumns.map((col) => ({
    ...col,
    render: (value: unknown, record: TopupPopular | { id: string }): React.ReactNode => {
      if (record.id === '__plus__') {
        return col.dataIndex === 'groupName' ? (
          <PlusCircleOutlined className="cursor-pointer text-xl text-primary" onClick={handleAdd} />
        ) : null;
      }

      if (record.id === '__add__' || record.id === '__edit__') {
        const editingRecord =
          record.id === '__edit__' ? dataSource.find((item) => item.id === editingId) : null;

        return (
          <RForm<FormValues>
            form={form}
            layout="vertical"
            initialValues={editingRecord ? toFormValues(editingRecord) : { highlight: 1 }}
          >
            {renderFormItem(
              col.dataIndex,
              form,
              categoryKey,
              channelKey,
              accept,
              beforeUpload as (file: File) => boolean,
              t,
              topupCategory
            )}
          </RForm>
        );
      }

      return col.render?.(value as never, record as TopupPopular) ?? value;
    }
  }));

  const actionColumn = {
    title: t('common_action'),
    dataIndex: 'action',
    render: (_: string, record: TopupPopular | { id: string }) => {
      if (record.id === '__add__' || record.id === '__edit__') {
        return (
          <ActionButtons
            data={{ ...record, status: undefined }}
            onAdd={handleSubmit}
            onCancel={handleCancel}
            buttons={['add', 'cancel']}
          />
        );
      }
      if (record.id === '__plus__') return null;
      return (
        <ActionButtons
          data={{ ...record, status: undefined }}
          onEdit={() => handleEdit(record as TopupPopular)}
          onDelete={() => handleDeleteTopupPopular(record as TopupPopular)}
          buttons={['edit', 'delete']}
        />
      );
    }
  };

  return [...columnsWithSpecialRender, actionColumn].map((column) => ({
    ...column,
    title: typeof column.title === 'string' ? t(column.title) : column.title
  }));
};

const toFormValues = (record: TopupPopular): FormValues => ({
  zhTwTitle: record.contentMapping?.zhTw?.title ?? '',
  zhTwContent: record.contentMapping?.zhTw?.content ?? '',
  categoryKey: record.categoryKey,
  channelKey: record.channelKey,
  itemId: record.itemId,
  highlight: record.highlight
});

const TableWrap = () => {
  const [dataSource, setDataSource] = useState<TopupPopular[]>([]);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [isAdding, setIsAdding] = useState(false);
  const [form] = RForm.useForm<FormValues>();

  const {
    topupPopular,
    handleUpdateOrder,
    isLoading,
    topupCategory,
    handleCreateTopupPopular,
    handleEditTopupPopular,
    handleDeleteTopupPopular
  } = useTopupSettingAction();

  const categoryKey = RForm.useWatch('categoryKey', form);
  const channelKey = RForm.useWatch('channelKey', form);

  useEffect(() => {
    setDataSource(topupPopular || []);
  }, [topupPopular]);

  useEffect(() => {
    if (editingId !== null) {
      const record = dataSource.find((item) => item.id === editingId);
      if (record) {
        form.setFieldsValue(toFormValues(record));
      }
    }
  }, [editingId, dataSource, form]);

  const handleAdd = () => {
    setIsAdding(true);
    setEditingId(null);
    form.resetFields();
    form.setFieldsValue({ highlight: 1 });
  };

  const handleEdit = (record: TopupPopular) => {
    setEditingId(record.id);
    setIsAdding(false);
    form.setFieldsValue(toFormValues(record));
  };

  const handleCancel = () => {
    setIsAdding(false);
    setEditingId(null);
    form.resetFields();
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (isAdding) {
        handleCreateTopupPopular(values);
      } else if (editingId !== null) {
        handleEditTopupPopular({ ...values, id: editingId });
      }
      handleCancel();
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const handleChangeOrder = (data: (TopupPopular | SpecialRow)[]) => {
    // 編輯及新增欄不排序
    const filtered = data.filter(
      (item) => item.id !== '__plus__' && item.id !== '__add__' && item.id !== '__edit__'
    ) as TopupPopular[];
    setDataSource(filtered);
    handleUpdateOrder(filtered);
  };

  const getDisplayData = (): (TopupPopular | { id: string })[] => {
    const rows: (TopupPopular | { id: string })[] = [{ id: '__plus__' }];
    if (isAdding) {
      rows.push({ id: '__add__' });
    }
    if (editingId !== null) {
      return [
        ...rows,
        ...dataSource.map((item) => (item.id === editingId ? { ...item, id: '__edit__' } : item))
      ];
    }
    return [...rows, ...dataSource];
  };

  const tableColumns = useTableColumns(
    form,
    topupCategory,
    categoryKey,
    channelKey,
    editingId,
    dataSource,
    handleAdd,
    handleSubmit,
    handleCancel,
    handleEdit,
    handleDeleteTopupPopular
  );

  return (
    <Spin spinning={isLoading}>
      <DragTable
        keyName="id"
        onSortChange={handleChangeOrder}
        loading={isLoading}
        rowKey="id"
        dataSource={getDisplayData()}
        columns={tableColumns}
        pagination={false}
      />
    </Spin>
  );
};

export default function PopularSettings() {
  const { t } = useTranslation();

  return (
    <div className="m-3 p-6 bg-white border border-component-border min-h-[90vh]">
      <h2 className="m-0 text-sm font-bold">{t('pages_transaction_topupsetting_popular')}</h2>
      <div className="flex items-center gap-10 mt-4">
        <div>
          <p className="mb-2 font-medium">
            {t('pages_transaction_topupsetting_popular_highlight')}
          </p>
          <div className="w-80">
            <img src={popularTopupHighlight} alt="popularTopupHighlight" />
          </div>
        </div>
        <div>
          <p className="mb-2 font-medium">{t('pages_transaction_topupsetting_popular_default')}</p>
          <div className="w-80">
            <img src={popularTopupDefault} alt="popularTopupDefault" />
          </div>
        </div>
      </div>
      <p className="text-xs text-warning mt-4 mb-3">
        {t('pages_transaction_topupsetting_popular_info')}
      </p>
      <TableWrap />
    </div>
  );
}
