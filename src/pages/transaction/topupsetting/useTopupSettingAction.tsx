import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import {
  createTopupPopular,
  deleteTopupPopular,
  editTopupPopular,
  getTopupCategory,
  getTopupPopular,
  type TopupPopularCreateParams,
  type TopupPopularEditParams,
  updateTopupPopularOrder
} from '@/api/topup';
import useActions from '@/hooks/useActions';
import { TopupPopular } from '@/types/topup';

const TOPUP_POPULAR_QUERY_KEY = 'topupPopular';
const TOPUP_CATEGORY_QUERY_KEY = 'topupCategory';

export const useTopupSettingAction = () => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const { handleDelete } = useActions();

  // 獲取充值類別
  const { data: topupCategory, isPending: isTopupCategoryPending } = useQuery({
    queryKey: [TOPUP_CATEGORY_QUERY_KEY],
    queryFn: () => getTopupCategory(),
    select: (data) => data?.data?.sort((a, b) => a.order - b.order) || []
  });

  // 獲取熱門充值組合
  const { data: topupPopular, isPending } = useQuery({
    queryKey: [TOPUP_POPULAR_QUERY_KEY],
    queryFn: () => getTopupPopular(),
    select: (data) => data?.data?.sort((a, b) => a.order - b.order) || []
  });

  // 更新熱門充值組合順序
  const { mutate: updateOrder, isPending: isUpdateOrderPending } = useMutation({
    mutationFn: updateTopupPopularOrder,
    onError: () => {
      queryClient.invalidateQueries({ queryKey: [TOPUP_POPULAR_QUERY_KEY] });
    }
  });

  // 刪除熱門組合
  const { mutate: deleteTopupPopularMutation, isPending: isDeletePending } = useMutation({
    mutationFn: deleteTopupPopular,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [TOPUP_POPULAR_QUERY_KEY] });
    }
  });

  // 新增熱門組合
  const { mutate: createTopupPopularMutation, isPending: isCreatePending } = useMutation({
    mutationFn: createTopupPopular,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [TOPUP_POPULAR_QUERY_KEY] });
    }
  });

  const { mutate: editTopupPopularMutation, isPending: isEditPending } = useMutation({
    mutationFn: editTopupPopular,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [TOPUP_POPULAR_QUERY_KEY] });
    }
  });

  const handleCreateTopupPopular = (topupPopular: TopupPopularCreateParams) => {
    createTopupPopularMutation(topupPopular);
  };

  const handleEditTopupPopular = (topupPopular: TopupPopularEditParams) => {
    editTopupPopularMutation(topupPopular);
  };

  const handleDeleteTopupPopular = (topupPopular: TopupPopular) => {
    handleDelete(t('pages_transaction_topupsetting_popular_name'), () => {
      deleteTopupPopularMutation({ id: topupPopular.id });
    });
  };

  // 處理順序更新操作
  const handleUpdateOrder = (topupPopulars: TopupPopular[]) => {
    updateOrder({ orders: topupPopulars.map((item) => item.id) });
  };

  const isLoading = isPending || isUpdateOrderPending || isCreatePending || isEditPending;

  return {
    topupCategory,
    isTopupCategoryPending,
    topupPopular,
    isPending,
    isUpdateOrderPending,
    isDeletePending,
    isEditPending,
    handleCreateTopupPopular,
    handleEditTopupPopular,
    handleDeleteTopupPopular,
    handleUpdateOrder,
    isLoading
  };
};
