import { useMutation, useQuery } from '@tanstack/react-query';
import { Spin } from 'antd';

import { getPlatformSettings } from '@/api/platform';
import { editTopupSetting } from '@/api/topup';
import { getVipList } from '@/api/vip';
import InformationIcon from '@/assets/img/icon/information.svg?react';
import RButton from '@/components/RButton';
import RForm from '@/components/RForm';
import RInputNumber from '@/components/RInputNumber';
import RSelect from '@/components/RSelect';
import RTooltip from '@/components/Rtooltip';

const VipSelect = ({ initialData }: { initialData?: number }) => {
  const { t } = useTranslation();
  const { data: vipList } = useQuery({
    queryKey: ['vipList'],
    queryFn: () => getVipList({ status: 1 })
  });

  return (
    <RForm.Item
      label={
        <div className="flex gap-1 items-center">
          <span className="text-text-secondary">
            {t('pages_transaction_topupsetting_basicVipLevel')}
          </span>
          <RTooltip title={t('pages_transaction_topupsetting_basicVipLevel_tooltip')} color="gray">
            <InformationIcon className="w-4.5 h-4.5 fill-info" />
          </RTooltip>
        </div>
      }
      layout="vertical"
      name="id"
      initialValue={initialData}
      rules={[{ required: true, message: '請選擇VIP等級' }]}
    >
      <RSelect
        className="!w-[320px] "
        options={vipList?.data?.map((vip) => ({
          label: vip.name,
          value: vip.id
        }))}
      />
    </RForm.Item>
  );
};

export default function BasicSetting() {
  const { t } = useTranslation();
  const [form] = RForm.useForm();
  const { data, isLoading } = useQuery({
    queryKey: ['platformSettings'],
    queryFn: getPlatformSettings
  });
  const currentVipLevel = data?.data?.topupVipLevel;
  const currentFrozenRemainTimeLimit = data?.data?.topupFrozenRemainTimeLimit;
  useEffect(() => {
    const formValues = {
      id: currentVipLevel ?? 1,
      topupFrozenRemainTimeLimit: currentFrozenRemainTimeLimit ?? 192
    };
    form.setFieldsValue(formValues);
  }, [currentVipLevel, currentFrozenRemainTimeLimit, form]);

  const { mutate: saveVipLevel, isPending } = useMutation({
    mutationFn: (values: { topupVipLevel: number; topupFrozenRemainTimeLimit: number }) =>
      editTopupSetting(values)
  });

  const handleSave = () => {
    form.validateFields().then((values) => {
      saveVipLevel({
        topupVipLevel: values.id,
        topupFrozenRemainTimeLimit: values.topupFrozenRemainTimeLimit
      });
    });
  };

  return (
    <Spin spinning={isLoading}>
      <div className="m-3 p-6 bg-white border border-component-border min-h-[60vh]">
        <div className="flex gap-3 items-end mb-4">
          <h2 className="m-0 text-sm font-bold">{t('pages_transaction_topupsetting_basic')}</h2>
          <p className="text-xs text-text-icon">{t('pages_platform_description')}</p>
        </div>
        <RForm form={form} layout="vertical">
          <VipSelect />
          <RForm.Item
            name="topupFrozenRemainTimeLimit"
            label={
              <p>
                <p>{t('pages_transaction_topupsetting_topupFrozenRemainTimeLimit')}</p>
                <p className="text-xs text-text-icon">
                  {t('pages_transaction_topupsetting_topupFrozenRemainTimeLimit_description')}
                </p>
              </p>
            }
            rules={[{ required: true, message: t('placeholder_input') }]}
          >
            <RInputNumber className="!w-[320px]" min={0} suffix={t('common_hour')} />
          </RForm.Item>
        </RForm>
        <RButton type="primary" className="!w-30 mt-15" onClick={handleSave} loading={isPending}>
          {t('common_save')}
        </RButton>
      </div>
    </Spin>
  );
}
