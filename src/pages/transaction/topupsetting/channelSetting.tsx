import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Spin } from 'antd';
import { Content } from 'antd/es/layout/layout';

import { updateTopupCategoryOrder } from '@/api/topup';
import ArrowRightIcon from '@/assets/img/icon/arrow-right.svg?react';
import EditIcon from '@/assets/img/icon/operation-edit.svg?react';
import DragTabs from '@/components/DragTabs';
import RTooltip from '@/components/Rtooltip';
import useFrontendLanguage from '@/hooks/useFrontendLanguage';
import { TopupCategory } from '@/types/topup';

import ChannelList from './channelSettingComponent/channelList';
import EditCategoryModal from './channelSettingComponent/EditCategoryModal';
import { useTopupSettingAction } from './useTopupSettingAction';

const CategoryTab = ({
  category,
  defaultLanguage,
  setEditingCategory
}: {
  category: TopupCategory;
  defaultLanguage: string;
  setEditingCategory: (category: TopupCategory) => void;
}) => {
  const { t } = useTranslation();
  return (
    <div className="flex gap-1">
      <span>{category.contentMapping[defaultLanguage]?.title}</span>
      <RTooltip title={t('common_edit')} placement="bottomLeft" arrow={false} color="gray">
        <EditIcon
          className="w-4 h-4 fill-text-secondary"
          onClick={() => setEditingCategory(category)}
        />
      </RTooltip>
      <div className="flex items-center justify-center w-5 h-5 ml-auto rounded-lg bg-component-active">
        <span className=" !text-text-icon">{category.channelsCount}</span>
      </div>
    </div>
  );
};

export default function ChannelSetting() {
  const { defaultFrontendLanguage } = useFrontendLanguage();
  const { topupCategory, isTopupCategoryPending } = useTopupSettingAction();
  const queryClient = useQueryClient();
  const [tabItems, setTabItems] = useState<
    {
      id: number;
      label: React.ReactNode;
      key: string;
      arrow: React.ReactNode | null;
    }[]
  >([]);
  const [activeKey, setActiveKey] = useState<string | undefined>(undefined);
  const [editingCategory, setEditingCategory] = useState<TopupCategory | null>(null);

  const { mutate: updateOrder, isPending: isUpdateOrderPending } = useMutation({
    mutationFn: updateTopupCategoryOrder,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['topupCategory'] });
    },
    onError: () => {
      queryClient.invalidateQueries({ queryKey: ['topupCategory'] });
    }
  });

  useEffect(() => {
    if (topupCategory && topupCategory.length > 0 && !activeKey) {
      setActiveKey(topupCategory[0].key);
    }
  }, [topupCategory, activeKey]);

  useEffect(() => {
    const items =
      topupCategory?.map((item) => ({
        id: item.id,
        label: (
          <CategoryTab
            category={item}
            defaultLanguage={defaultFrontendLanguage}
            setEditingCategory={setEditingCategory}
          />
        ),
        key: item.key,
        arrow:
          item.key === activeKey ? <ArrowRightIcon className="w-4 h-4 fill-text-secondary" /> : null
      })) || [];
    setTabItems(items);
  }, [topupCategory, defaultFrontendLanguage, activeKey]);

  const handleChangeOrder = (newItems: typeof tabItems) => {
    // console.log('New order:', newItems);
    setTabItems(newItems);
    updateOrder({ orders: newItems.map((item) => Number(item.id)) });
  };

  const isLoading = isUpdateOrderPending || isTopupCategoryPending;

  return (
    <Spin spinning={isLoading}>
      <div className="flex mt-3 ml-4 mr-9 bg-white border border-component-border min-h-[90vh]">
        {/* 左側主通道 */}
        <div className="border-r w-60 border-component-border">
          <DragTabs
            items={tabItems}
            onChange={(key) => {
              setActiveKey(key);
            }}
            showArrow={true}
            onSortChange={handleChangeOrder}
            tabPosition="left"
            dragDirection="vertical"
            activeKey={activeKey}
          />
        </div>

        {/* 右側內容 */}
        <Content className="flex-1 max-w-[1440px]">
          {/* 上方子通道 Tags */}
          <div className="mb-4">
            <ChannelList
              key={activeKey}
              categoryKey={activeKey || ''}
              defaultFrontendLanguage={defaultFrontendLanguage}
            />
          </div>
        </Content>
      </div>
      <EditCategoryModal
        open={!!editingCategory}
        onClose={() => setEditingCategory(null)}
        initialValues={editingCategory}
        defaultFrontendLanguage={defaultFrontendLanguage}
      />
    </Spin>
  );
}
