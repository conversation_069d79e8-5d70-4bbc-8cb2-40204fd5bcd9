import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { getPlayerTagListOption } from '@/api/player';
import { editTopupChannel, editTopupChannelOrder, editTopupChannelPhoto } from '@/api/topup';
import { ActionButtons } from '@/components/ActionButtons';
import DragTable from '@/components/DragTable';
import RButton from '@/components/RButton';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RModal from '@/components/RModal';
import RSelect from '@/components/RSelect';
import RTag from '@/components/RTag';
import { tagColors } from '@/components/RTag/types';
import RUploader from '@/components/RUploader';
import useImageUpload from '@/components/RUploader/useImageUpload';
import { PlayerTag } from '@/types/playerlist';
import { TopupChannel } from '@/types/topup';

type EditChannelModalProps = {
  open: boolean;
  onClose: () => void;
  channels: TopupChannel[];
  defaultFrontendLanguage?: string;
  categoryKey: string;
};

type FormValue = {
  name: string;
  zhTwPhoto: string;
  tags: number[];
};

type SpecialRow = { id: string };

const enum TagType {
  All = -1,
  NoTag = -2
}

const ChannelName = ({
  record,
  defaultFrontendLanguage
}: {
  record: TopupChannel;
  defaultFrontendLanguage: string;
}) => {
  const value = record.contentMapping?.[defaultFrontendLanguage || 'zh_TW']?.title;
  if (!value) return '-';
  return <div>{value}</div>;
};

const ChannelPhoto = ({
  record,
  defaultFrontendLanguage
}: {
  record: TopupChannel;
  defaultFrontendLanguage: string;
}) => {
  const value =
    typeof record.imgUrlMapping === 'string'
      ? record.imgUrlMapping
      : record.imgUrlMapping?.[defaultFrontendLanguage || 'zh_TW']?.photo;
  if (!value) return '-';
  return <img src={value} alt="channel photo" className="w-10 h-10 object-cover" />;
};

const EditChannelModal = ({
  open,
  onClose,
  channels,
  defaultFrontendLanguage,
  categoryKey
}: EditChannelModalProps) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const [editingId, setEditingId] = useState<number | null>(null);
  const [form] = RForm.useForm<FormValue>();
  const [dataSource, setDataSource] = useState<TopupChannel[]>(channels);

  // 當外部 channels 更新時，同步更新本地 state 和重置表單
  useEffect(() => {
    if (!open) {
      setEditingId(null);
      form.resetFields();
    } else {
      setDataSource(channels);
    }
  }, [open, channels, form]);

  const { data: tagList } = useQuery({
    queryKey: ['tagList'],
    queryFn: getPlayerTagListOption
  });
  const tagListOptions = [
    { label: t('common_all'), value: TagType.All },
    { label: t('pages_playerTag_Tag'), value: TagType.NoTag },
    ...(tagList?.data?.map((tag: PlayerTag) => ({
      label: tag.name,
      value: tag.id
    })) || [])
  ];

  const { mutate: editChannel } = useMutation({
    mutationFn: editTopupChannel,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['topupChannel'] });
      handleCancel();
    }
  });

  const { mutate: uploadPhoto } = useMutation({
    mutationFn: editTopupChannelPhoto,
    onSuccess: (response) => {
      if (response?.data?.zhTwPhoto) {
        form.setFieldValue('zhTwPhoto', response.data.zhTwPhoto);
      }
    }
  });

  const { mutate: editTopupChannelOrderMutation } = useMutation({
    mutationFn: editTopupChannelOrder,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['topupChannel', categoryKey] });
    }
  });

  const columns = [
    {
      title: 'pages_transaction_topupsetting_Channel_name',
      dataIndex: 'name',
      render: (_: string, record: TopupChannel) => (
        <ChannelName record={record} defaultFrontendLanguage={defaultFrontendLanguage || 'zh_TW'} />
      )
    },
    {
      title: 'pages_transaction_topupsetting_Channel_photo',
      dataIndex: 'photo',
      key: 'photo',
      render: (_: string, record: TopupChannel) => (
        <ChannelPhoto
          record={record}
          defaultFrontendLanguage={defaultFrontendLanguage || 'zh_TW'}
        />
      )
    },
    {
      title: 'pages_transaction_topupsetting_Channel_tags',
      dataIndex: 'tags',
      render: (tags: { id: number; name: string }[]) => {
        if (!tags || tags.length === 0) {
          return <RTag color="default">{t('pages_playerTag_Tag')}</RTag>;
        }
        return (
          <div className="flex flex-wrap gap-1">
            {tags.map((tag, index) => (
              <RTag key={tag.id} color={tagColors[index % tagColors.length]}>
                {tag.name}
              </RTag>
            ))}
          </div>
        );
      }
    }
  ];

  const toFormValues = useCallback(
    (record: TopupChannel): FormValue => ({
      name: record.contentMapping?.[defaultFrontendLanguage || 'zh_TW']?.title || '',
      zhTwPhoto:
        typeof record.imgUrlMapping === 'string'
          ? record.imgUrlMapping
          : record.imgUrlMapping?.[defaultFrontendLanguage || 'zh_TW']?.photo || '',
      tags: record.tags.map((tag) => tag.id) || []
    }),
    [defaultFrontendLanguage]
  );

  useEffect(() => {
    if (editingId !== null) {
      const record = channels.find((item) => item.id === editingId);
      if (record) {
        const formValues = toFormValues(record);
        form.setFieldsValue(formValues);
      }
    }
  }, [editingId, channels, form, toFormValues]);

  const handleEdit = (record: TopupChannel) => {
    setEditingId(record.id);
    const formValues = toFormValues(record);
    form.setFieldsValue(formValues);
  };

  const handleCancel = () => {
    setEditingId(null);
    form.resetFields();
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (editingId !== null) {
        let tags = values.tags;
        // 如果選擇了全部或無標籤，忽略其他選項
        if (tags.includes(TagType.All)) {
          // all
          tags = tagList?.data?.map((tag) => tag.id) || [];
        } else if (tags.includes(TagType.NoTag)) {
          // notag
          tags = [];
        } else {
          // 過濾掉特殊選項值
          tags = tags.filter((tag) => tag !== TagType.All && tag !== TagType.NoTag);
        }

        editChannel({
          id: editingId,
          zhTwTitle: values.name,
          zhTwPhoto: values.zhTwPhoto,
          tags
        });
      }
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const getDisplayData = (): (TopupChannel | SpecialRow)[] => {
    if (editingId !== null) {
      return dataSource.map((item) => (item.id === editingId ? { ...item, id: '__edit__' } : item));
    }
    return [...dataSource];
  };

  const columnsWithSpecialRender = columns.map((col) => {
    const originRender = col.render;
    return {
      ...col,
      render: (value: unknown, record: TopupChannel | SpecialRow): React.ReactNode => {
        if (record.id === '__edit__') {
          const editingRecord = channels.find((item) => item.id === editingId);
          return (
            <RForm<FormValue>
              form={form}
              layout="vertical"
              initialValues={editingRecord ? toFormValues(editingRecord) : undefined}
            >
              {col.dataIndex === 'name' && (
                <RForm.Item
                  name="name"
                  rules={[{ required: true, message: t('placeholder_input') }]}
                >
                  <RInput
                    placeholder={t('pages_transaction_topupsetting_Channel_name')}
                    className="!w-60"
                  />
                </RForm.Item>
              )}
              {col.dataIndex === 'photo' && (
                <RForm.Item name="zhTwPhoto">
                  <RUploader accept={accept} beforeUpload={handlePhotoUpload} />
                </RForm.Item>
              )}
              {col.dataIndex === 'tags' && (
                <RForm.Item name="tags">
                  <RSelect
                    options={tagListOptions}
                    mode="multiple"
                    className="!w-80"
                    onChange={(values: number[]) => {
                      // 如果選擇了特殊選項，只保留該選項
                      if (values.includes(TagType.All)) {
                        form.setFieldValue('tags', [TagType.All]);
                      } else if (values.includes(TagType.NoTag)) {
                        form.setFieldValue('tags', [TagType.NoTag]);
                      } else {
                        const normalTags = values.filter(
                          (value: number) => value !== TagType.All && value !== TagType.NoTag
                        );
                        // 如果所有普通標籤都被選取，就等於選取全部
                        if (normalTags.length === (tagList?.data?.length || 0)) {
                          form.setFieldValue('tags', [TagType.All]);
                        } else {
                          form.setFieldValue('tags', normalTags);
                        }
                      }
                    }}
                  />
                </RForm.Item>
              )}
            </RForm>
          );
        }
        return originRender
          ? (originRender(value as never, record as TopupChannel) as React.ReactNode)
          : null;
      }
    };
  });

  const { accept } = useImageUpload({
    fileType: 'jpg,jpeg,png,webp',
    maxSize: 1
  });

  const actionColumn = {
    title: t('common_action'),
    dataIndex: 'action',
    render: (_: string, record: TopupChannel | SpecialRow) => {
      if (record.id === '__edit__') {
        return (
          <ActionButtons
            data={{ ...record, status: undefined }}
            onAdd={handleSubmit}
            onCancel={handleCancel}
            buttons={['add', 'cancel']}
          />
        );
      }
      return (
        <ActionButtons
          data={{ ...record, status: undefined }}
          onEdit={() => handleEdit(record as TopupChannel)}
          buttons={['edit']}
        />
      );
    }
  };

  const tableColumns = [...columnsWithSpecialRender, actionColumn].map((column) => ({
    ...column,
    title: typeof column.title === 'string' ? t(column.title) : column.title
  }));

  const handleSortChange = (data: (TopupChannel | SpecialRow)[]) => {
    // 編輯欄不排序
    const filtered = data.filter((item) => item.id !== '__edit__') as TopupChannel[];
    setDataSource(filtered);
    editTopupChannelOrderMutation({ orders: filtered.map((item) => item.id) });
  };

  const handlePhotoUpload = async (file: File) => {
    if (editingId !== null) {
      uploadPhoto({ id: editingId, zhTwPhoto: file });
    }
    return false; // 阻止自動上傳
  };

  return (
    <RModal
      open={open}
      onCancel={onClose}
      title={t('common_edit_name', { name: t('pages_transaction_topupsetting_Channel') })}
      footer={null}
      width={1280}
    >
      <p className="mb-3 text-warning">{t('pages_transaction_topupsetting_Channel_info')}</p>
      <DragTable
        columns={tableColumns}
        dataSource={getDisplayData()}
        onSortChange={handleSortChange}
        keyName="id"
        pagination={false}
      />
      <div className="m-6 mt-15 w-30 mx-auto">
        <RButton type="default" className="!w-30" onClick={onClose}>
          {t('common_close')}
        </RButton>
      </div>
    </RModal>
  );
};

export default EditChannelModal;
