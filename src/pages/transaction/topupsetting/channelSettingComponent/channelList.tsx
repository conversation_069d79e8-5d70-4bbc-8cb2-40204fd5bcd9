import { PlusCircleOutlined } from '@ant-design/icons';
import { useMutation, useQuery } from '@tanstack/react-query';
import { Spin } from 'antd';

import {
  createTopupItem,
  editTopupItem,
  editTopupItemOrder,
  getTopupChannel,
  getTopupItem
} from '@/api/topup';
import EditIcon from '@/assets/img/icon/operation-edit.svg?react';
import { ActionButtons } from '@/components/ActionButtons';
import DragTable from '@/components/DragTable';
import { RButton } from '@/components/RButton';
import RForm from '@/components/RForm';
import RInputNumber from '@/components/RInputNumber';
import RSwitch from '@/components/RSwitch';
import RTag from '@/components/RTag';
import { tagColors } from '@/components/RTag/types';
import StatusLabel from '@/components/StatusLabel';
import type { TopupItem as TopupItemType } from '@/types/topup';
import { numberFormat } from '@/utils/numbers';

import EditChannelInfo from './editChannelInfo';
import EditChannel from './EditChannelModal';

type ChanelListProps = {
  categoryKey: string;
  defaultFrontendLanguage: string;
};

type SpecialRow = { id: string };

interface ItemFormRow {
  amount: number | undefined;
  buyPrice: number | undefined;
  status: 1 | 0;
}
type ItemFormValues = Record<string, ItemFormRow>;

type TopupItem = TopupItemType & { status?: 1 | 0 };

const ChanelList = ({ categoryKey, defaultFrontendLanguage }: ChanelListProps) => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const [selectedChannel, setSelectedChannel] = useState<string | null>(null);
  const [editChannelInfoOpen, setEditChannelInfoOpen] = useState(false);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [isAdding, setIsAdding] = useState(false);
  const [form] = RForm.useForm<ItemFormValues>();
  const [formInitialValues, setFormInitialValues] = useState<ItemFormValues>({});

  const {
    data: channelsData,
    isLoading: isChannelsLoading,
    refetch: refetchChannels
  } = useQuery({
    queryKey: ['topupChannel', categoryKey],
    queryFn: () => getTopupChannel({ categoryKey }),
    enabled: !!categoryKey
  });

  const channels = useMemo(() => channelsData?.data || [], [channelsData?.data]);

  useEffect(() => {
    if (channels.length > 0 && !selectedChannel) {
      setSelectedChannel(channels[0].key);
    }
  }, [channels, selectedChannel]);

  const {
    data: itemsData,
    refetch: refetchItems,
    isLoading: isItemsLoading
  } = useQuery({
    queryKey: ['topupItem', selectedChannel],
    queryFn: () => getTopupItem({ channelKey: selectedChannel || '' }),
    enabled: !!selectedChannel
  });

  const items = useMemo(() => itemsData?.data || [], [itemsData?.data]);
  const [dataSource, setDataSource] = useState<(TopupItem | SpecialRow)[]>(items);

  useEffect(() => {
    setDataSource(items);
  }, [items]);

  useEffect(() => {
    if (editingId !== null) {
      const record = dataSource.find(
        (item): item is TopupItem =>
          'id' in item && item.id === editingId && typeof item.id === 'number'
      );
      if (record && 'amount' in record && 'buyPrice' in record) {
        form.setFieldsValue({
          [record.id]: {
            amount: record.amount,
            buyPrice: record.buyPrice,
            status: (record as TopupItem).status ?? 1
          }
        });
      }
    }
  }, [editingId, dataSource, form]);

  const createMutation = useMutation({
    mutationFn: (data: {
      categoryKey: string;
      channelKey: string;
      amount: number;
      buyPrice: number;
      status: 0 | 1;
    }) => createTopupItem(data),
    onSuccess: () => {
      handleCancel();
      refetchItems();
      refetchChannels();
    }
  });

  const editMutation = useMutation({
    mutationFn: (data: { id: number; amount: number; buyPrice: number; status: 0 | 1 }) =>
      editTopupItem(data),
    onSuccess: () => {
      handleCancel();
      refetchItems();
      refetchChannels();
    }
  });

  const orderMutation = useMutation({
    mutationFn: (data: { orders: number[] }) => editTopupItemOrder(data),
    onSuccess: () => {
      refetchItems();
    }
  });

  const handleAdd = () => {
    setIsAdding(true);
    setEditingId(null);
    form.resetFields();
    setFormInitialValues({ __add__: { amount: undefined, buyPrice: undefined, status: 1 } });
  };

  const handleEdit = (record: TopupItem) => {
    setEditingId(record.id);
    setIsAdding(false);
    form.resetFields();
    setFormInitialValues({
      [record.id]: {
        amount: record.amount,
        buyPrice: record.buyPrice,
        status: (record as TopupItem).status ?? 1
      }
    });
  };

  const handleCancel = () => {
    setIsAdding(false);
    setEditingId(null);
    form.resetFields();
  };

  const handleSubmit = async () => {
    try {
      let values;
      if (isAdding) {
        values = await form.validateFields([
          ['__add__', 'amount'],
          ['__add__', 'buyPrice'],
          ['__add__', 'status']
        ]);
        const v = values['__add__'];
        if (selectedChannel && v.amount && v.buyPrice) {
          createMutation.mutate({
            categoryKey,
            channelKey: selectedChannel,
            amount: v.amount as number,
            buyPrice: v.buyPrice as number,
            status: v.status
          });
        }
      } else if (editingId !== null) {
        values = await form.validateFields([
          [editingId, 'amount'],
          [editingId, 'buyPrice'],
          [editingId, 'status']
        ]);
        const v = values[editingId];
        if (v.amount && v.buyPrice) {
          editMutation.mutate({
            id: editingId,
            amount: v.amount as number,
            buyPrice: v.buyPrice as number,
            status: v.status
          });
        }
      }
    } catch {
      // 驗證失敗
    }
  };

  const handleChangeOrder = async (data: (TopupItem | SpecialRow)[]) => {
    const filteredData = data.filter((item) => item.id !== '__plus__' && item.id !== '__add__');
    setDataSource(filteredData);
    const orders = filteredData
      .filter((item): item is TopupItem => 'id' in item && typeof item.id === 'number')
      .map((item) => (item as TopupItem).id);
    orderMutation.mutate({ orders });
  };

  const getDisplayData = () => {
    const rows: (TopupItem | SpecialRow)[] = [{ id: '__plus__' }];
    if (isAdding) rows.push({ id: '__add__' });
    return [...rows, ...dataSource];
  };

  const columns = [
    {
      title: t('pages_transaction_topupsetting_Channel_topup_buyprice'),
      dataIndex: 'buyPrice',
      render: (value: number, record: TopupItem | SpecialRow) => {
        if (record.id === '__plus__') {
          return (
            <PlusCircleOutlined
              className="cursor-pointer text-xl text-primary"
              onClick={handleAdd}
            />
          );
        }
        if (record.id === '__add__' || editingId === record.id) {
          return (
            <RForm.Item
              name={[record.id, 'buyPrice']}
              rules={[{ required: true, message: t('placeholder_input') }]}
            >
              <RInputNumber min={1} placeholder={t('placeholder_input')} className="!w-40" />
            </RForm.Item>
          );
        }
        return value ? numberFormat(value) : '-';
      }
    },
    {
      title: t('pages_transaction_topupsetting_Channel_topup_amount'),
      dataIndex: 'amount',
      render: (value: number, record: TopupItem | SpecialRow) => {
        if (record.id === '__plus__') {
          return null;
        }
        if (record.id === '__add__' || editingId === record.id) {
          return (
            <RForm.Item
              name={[record.id, 'amount']}
              rules={[{ required: true, message: t('placeholder_input') }]}
            >
              <RInputNumber min={1} placeholder={t('placeholder_input')} className="!w-40" />
            </RForm.Item>
          );
        }
        return value ? numberFormat(value) : '-';
      }
    },
    {
      title: t('common_status'),
      dataIndex: 'status',
      render: (value: number, record: TopupItem | SpecialRow) => {
        if (record.id === '__plus__') {
          return null;
        }
        if (record.id === '__add__' || editingId === record.id) {
          return (
            <RForm.Item name={[record.id, 'status']} valuePropName="checked">
              <RSwitch
                isBoolean={false}
                defaultCheckValue={1}
                onChange={(checked) => {
                  form.setFieldsValue({ [record.id]: { status: (checked ? 1 : 0) as 0 | 1 } });
                }}
              />
            </RForm.Item>
          );
        }
        return <StatusLabel status={value} />;
      }
    }
  ];

  const actionColumn = {
    title: t('common_action'),
    dataIndex: 'action',
    render: (_: unknown, record: TopupItem | SpecialRow) => {
      if (record.id === '__add__' || editingId === record.id) {
        return (
          <ActionButtons
            data={{ ...record, status: undefined }}
            onAdd={handleSubmit}
            onCancel={handleCancel}
            buttons={['add', 'cancel']}
          />
        );
      }
      if ('id' in record && typeof record.id === 'number') {
        return (
          <ActionButtons
            data={{ status: (record as TopupItem).status }}
            onEdit={() => handleEdit(record as TopupItem)}
            buttons={['edit']}
          />
        );
      }
      return null;
    }
  };

  const tableColumns = [...columns, actionColumn];
  const loading = isChannelsLoading || isItemsLoading;
  return (
    <Spin spinning={loading}>
      <div className="m-6">
        <RButton variant="solid" color="green" className="w-20" onClick={() => setOpen(true)}>
          {t('common_edit_name', { name: t('pages_transaction_topupsetting_Channel') })}
        </RButton>
        <p className="font-medium my-4">{t('pages_transaction_topupsetting_Channel_list')}</p>

        <div className="flex flex-wrap gap-2 mb-4">
          {channels.map((channel) => (
            <RTag
              key={channel.id}
              className={`mb-2 cursor-pointer ${selectedChannel === channel.key ? 'tag-primary' : 'tag-outline-primary'}`}
              onClick={() => setSelectedChannel(channel.key)}
            >
              {channel.contentMapping?.[defaultFrontendLanguage]?.title}
              <span className="text-xs ">({channel.itemsCount})</span>
            </RTag>
          ))}
        </div>

        {selectedChannel && (
          <>
            <div className="p-3 bg-bg-primary rounded-xs">
              <p className=" mb-1.5 text-text-secondary">
                {t('pages_transaction_topupsetting_Channel_tags')}
              </p>
              {channels.find((channel) => channel.key === selectedChannel)?.tags?.length ? (
                channels
                  .find((channel) => channel.key === selectedChannel)
                  ?.tags?.map((tag, idx) => (
                    <RTag key={tag.id} color={tagColors[idx % tagColors.length]}>
                      {tag.name}
                    </RTag>
                  ))
              ) : (
                <RTag color="default">{t('pages_playerTag_Tag')}</RTag>
              )}
              <div>
                <div className="flex items-center gap-2 mt-5 mb-1.5">
                  <p className="  text-text-secondary">
                    {t('pages_transaction_topupsetting_Channel_description')}
                  </p>
                  <EditIcon
                    className="w-4 h-4 fill-text-icon"
                    onClick={() => setEditChannelInfoOpen(true)}
                  />
                </div>
                <div className="text-text-secondary">
                  <div
                    dangerouslySetInnerHTML={{
                      __html:
                        channels.find((channel) => channel.key === selectedChannel)
                          ?.contentMapping?.[defaultFrontendLanguage]?.content || t('common_none')
                    }}
                  />
                </div>
              </div>
            </div>
          </>
        )}
        <div>
          {selectedChannel && (
            <div className="mt-4">
              <RForm<ItemFormValues>
                form={form}
                initialValues={formInitialValues}
                key={isAdding ? '__add__' : editingId || 'view'}
              >
                <DragTable
                  keyName="id"
                  onSortChange={handleChangeOrder}
                  dataSource={getDisplayData()}
                  columns={tableColumns}
                  pagination={false}
                />
              </RForm>
            </div>
          )}
        </div>

        <EditChannel
          open={open}
          onClose={() => setOpen(false)}
          channels={channels}
          defaultFrontendLanguage={defaultFrontendLanguage}
          categoryKey={categoryKey}
        />
        <EditChannelInfo
          open={editChannelInfoOpen}
          onClose={() => setEditChannelInfoOpen(false)}
          channelId={channels.find((channel) => channel.key === selectedChannel)?.id || 0}
          content={
            channels.find((channel) => channel.key === selectedChannel)?.contentMapping?.[
              defaultFrontendLanguage
            ]?.content || ''
          }
        />
      </div>
    </Spin>
  );
};

export default ChanelList;
