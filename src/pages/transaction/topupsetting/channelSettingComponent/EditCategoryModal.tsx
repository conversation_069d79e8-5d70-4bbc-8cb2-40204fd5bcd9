import { useMutation, useQueryClient } from '@tanstack/react-query';

import { editTopupCategory } from '@/api/topup';
import FormModal from '@/components/FormModal';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RUploader from '@/components/RUploader';
import useImageUpload from '@/components/RUploader/useImageUpload';
import { TopupCategory } from '@/types/topup';

type FormValue = {
  name: string;
  zhTwPhoto: File;
};

const EditCategoryModal = ({
  open,
  onClose,
  initialValues,
  defaultFrontendLanguage
}: {
  open: boolean;
  onClose: () => void;
  initialValues: TopupCategory | null;
  defaultFrontendLanguage: string;
}) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  const { mutate: editTopupCategoryMutation, isPending } = useMutation({
    mutationFn: editTopupCategory,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['topupCategory'] });
    }
  });

  const [form] = RForm.useForm<FormValue>();

  useEffect(() => {
    if (initialValues && defaultFrontendLanguage) {
      form.setFieldsValue({
        name: initialValues.contentMapping?.[defaultFrontendLanguage]?.title || ''
      });
    }
  }, [initialValues, defaultFrontendLanguage, form]);

  const handleSubmit = (values: FormValue) => {
    if (initialValues) {
      editTopupCategoryMutation({
        id: initialValues.id,
        zhTwTitle: values.name,
        zhTwPhoto: values.zhTwPhoto
      });
      onClose();
    }
  };

  const { accept, beforeUpload } = useImageUpload({
    fileType: 'jpg,jpeg,png,webp',
    maxSize: 1
  });

  return (
    <FormModal
      form={form}
      open={open}
      onClose={onClose}
      title={t('common_edit_name', { name: t('common_category') })}
      width={360}
      onSubmit={handleSubmit}
      isLoading={isPending}
    >
      <RForm.Item
        name="name"
        label={t('pages_transaction_topupsetting_categoryName')}
        rules={[{ required: true }]}
      >
        <RInput />
      </RForm.Item>
      <div className="flex items-center gap-2">
        <div className="mb-1">{t('common_currentPhoto')}</div>
        <div className="w-10 h-10">
          {initialValues?.imgUrlMapping && (
            <img
              src={initialValues?.imgUrlMapping?.[defaultFrontendLanguage]?.photo}
              alt="category"
            />
          )}
        </div>
      </div>

      <RForm.Item
        name="zhTwPhoto"
        label={t('pages_transaction_topupsetting_categoryImage')}
        rules={[
          {
            required: !initialValues?.imgUrlMapping?.[defaultFrontendLanguage]?.photo,
            message: t('pages_transaction_topupsetting_photo_warning')
          }
        ]}
      >
        <RUploader
          accept={accept}
          beforeUpload={beforeUpload}
          note={t('pages_transaction_topupsetting_image_info')}
          width={200}
        />
      </RForm.Item>
    </FormModal>
  );
};

export default EditCategoryModal;
