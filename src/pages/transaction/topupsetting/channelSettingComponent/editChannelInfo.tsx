import { useMutation, useQueryClient } from '@tanstack/react-query';

import { editTopupChannelInfo } from '@/api/topup';
import FormModal from '@/components/FormModal';
import HTMLEditor from '@/components/HTMLEditor';
import RForm from '@/components/RForm';

type EditChannelInfoProps = {
  open: boolean;
  onClose: () => void;
  channelId: number;
  content: string;
  onSuccess?: () => void;
};

export default function EditChannelInfo({
  open,
  onClose,
  channelId,
  content,
  onSuccess
}: EditChannelInfoProps) {
  const { t } = useTranslation();
  const [form] = RForm.useForm();
  const queryClient = useQueryClient();

  const { mutate: editChannelInfoMutation, isPending } = useMutation({
    mutationFn: editTopupChannelInfo,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['topupChannel'] });
      onSuccess?.();
      onClose();
    }
  });

  const handleSubmit = () => {
    editChannelInfoMutation({
      id: channelId,
      zhTwContent: form.getFieldValue('content')
    });
  };

  return (
    <FormModal
      title={t('pages_transaction_topupsetting_Channel_description')}
      form={form}
      open={open}
      onClose={onClose}
      onSubmit={handleSubmit}
      isLoading={isPending}
      initialValues={{ content }}
    >
      <RForm.Item name="content" rules={[{ required: true }]}>
        <HTMLEditor />
      </RForm.Item>
    </FormModal>
  );
}
