import { useQuery } from '@tanstack/react-query';
import { Spin } from 'antd';

import { getTopupSupplier } from '@/api/topup';
import RTable from '@/components/RTable';

export default function SupplySetting() {
  const { t } = useTranslation();
  const { data, isLoading } = useQuery({
    queryKey: ['topupSupplier'],
    queryFn: getTopupSupplier
  });

  const columns = [
    {
      title: t('pages_transaction_topupsetting_supply_id'),
      dataIndex: 'id',
      key: 'id',
      width: 200
    },
    {
      title: t('pages_transaction_topupsetting_supply_name'),
      dataIndex: 'name',
      key: 'name'
    }
  ];

  return (
    <Spin spinning={isLoading}>
      <div className="p-4">
        <RTable columns={columns} dataSource={data?.data || []} rowKey="id" pagination={false} />
      </div>
    </Spin>
  );
}
