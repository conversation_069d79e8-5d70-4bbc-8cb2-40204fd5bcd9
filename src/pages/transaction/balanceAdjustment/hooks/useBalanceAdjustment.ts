import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { createBalanceAdjustment, getBalanceAdjustmentList } from '@/api/balanceAdjustment';
import { BalanceAdjustmentSearchParams } from '@/types/balanceAdjustment';

// Query keys for balance adjustment related queries
export const balanceAdjustmentKeys = {
  all: ['balanceAdjustment'] as const,
  lists: () => ['balanceAdjustment', 'list'] as const,
  list: (params: BalanceAdjustmentSearchParams) => ['balanceAdjustment', 'list', params] as const
};

export const useBalanceAdjustment = (params: BalanceAdjustmentSearchParams) => {
  return useQuery({
    queryKey: balanceAdjustmentKeys.list(params),
    queryFn: () => getBalanceAdjustmentList(params),
    select: (data) => data.data
  });
};

export const useBalanceAdjustmentMutation = (mutationOptions?: {
  onSuccess?: () => void;
  onError?: (error: any) => void;
}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createBalanceAdjustment,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: balanceAdjustmentKeys.lists() });
      mutationOptions?.onSuccess?.();
    },
    onError: mutationOptions?.onError
  });
};
