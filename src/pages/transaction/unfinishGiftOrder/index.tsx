import dayjs from 'dayjs';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import OperatorCell from '@/components/cells/OperatorCell';
import QuickDateSelect from '@/components/QuickDateSelect';
import RButton from '@/components/RButton';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RSelect from '@/components/RSelect';
import RTable from '@/components/RTable';
import RTag from '@/components/RTag';
import SearchForm from '@/components/SearchForm';
import { useGiftStatus } from '@/hooks/useGiftStatus';
import usePagination from '@/hooks/usePagination';
import TableSearchLayout from '@/layout/TableSearchLayout';
import { GiftOrderSearchParams, GiftStatusFilter, IncompleteGiftOrder } from '@/types/gift';
import { cleanSearchParams } from '@/utils/object';
import { formatTime } from '@/utils/time';

import { useIncompleteGiftOrders } from './hooks/useIncompleteGiftOrders';
import WithdrawGiftOrderModal from './WithdrawGiftOrderModal';

const SEARCH_FIELD_OPTIONS = [
  { label: 'pages_transaction_unfinishGiftOrder_giftAccount', value: 'playerAccount' },
  {
    label: 'pages_transaction_unfinishGiftOrder_targetAccount_nickname',
    value: 'targetAccount'
  },
  { label: 'pages_transaction_topupOrder_giftId', value: 'giftId' }
] as const;

const defaultToday = [dayjs().startOf('day').valueOf(), dayjs().endOf('day').valueOf()] as [
  number,
  number
];

type SearchFormValues = {
  conditionType?: (typeof SEARCH_FIELD_OPTIONS)[number]['value'];
  conditionValue?: string;
  start: number;
  end: number;
  playerAccount?: string;
  targetAccount?: string;
  giftId?: string;
  status?: string;
  excludeTest: 0 | 1;
};

const Amount = ({ amount }: { amount: number }) => {
  const { t } = useTranslation();
  if (amount === 1) return t('common_first');
  return <span>{amount.toLocaleString()}</span>;
};

const getTableColumns = (
  t: (key: string) => string,
  onWithdraw: (record: IncompleteGiftOrder) => void
) => [
  {
    title: t('pages_transaction_unfinishGiftOrder_orderTime'),
    dataIndex: 'createdAt',
    render: (createdAt: number, record: IncompleteGiftOrder) => (
      <OperatorCell record={{ updatedBy: record.id, updatedAt: createdAt }} />
    )
  },
  {
    title: t('pages_transaction_unfinishGiftOrder_giftAccount'),
    dataIndex: 'playerAccount'
  },
  {
    title: t('pages_transaction_unfinishGiftOrder_giftAccount_nickname'),
    dataIndex: 'playerName'
  },
  {
    title: t('pages_transaction_unfinishGiftOrder_targetAccount'),
    dataIndex: 'targetAccount'
  },
  {
    title: t('pages_transaction_unfinishGiftOrder_targetAccount_nickname'),
    dataIndex: 'targetName'
  },
  {
    title: t('pages_transaction_unfinishGiftOrder_giftAmount'),
    dataIndex: 'totalTimes',
    render: (amount: number) => <Amount amount={amount} />
  },
  {
    title: t('pages_transaction_unfinishGiftOrder_giftAmount_perDay'),
    dataIndex: 'todayTimes',
    render: (amount: number) => <Amount amount={amount} />
  },
  {
    title: t('pages_transaction_unfinishGiftOrder_gift'),
    dataIndex: 'item'
  },
  {
    title: t('common_status'),
    dataIndex: 'status',
    render: (status: string) => {
      return <RTag>{status}</RTag>;
    }
  },
  {
    title: t('pages_transaction_unfinishGiftOrder_updateTime'),
    dataIndex: 'updatedAt',
    render: (updatedAt: number) => formatTime(updatedAt)
  },
  {
    title: t('common_action'),
    key: 'action',
    render: (_: any, record: IncompleteGiftOrder) => (
      <div className="flex gap-2">
        <RButton
          size="small"
          variant="outlined"
          color="red"
          type="link"
          onClick={() => onWithdraw(record)}
        >
          {t('pages_transaction_unfinishGiftOrder_withdraw')}
        </RButton>
      </div>
    )
  }
];

const SearchFormWrap = ({
  onSearch,
  onReset
}: {
  onSearch: (values: Omit<GiftOrderSearchParams, 'page' | 'limit'>) => void;
  onReset: () => void;
}) => {
  const { t } = useTranslation();
  const { data: giftStatusData } = useGiftStatus({ filter: GiftStatusFilter.INCOMPLETE });

  const statusOptions = useMemo(() => {
    const allOption = { label: t('common_all'), value: 'all' };
    const apiOptions =
      giftStatusData?.map((status) => ({
        label: status.label,
        value: status.id
      })) || [];
    return [allOption, ...apiOptions];
  }, [giftStatusData, t]);

  const searchFieldOptions = useMemo(() => {
    return SEARCH_FIELD_OPTIONS.map((option) => ({
      label: t(option.label),
      value: option.value
    }));
  }, [t]);

  const handleSearch = (values: SearchFormValues) => {
    const { conditionType, conditionValue, start, end, ...rest } = values;
    const searchValues = {
      ...cleanSearchParams(rest),
      timeStart: start,
      timeEnd: end
    };

    if (conditionType && conditionValue) {
      onSearch({
        ...searchValues,
        [conditionType]: conditionValue
      });
    } else {
      onSearch(searchValues);
    }
  };

  return (
    <SearchForm<SearchFormValues> onSearch={handleSearch} onReset={onReset} className="">
      <RForm.Item name="date" label={t('common_timeSelect')} initialValue={defaultToday}>
        <QuickDateSelect defaultActiveKey="today" />
      </RForm.Item>
      <RForm.Item name="conditionType" label={t('common_condition')}>
        <RSelect
          placeholder={t('common_please_select', { name: t('common_condition') })}
          options={searchFieldOptions}
        />
      </RForm.Item>
      <RForm.Item name="conditionValue" label="">
        <RInput className="max-h-[32px]" placeholder={t('placeholder_input')} />
      </RForm.Item>
      <RForm.Item name="status" label={t('common_status')}>
        <RSelect
          placeholder={t('common_please_select', { name: t('common_status') })}
          options={statusOptions}
        />
      </RForm.Item>
      <RForm.Item name="excludeTest" label={t('common_filtertest')}>
        <RSelect
          placeholder={t('common_please_select', { name: t('common_filtertest') })}
          options={[
            { label: t('common_yes'), value: 1 },
            { label: t('common_no'), value: 0 }
          ]}
        />
      </RForm.Item>
    </SearchForm>
  );
};

const UnfinishGiftOrderPage = () => {
  const { t } = useTranslation();
  const { page, setPage, limit, setLimit } = usePagination({});
  const [params, setParams] = useState<Omit<GiftOrderSearchParams, 'page' | 'limit'>>({
    timeStart: defaultToday[0],
    timeEnd: defaultToday[1]
  });
  const [selectedGiftOrder, setSelectedGiftOrder] = useState<IncompleteGiftOrder | null>(null);

  const handleWithdraw = (record: IncompleteGiftOrder) => {
    setSelectedGiftOrder(record);
  };

  const handleCloseWithdrawModal = () => {
    setSelectedGiftOrder(null);
  };

  const tableColumns = useMemo(() => getTableColumns(t, handleWithdraw), [t]);

  const { data, isPending } = useIncompleteGiftOrders({
    page,
    limit,
    ...params
  });

  const handleChangePage = (page: number, pageSize: number) => {
    setPage(page);
    setLimit(pageSize);
  };

  const handleSearch = (newParams: Omit<GiftOrderSearchParams, 'page' | 'limit'>) => {
    setParams(newParams);
    setPage(1);
  };

  const handleReset = () => {
    const resetParams = {
      timeStart: defaultToday[0],
      timeEnd: defaultToday[1]
    };
    setParams(resetParams);
    setPage(1);
  };

  return (
    <>
      <TableSearchLayout
        searchFields={<SearchFormWrap onSearch={handleSearch} onReset={handleReset} />}
      >
        <RTable
          loading={isPending}
          rowKey="id"
          dataSource={data?.data || []}
          columns={tableColumns}
          pagination={{
            current: page,
            pageSize: limit,
            total: data?.total || 0,
            showSizeChanger: true,
            onChange: handleChangePage
          }}
        />
      </TableSearchLayout>

      {selectedGiftOrder && (
        <WithdrawGiftOrderModal
          open={!!selectedGiftOrder}
          onClose={handleCloseWithdrawModal}
          giftOrder={selectedGiftOrder}
        />
      )}
    </>
  );
};

export default UnfinishGiftOrderPage;
