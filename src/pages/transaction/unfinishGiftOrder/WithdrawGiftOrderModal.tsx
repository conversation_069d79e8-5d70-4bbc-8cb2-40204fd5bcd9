import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';

import { withdrawGiftOrder } from '@/api/gift';
import InfoIcon from '@/assets/img/icon/info.svg?react';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RModal from '@/components/RModal';
import { IncompleteGiftOrder } from '@/types/gift';

import { giftOrderKeys } from './hooks/useIncompleteGiftOrders';

interface WithdrawGiftOrderModalProps {
  open: boolean;
  onClose: () => void;
  giftOrder: IncompleteGiftOrder | null;
}

type FormValues = {
  note: string;
};

const WithdrawGiftOrderModal = ({ open, onClose, giftOrder }: WithdrawGiftOrderModalProps) => {
  const { t } = useTranslation();
  const [form] = RForm.useForm<FormValues>();
  const queryClient = useQueryClient();

  const { mutateAsync: withdrawMutate, isPending } = useMutation({
    mutationFn: withdrawGiftOrder,
    onSuccess: () => {
      // Invalidate gift order queries to refresh the data
      queryClient.invalidateQueries({ queryKey: giftOrderKeys.incomplete.all });
      handleClose();
    }
  });

  const handleClose = () => {
    form.resetFields();
    onClose();
  };

  const handleSubmit = async (values: FormValues) => {
    if (!giftOrder) return;

    await withdrawMutate({
      id: giftOrder.id,
      note: {
        zh_tw: values.note
      }
    });
  };

  return (
    <RModal
      centered
      maskClosable={false}
      title={t('pages_transaction_unfinishGiftOrder_withdraw_modal_title')}
      open={open}
      onCancel={handleClose}
      onOk={() => form.submit()}
      loading={isPending}
      okButtonProps={{ 
        text: t('common_confirm'),
        show: true 
      }}
      cancelButtonProps={{ 
        text: t('common_cancel'),
        show: true 
      }}
      destroyOnClose={true}
      forceRender={true}
      width={350}
    >
      <div className="flex flex-col items-center">
        <div className="flex items-center gap-2 mb-4">
          <InfoIcon width={48} height={48} className="fill-warning" />
        </div>
        <div className="text-center mb-6">
          {t('pages_transaction_unfinishGiftOrder_withdraw_modal_description')}
        </div>
      </div>

      <RForm
          form={form}
          onFinish={handleSubmit}
          layout="vertical"
          preserve={false}
        >
          <RForm.Item
            name="note"
            label={t('pages_transaction_unfinishGiftOrder_withdraw_modal_note_label')}
            rules={[
              {
                required: true,
                message: t('common_please_enter', {
                  name: t('pages_transaction_unfinishGiftOrder_withdraw_modal_note_label')
                })
              }
            ]}
          >
            <RInput
              placeholder={t('pages_transaction_unfinishGiftOrder_withdraw_modal_note_placeholder')}
            />
          </RForm.Item>
        </RForm>
    </RModal>
  );
};

export default WithdrawGiftOrderModal;
