import { useQuery } from '@tanstack/react-query';

import { getIncompleteGiftOrders } from '@/api/gift';
import { GiftOrderSearchParams } from '@/types/gift';

// Query keys for gift order related queries
export const giftOrderKeys = {
  all: ['giftOrder'] as const,
  incomplete: {
    all: ['giftOrder', 'incomplete'] as const,
    lists: () => ['giftOrder', 'incomplete', 'list'] as const,
    list: (params: GiftOrderSearchParams) => ['giftOrder', 'incomplete', 'list', params] as const
  }
};

export const useIncompleteGiftOrders = (params: GiftOrderSearchParams) => {
  return useQuery({
    queryKey: giftOrderKeys.incomplete.list(params),
    queryFn: () => getIncompleteGiftOrders(params),
    select: (data) => data.data
  });
};
