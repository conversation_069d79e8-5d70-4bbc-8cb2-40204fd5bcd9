import { useMutation, useQueryClient } from '@tanstack/react-query';

import { withdrawGiftOrder } from '@/api/gift';

import { giftOrderKeys } from './useIncompleteGiftOrders';

export const useWithdrawGiftOrder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: withdrawGiftOrder,
    onSuccess: () => {
      // Invalidate all incomplete gift order queries to refresh the data
      queryClient.invalidateQueries({ queryKey: giftOrderKeys.incomplete.all });
    }
  });
};
