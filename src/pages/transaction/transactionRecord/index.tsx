import { useMutation, useQuery } from '@tanstack/react-query';
import dayjs from 'dayjs';

import { getTransactionRecord, getTransactionRecordExport } from '@/api/transaction';
import ExportRecord from '@/components/ExportRecord';
import QuickDateSelect from '@/components/QuickDateSelect';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RMultipleSelect from '@/components/RMultipleSelect';
import RSelect from '@/components/RSelect';
import RTable from '@/components/RTable';
import SearchForm from '@/components/SearchForm';
import usePagination from '@/hooks/usePagination';
import TableSearchLayout from '@/layout/TableSearchLayout';
import { numberFormat } from '@/utils/numbers';
import { formatTime } from '@/utils/time';

type SearchFormValues = {
  account?: string;
  name?: string;
  id?: string;
  type?: string;
  currency?: string;
  accountType?: number;
  start?: number;
  end?: number;
};

type TempSearchFormValues = {
  accountType?: 1 | 0; // 1: 非測試帳號, 0: all
} & {
  conditionType: string;
  conditionValue: string;
};

const CONDITION_OPTIONS = [
  { label: 'pages_player_account', value: 'account' },
  { label: 'pages_player_nicknameSearch', value: 'name' },
  { label: 'common_id', value: 'id' }
] as const;

const TYPE_OPTIONS = [
  { label: 'pages_transaction_transactionRecord_type_topup', value: 'topup' },
  { label: 'pages_transaction_transactionRecord_type_sendGift', value: 'send_gift' },
  { label: 'pages_transaction_transactionRecord_type_receiveGift', value: 'receive_gift' },
  { label: 'pages_transaction_transactionRecord_type_sendFee', value: 'send_fee' },
  { label: 'pages_transaction_transactionRecord_type_giftReturn', value: 'gift_return' },
  { label: 'pages_transaction_transactionRecord_type_bet', value: 'bet' },
  { label: 'pages_transaction_transactionRecord_type_prize', value: 'prize' }
] as const;

const CURRENCY_OPTIONS = [
  { label: 'pages_transaction_transactionRecord_currency_main', value: 'main' },
  { label: 'common_else', value: 'secondary' }
] as const;

const defaultToday = [dayjs().startOf('day').valueOf(), dayjs().endOf('day').valueOf()];

const getTableColumns = (t: (key: string) => string) => [
  {
    title: t('common_id'),
    dataIndex: 'id'
  },
  {
    title: t('common_account'),
    dataIndex: 'account'
  },
  {
    title: t('pages_player_nickname'),
    dataIndex: 'name'
  },
  {
    title: t('pages_transaction_transactionRecord_currency'),
    dataIndex: 'currency',
    render: (currency: string) => {
      const option = CURRENCY_OPTIONS.find((option) => option.value === currency);
      return option ? t(option.label) : currency;
    }
  },
  {
    title: t('pages_transaction_transactionRecord_type'),
    dataIndex: 'type',
    render: (type: string) => {
      const option = TYPE_OPTIONS.find((option) => option.value === type);
      return option ? t(option.label) : type;
    }
  },
  {
    title: t('pages_transaction_transactionRecord_amount'),
    dataIndex: 'amount',
    render: (amount: number) => numberFormat(amount)
  },
  {
    title: t('pages_transaction_transactionRecord_remain'),
    dataIndex: 'remain',
    render: (remain: number) => numberFormat(remain)
  },
  {
    title: t('common_note'),
    dataIndex: 'note'
  },
  {
    title: t('pages_transaction_transactionRecord_time'),
    dataIndex: 'createdAt',
    render: (createdAt: number) => formatTime(createdAt)
  }
];

const ConditionSearch = () => {
  const { t } = useTranslation();

  const translatedOptions = useMemo(
    () =>
      CONDITION_OPTIONS.map((option) => ({
        label: t(option.label),
        value: option.value
      })),
    [t]
  );

  return (
    <RForm.Item label={t('common_condition')}>
      <RForm.Item name="conditionType" className="inline-block !mr-2" initialValue="account">
        <RSelect options={translatedOptions} />
      </RForm.Item>
      <RForm.Item name="conditionValue" className="inline-block">
        <RInput className="max-h-[32px]" />
      </RForm.Item>
    </RForm.Item>
  );
};

const SearchFormWrap = ({
  onSearch,
  onReset
}: {
  onSearch: (values: SearchFormValues) => void;
  onReset: () => void;
}) => {
  const { t } = useTranslation();

  const handleSearch = (values: TempSearchFormValues) => {
    const { conditionType, conditionValue, ...rest } = values;
    if (conditionType && conditionValue) {
      const searchValues = {
        ...rest,
        [conditionType]: conditionValue
      };
      onSearch(searchValues);
    } else {
      onSearch(rest);
    }
  };

  const translatedTypeOptions = useMemo(
    () =>
      TYPE_OPTIONS.map((option) => ({
        label: t(option.label),
        value: option.value
      })),
    [t]
  );

  const translatedCurrencyOptions = useMemo(
    () =>
      CURRENCY_OPTIONS.map((option) => ({
        label: t(option.label),
        value: option.value
      })),
    [t]
  );

  return (
    <SearchForm<TempSearchFormValues> onSearch={handleSearch} onReset={onReset} className="">
      <RForm.Item name="date" label={t('pages_admin_addTimeFilter')}>
        <QuickDateSelect defaultActiveKey="today" />
      </RForm.Item>
      <ConditionSearch />
      <RForm.Item name="type" label={t('pages_transaction_transactionRecord_type')}>
        <RMultipleSelect options={translatedTypeOptions} onChange={() => {}} />
      </RForm.Item>
      <RForm.Item name="currency" label={t('common_currency')}>
        <RSelect options={translatedCurrencyOptions} />
      </RForm.Item>
      <RForm.Item name="accountType" label={t('common_filtertest')} initialValue={1}>
        <RSelect
          options={[
            { label: t('common_yes'), value: 1 }, // 非測試帳號
            { label: t('common_no'), value: 0 } // all
          ]}
        />
      </RForm.Item>
    </SearchForm>
  );
};

const TransactionRecordPage = () => {
  const { t } = useTranslation();
  const { page, setPage, limit, setLimit } = usePagination({});
  const [params, setParams] = useState<SearchFormValues>({
    start: defaultToday[0],
    end: defaultToday[1]
  });

  const tableColumns = useMemo(() => getTableColumns(t), [t]);

  const { data, isPending } = useQuery({
    queryKey: ['transactionRecord', { page, limit, ...params }],
    queryFn: () =>
      getTransactionRecord({
        page,
        limit,
        accountType: 1,
        ...params
      })
  });

  const {
    mutate: exportTransactionRecord,
    data: exportData,
    isPending: renderModalContent
  } = useMutation({
    mutationFn: getTransactionRecordExport
  });

  const handleChangePage = (page: number, pageSize: number) => {
    setPage(page);
    setLimit(pageSize);
  };
  const handleSearch = (values: SearchFormValues) => {
    setPage(1);
    setParams({
      ...params,
      ...values
    });
  };

  const handleReset = () => {
    setParams({
      start: defaultToday[0],
      end: defaultToday[1],
      accountType: 1,
      type: undefined,
      currency: undefined,
      account: undefined,
      name: undefined,
      id: undefined
    });
  };

  return (
    <TableSearchLayout
      searchFields={<SearchFormWrap onSearch={handleSearch} onReset={handleReset} />}
    >
      <ExportRecord
        onClick={() => exportTransactionRecord(params)}
        id={exportData?.data?.id}
        isLoading={renderModalContent}
      />
      <RTable
        loading={isPending}
        rowKey="id"
        dataSource={data?.data?.data || []}
        columns={tableColumns}
        pagination={{
          current: page,
          pageSize: limit,
          total: data?.data?.total || 0,
          showSizeChanger: true,
          onChange: handleChangePage
        }}
      />
    </TableSearchLayout>
  );
};

export default TransactionRecordPage;
