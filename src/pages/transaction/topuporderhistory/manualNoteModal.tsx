import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';

import { updateTopupOrderNote } from '@/api/topup';
import InfoIcon from '@/assets/img/icon/info.svg?react';
import { RForm } from '@/components/RForm';
import RInput from '@/components/RInput';
import RModal from '@/components/RModal';

interface ManualNoteModalProps {
  open: boolean;
  onClose: () => void;
  id: number;
}

const ManualNoteModal = ({ open, onClose, id }: ManualNoteModalProps) => {
  const { t } = useTranslation();
  const [form] = RForm.useForm();
  const queryClient = useQueryClient();

  const { mutate: updateNote, isPending } = useMutation({
    mutationFn: updateTopupOrderNote,
    onSuccess: () => {
      form.resetFields();
      onClose();
      queryClient.invalidateQueries({ queryKey: ['incompleteTopupOrder'] });
    }
  });

  const handleOk = () => {
    const values = form.getFieldsValue();
    console.log(id, values);
    updateNote({
      id,
      note: { zhTw: values.note }
    });
  };

  return (
    <RModal
      open={open}
      onClose={onClose}
      onOk={handleOk}
      onCancel={onClose}
      title={t('pages_transaction_topupOrder_manual')}
      width={360}
      loading={isPending}
    >
      <div>
        <div className="flex flex-col items-center gap-2">
          <div className="fill-warning">
            <InfoIcon width={48} height={48} />
          </div>
          <p className="my-4">{t('pages_transaction_topupOrder_manual_content')}</p>
        </div>
        <RForm form={form} layout="vertical">
          <RForm.Item
            label={
              <span className="text-text-secondary">{t('pages_transaction_topupOrder_note')}</span>
            }
            name="note"
          >
            <RInput />
          </RForm.Item>
        </RForm>
      </div>
    </RModal>
  );
};

export default ManualNoteModal;
