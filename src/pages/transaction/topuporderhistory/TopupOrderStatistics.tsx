import { TopupOrderHistoryResponse } from '@/api/topup';
import { numberFormat } from '@/utils/numbers';

type TopupOrderStatisticsProps = {
  data: TopupOrderHistoryResponse['data'] | undefined;
  pageAmount: number;
  pagePoints: number;
};

const TopupOrderStatistics = ({ data, pageAmount, pagePoints }: TopupOrderStatisticsProps) => {
  const { t } = useTranslation();

  const statistics = [
    {
      label: t('pages_transaction_topupOrder_totalPlayer'),
      value: data?.statistic?.accountCount ?? 0
    },
    {
      label: t('pages_transaction_topupOrder_totalOrders'),
      value: data?.statistic?.orderCount ?? 0
    }
  ];

  const summary = [
    {
      label: t('pages_transaction_topupOrder_pagesumup'),
      value: pageAmount
    },
    {
      label: t('pages_transaction_topupOrder_pagesumup_total'),
      value: data?.statistic?.sumTtlAmount ?? 0
    },
    {
      label: t('pages_transaction_topupOrder_pagesumup_points'),
      value: pagePoints
    },
    {
      label: t('pages_transaction_topupOrder_pagesumup_total_points'),
      value: data?.statistic?.sumTtlPoint ?? 0
    }
  ];

  return (
    <div>
      <div className="flex gap-7 justify-end">
        {statistics.map(({ label, value }) => (
          <div key={label}>
            <span className="text-text-secondary mr-1">{label}:</span>
            <span>{numberFormat(value)}</span>
          </div>
        ))}
      </div>
      <div className="flex gap-7 mt-1">
        {summary.map(({ label, value }) => (
          <div key={label}>
            <span className="text-text-secondary mr-1">{label}:</span>
            <span>{numberFormat(value)}</span>
          </div>
        ))}
      </div>
    </div>
  );
};
export default TopupOrderStatistics;
