import { useMutation, useQuery } from '@tanstack/react-query';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';

import {
  getTopupOrderHistory,
  getTopupOrderHistoryExport,
  TopupOrderHistoryResponse
} from '@/api/topup';
import { CategoryAndChannelSelect } from '@/components/CategoryAndChannelSelect';
import ExportRecord from '@/components/ExportRecord';
import QuickDateSelect from '@/components/QuickDateSelect';
import { RButton } from '@/components/RButton';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RSelect from '@/components/RSelect';
import RTable from '@/components/RTable';
import RTag from '@/components/RTag';
import { tagColors } from '@/components/RTag/types';
import SearchForm from '@/components/SearchForm';
import TagSelect from '@/components/TagSelect';
import { TimeSelect } from '@/enums/timeSelect';
import useFrontendLanguage from '@/hooks/useFrontendLanguage';
import usePagination from '@/hooks/usePagination';
import TableSearchLayout from '@/layout/TableSearchLayout';
import {
  useTopupAllChannels,
  useTopupCategory,
  useTopupChannel,
  useTopupSupplier
} from '@/pages/transaction/hook/useTopupQueries';
import { TopupOrderHistory, TopupOrderStatus } from '@/types/topup';
import { numberFormat } from '@/utils/numbers';
import { formatTime } from '@/utils/time';

import ManualNoteModal from './manualNoteModal';
import TopupOrderStatistics from './TopupOrderStatistics';

type SearchFormValues = {
  start?: number;
  end?: number;
  account?: string;
  name?: string;
  orderId?: string;
  refAccount?: string;
  agentAccount?: string;
  platformKey?: string;
  categoryKey?: string;
  channelKey?: string;
  tagId?: number[];
  excludeTest?: 1 | 0;
  timeFilter?: TimeSelect;
};

type TempSearchFormValues = {} & {
  conditionType: string;
  conditionValue: string;
  platformKey: string;
  channelKey: string;
  excludeTest: 1 | 0;
  timeFilter: string;
};

const CONDITION_OPTIONS = [
  { label: 'pages_player_account', value: 'account' },
  { label: 'pages_player_nicknameSearch', value: 'name' },
  { label: 'pages_transaction_topupOrder_orderId', value: 'orderId' },
  { label: 'pages_player_parentAccount', value: 'refAccount' },
  { label: 'pages_transaction_topupOrder_agentAccount', value: 'agentAccount' }
] as const;

const TIME_FILTER_OPTIONS = [
  { label: 'pages_transaction_topupOrder_timeSelect_updatedAt', value: TimeSelect.UPDATE_AT },
  { label: 'pages_transaction_topupOrder_timeSelect_createdAt', value: TimeSelect.CREATE_AT }
] as const;

const defaultToday = [dayjs().startOf('day').valueOf(), dayjs().endOf('day').valueOf()];

const getTableColumns = (
  t: (key: string) => string,
  setShowManualNoteModal: (show: boolean, record: TopupOrderHistory) => void,
  supplierList?: { key: string; name: string }[],
  categoryList?: { key: string; contentMapping: Record<string, { title: string }> }[],
  allChannelList?: { key: string; contentMapping: Record<string, { title: string }> }[],
  defaultFrontendLanguage?: string
): ColumnsType<TopupOrderHistory> => [
  {
    title: `${t('pages_transaction_topupOrder_orderId')} / ${t('pages_transaction_topupOrder_orderTime')}`,
    dataIndex: 'id',
    width: 180,
    render: (id: number, record: TopupOrderHistory) => {
      return (
        <div>
          <div>{id}/</div>
          <div>{formatTime(record.createdAt)}</div>
        </div>
      );
    }
  },
  {
    title: t('common_account'),
    dataIndex: 'account',
    width: 100
  },
  {
    title: t('pages_player_nickname'),
    dataIndex: 'name',
    width: 100
  },
  {
    title: `${t('pages_player_parentAccount')}/ ${t('pages_transaction_topupOrder_agentAccount')}`,
    dataIndex: 'refAccount',
    width: 100,
    render: (refAccount: string, record: TopupOrderHistory) => {
      return (
        <div>
          <div>{refAccount}/</div>
          <div>{record.agentAccount}</div>
        </div>
      );
    }
  },
  {
    title: t('pages_player_playerTag'),
    dataIndex: 'tags',
    width: 150,
    render: (tags: { id: number; name: string }[]) => {
      if (!tags || tags.length === 0) {
        return <RTag color="default">{t('pages_playerTag_Tag')}</RTag>;
      }
      return (
        <div className="flex flex-wrap gap-1">
          {tags.map((tag, index) => (
            <RTag key={tag.id} color={tagColors[index % tagColors.length]}>
              {tag.name}
            </RTag>
          ))}
        </div>
      );
    }
  },
  {
    title: t('pages_transaction_topupOrder_topupCount'),
    dataIndex: 'topupCount',
    width: 100,
    render: (topupCount: number) => {
      return topupCount === 1 ? t('pages_transaction_topupOrder_topupCount_first') : topupCount;
    }
  },
  {
    title: t('common_supplier'),
    dataIndex: 'platformKey',
    width: 100,
    render: (platformKey: string) => {
      const supplier = supplierList?.find((item) => item.name === platformKey);
      return supplier?.name || platformKey;
    }
  },
  {
    title: t('pages_transaction_topupsetting_popular_category'),
    dataIndex: 'categoryKey',
    width: 120,
    render: (categoryKey: string) => {
      const category = categoryList?.find((item) => item.key === categoryKey);
      return category?.contentMapping[defaultFrontendLanguage || '']?.title || categoryKey;
    }
  },
  {
    title: t('pages_transaction_topupsetting_popular_channel'),
    dataIndex: 'channelKey',
    width: 120,
    render: (channelKey: string) => {
      const channel = allChannelList?.find((item) => item.key === channelKey);
      return channel?.contentMapping[defaultFrontendLanguage || '']?.title || channelKey;
    }
  },
  {
    title: t('pages_transaction_topupsetting_Channel_topup_buyprice'),
    dataIndex: 'amount',
    width: 100,
    render: (amount: number) => {
      return numberFormat(amount);
    }
  },
  {
    title: t('pages_transaction_topupOrder_points'),
    dataIndex: 'points',
    width: 150,
    render: (points: number) => {
      return (
        <div>
          <div>
            {numberFormat(points)} {t('pages_transaction_transactionRecord_amount')}
          </div>
        </div>
      );
    }
  },
  {
    title: t('pages_transaction_topupOrder_note'),
    dataIndex: 'note',
    width: 100,
    render: (note: { zhTw: string }) => {
      return note?.zhTw || '-';
    }
  },
  {
    title: `${t('pages_transaction_topupOrder_timeSelect_updatedAt')}/${t('pages_transaction_topupOrder_admin')}`,
    dataIndex: 'updatedAt',
    width: 100,
    render: (updatedAt: number, record: TopupOrderHistory) => {
      return formatTime(updatedAt) + '/' + record.updatedBy;
    }
  },

  {
    title: t('common_status'),
    dataIndex: 'status',
    fixed: 'right' as const,
    width: 100,
    // 此頁面只會有通過及關閉狀態
    render: (status: TopupOrderStatus) => {
      switch (status) {
        case TopupOrderStatus.Success:
          return (
            <RTag color="var(--tag-bg-enabled)" textColor="var(--color-success)">
              {t('common_pass')}
            </RTag>
          );
        case TopupOrderStatus.Closed:
          return <RTag>{t('common_close')}</RTag>;
        case TopupOrderStatus.Pending:
          return <RTag>{t('common_pending')}</RTag>;
      }
    }
  },
  {
    title: t('common_action'),
    dataIndex: 'action',
    fixed: 'right' as const,
    width: 100,
    // 判斷狀態關閉才需顯示
    render: (_, record: TopupOrderHistory) => {
      return record.status === TopupOrderStatus.Closed ? (
        <RButton
          variant="outlined"
          color="orange"
          type="link"
          size="small"
          onClick={() => setShowManualNoteModal(true, record)}
        >
          {t('pages_transaction_topupOrder_manual')}
        </RButton>
      ) : null;
    }
  }
];

const ConditionSearch = () => {
  const { t } = useTranslation();

  const translatedOptions = useMemo(
    () =>
      CONDITION_OPTIONS.map((option) => ({
        label: t(option.label),
        value: option.value
      })),
    [t]
  );

  return (
    <RForm.Item label={t('common_condition')}>
      <RForm.Item name="conditionType" className="inline-block !mr-2" initialValue="account">
        <RSelect options={translatedOptions} />
      </RForm.Item>
      <RForm.Item name="conditionValue" className="inline-block">
        <RInput className="max-h-[32px]" />
      </RForm.Item>
    </RForm.Item>
  );
};

const SearchFormWrap = ({
  onSearch,
  onReset,
  supplierList,
  categoryOptions,
  channelOptions,
  isChannelsLoading,
  onCategoryChange
}: {
  onSearch: (values: SearchFormValues) => void;
  onReset: () => void;
  supplierList?: { key: string; name: string }[];
  categoryOptions: { label: string; value: string }[];
  channelOptions: { label: string; value: string }[];
  isChannelsLoading?: boolean;
  onCategoryChange: (value: string) => void;
}) => {
  const { t } = useTranslation();

  const timeFilterOptions = useMemo(() => {
    return TIME_FILTER_OPTIONS.map((option) => ({
      label: t(option.label),
      value: option.value
    }));
  }, [t]);

  const allOption = useMemo(() => ({ label: t('common_all'), value: 'all' }), [t]);

  const handleSearch = (values: TempSearchFormValues) => {
    const { conditionType, conditionValue, ...rest } = values;
    const processedValues = Object.entries(rest).reduce(
      (acc, [key, value]) => ({
        ...acc,
        [key]: value === 'all' ? undefined : value
      }),
      {}
    );

    if (conditionType && conditionValue) {
      onSearch({
        ...processedValues,
        [conditionType]: conditionValue
      });
    } else {
      onSearch(processedValues);
    }
  };

  const handleReset = () => {
    onReset();
  };

  const supplierListOptions = useMemo(() => {
    return supplierList?.map((item) => ({ label: item.name, value: item.key }));
  }, [supplierList]);
  const supplierOptions = [allOption, ...(supplierListOptions || [])];

  return (
    <SearchForm<TempSearchFormValues>
      onSearch={handleSearch}
      onReset={handleReset}
      className=""
      initialValues={{
        platformKey: 'all',
        channelKey: 'all',
        excludeTest: 1,
        timeFilter: TimeSelect.UPDATE_AT
      }}
    >
      <RForm.Item name="timeFilter" label={t('pages_transaction_topupOrder_timeSelect')}>
        <RSelect options={timeFilterOptions} />
      </RForm.Item>
      <RForm.Item name="date" label={t('pages_admin_addTimeFilter')} initialValue={defaultToday}>
        <QuickDateSelect defaultActiveKey="today" />
      </RForm.Item>
      <ConditionSearch />
      <RForm.Item name="platformKey" label={t('common_supplier')}>
        <RSelect options={supplierOptions} />
      </RForm.Item>
      <CategoryAndChannelSelect
        categoryOptions={categoryOptions}
        channelOptions={channelOptions}
        isChannelsLoading={isChannelsLoading}
        onCategoryChange={onCategoryChange}
      />
      <TagSelect name="tagId" />
      <RForm.Item name="status" label={t('common_status')} initialValue="all">
        <RSelect
          options={[
            { label: t('common_all'), value: 'all' },
            { label: t('common_close'), value: TopupOrderStatus.Closed },
            { label: t('common_pass'), value: TopupOrderStatus.Success }
          ]}
        />
      </RForm.Item>
      <RForm.Item name="excludeTest" label={t('common_filtertest')}>
        <RSelect
          options={[
            { label: t('common_yes'), value: 1 }, // 非測試帳號
            { label: t('common_no'), value: 0 } // all
          ]}
        />
      </RForm.Item>
    </SearchForm>
  );
};

const TopupOrdersHistoryPage = () => {
  const { t } = useTranslation();
  const { page, setPage, limit, setLimit } = usePagination({});
  const [params, setParams] = useState<SearchFormValues>({
    start: defaultToday[0],
    end: defaultToday[1],
    timeFilter: TimeSelect.UPDATE_AT,
    excludeTest: 1
  });
  const [showManualNoteModal, setShowManualNoteModal] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<TopupOrderHistory | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const { defaultFrontendLanguage } = useFrontendLanguage();

  const { data: supplierList } = useTopupSupplier();
  const { data: categoryList } = useTopupCategory();
  const { data: channels, isLoading: isChannelsLoading } = useTopupChannel(selectedCategory);
  const { data: allChannels } = useTopupAllChannels();

  const { data, isPending } = useQuery<TopupOrderHistoryResponse>({
    queryKey: ['incompleteTopupOrder', { page, limit, ...params }],
    queryFn: async () => {
      const response = await getTopupOrderHistory({
        page,
        limit,
        ...params
      });
      return response as unknown as TopupOrderHistoryResponse;
    }
  });

  const pageAmount = useMemo(
    () =>
      data?.data?.data?.reduce(
        (sum: number, item: TopupOrderHistory) => sum + (item.amount ?? 0),
        0
      ) ?? 0,
    [data?.data?.data]
  );

  const pagePoints = useMemo(
    () =>
      data?.data?.data?.reduce(
        (sum: number, item: TopupOrderHistory) => sum + (item.points ?? 0),
        0
      ) ?? 0,
    [data?.data?.data]
  );

  const categoryOptions = useMemo(
    () =>
      categoryList?.data?.map((item) => ({
        label: item.contentMapping[defaultFrontendLanguage]?.title,
        value: item.key
      })) ?? [],
    [categoryList?.data, defaultFrontendLanguage]
  );

  const channelOptions = useMemo(
    () => [
      { label: t('common_all'), value: 'all' },
      ...(channels?.data?.map((channel) => ({
        label: channel.contentMapping[defaultFrontendLanguage]?.title,
        value: channel.key
      })) ?? [])
    ],
    [channels?.data, defaultFrontendLanguage, t]
  );

  const tableColumns = useMemo(
    () =>
      getTableColumns(
        t,
        (show, record) => {
          setShowManualNoteModal(show);
          setSelectedRecord(record);
        },
        supplierList?.data,
        categoryList?.data,
        allChannels?.data,
        defaultFrontendLanguage
      ),
    [t, supplierList?.data, categoryList?.data, allChannels?.data, defaultFrontendLanguage]
  );

  const {
    mutate: exportTopupOrderHistory,
    data: exportData,
    isPending: renderModalContent
  } = useMutation({
    mutationFn: getTopupOrderHistoryExport
  });

  const handleChangePage = (page: number, pageSize: number) => {
    setPage(page);
    setLimit(pageSize);
  };

  const handleSearch = (values: SearchFormValues) => {
    setPage(1);
    setParams((prev) => ({ ...prev, ...values }));
  };

  const handleReset = () => {
    setParams({
      start: defaultToday[0],
      end: defaultToday[1],
      timeFilter: TimeSelect.UPDATE_AT,
      excludeTest: 1
    });
  };

  const handleCloseModal = () => {
    setShowManualNoteModal(false);
    setSelectedRecord(null);
  };

  return (
    <TableSearchLayout
      searchFields={
        <SearchFormWrap
          onSearch={handleSearch}
          onReset={handleReset}
          supplierList={supplierList?.data}
          categoryOptions={categoryOptions}
          channelOptions={channelOptions}
          isChannelsLoading={isChannelsLoading}
          onCategoryChange={setSelectedCategory}
        />
      }
    >
      <div className="flex justify-end gap-5">
        <TopupOrderStatistics data={data?.data} pageAmount={pageAmount} pagePoints={pagePoints} />
        <ExportRecord
          onClick={() => exportTopupOrderHistory(params)}
          id={exportData?.data?.id}
          isLoading={renderModalContent}
        />
      </div>
      <RTable<TopupOrderHistory>
        className="custom-table"
        loading={isPending}
        rowKey="id"
        dataSource={data?.data?.data ?? []}
        columns={tableColumns}
        scroll={{ x: 'max-content' }}
        pagination={{
          current: page,
          pageSize: limit,
          total: data?.data?.total ?? 0,
          showSizeChanger: true,
          onChange: handleChangePage
        }}
      />
      <ManualNoteModal
        open={showManualNoteModal}
        onClose={handleCloseModal}
        id={selectedRecord?.id ?? 0}
      />
    </TableSearchLayout>
  );
};

export default TopupOrdersHistoryPage;
