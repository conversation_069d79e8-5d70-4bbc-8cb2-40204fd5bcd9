import { CategoryAndChannelSelect } from '@/components/CategoryAndChannelSelect';
// import QuickDateSelect from '@/components/QuickDateSelect';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RSelect from '@/components/RSelect';
import RTable from '@/components/RTable';
import RTag from '@/components/RTag';
import { tagColors } from '@/components/RTag/types';
import SearchForm from '@/components/SearchForm';
import TagSelect from '@/components/TagSelect';
import useFrontendLanguage from '@/hooks/useFrontendLanguage';
import usePagination from '@/hooks/usePagination';
import TableSearchLayout from '@/layout/TableSearchLayout';
import {
  useTopupAllChannels,
  useTopupCategory,
  useTopupChannel,
  useTopupIncompleteOrders,
  useTopupSupplier
} from '@/pages/transaction/hook/useTopupQueries';
import { TopupIncompletedOrder, TopupOrderStatus } from '@/types/topup';
import { numberFormat } from '@/utils/numbers';
import { formatTime } from '@/utils/time';

type SearchFormValues = {
  // start?: number;
  // end?: number;
  account?: string;
  name?: string;
  orderId?: string;
  refAccount?: string;
  agentAccount?: string;
  platformKey?: string;
  categoryKey?: string;
  channelKey?: string;
  tagId?: number[];
  excludeTest?: 1 | 0;
};

type TempSearchFormValues = {} & {
  conditionType: string;
  conditionValue: string;
  platformKey: string;
  categoryKey: string;
  channelKey: string;
  excludeTest: 1 | 0;
};

const CONDITION_OPTIONS = [
  { label: 'pages_player_account', value: 'account' },
  { label: 'pages_player_nicknameSearch', value: 'name' },
  { label: 'pages_transaction_topupOrder_orderId', value: 'orderId' },
  { label: 'pages_player_parentAccount', value: 'refAccount' },
  { label: 'pages_transaction_topupOrder_agentAccount', value: 'agentAccount' }
] as const;

const getTableColumns = (
  t: (key: string) => string,
  supplierList?: { key: string; name: string }[],
  categoryList?: { key: string; contentMapping: Record<string, { title: string }> }[],
  allChannelList?: { key: string; contentMapping: Record<string, { title: string }> }[],
  defaultFrontendLanguage?: string
) => [
  {
    title: `${t('pages_transaction_topupOrder_orderId')} / ${t('pages_transaction_topupOrder_orderTime')}`,
    dataIndex: 'id',
    render: (id: number, record: TopupIncompletedOrder) => {
      return (
        <div>
          <div>{id}/</div>
          <div>{formatTime(record.createdAt)}</div>
        </div>
      );
    }
  },
  {
    title: t('common_account'),
    dataIndex: 'account'
  },
  {
    title: t('pages_player_nickname'),
    dataIndex: 'name'
  },
  {
    title: `${t('pages_player_parentAccount')}/ ${t('pages_transaction_topupOrder_agentAccount')}`,
    dataIndex: 'refAccount',
    render: (refAccount: string, record: TopupIncompletedOrder) => {
      return (
        <div>
          <div>{refAccount}/</div>
          <div>{record.agentAccount}</div>
        </div>
      );
    }
  },
  {
    title: t('pages_player_playerTag'),
    dataIndex: 'tags',
    render: (tags: { id: number; name: string }[]) => {
      if (!tags || tags.length === 0) {
        return <RTag color="default">{t('pages_playerTag_Tag')}</RTag>;
      }
      return (
        <div className="flex flex-wrap gap-1">
          {tags.map((tag, index) => (
            <RTag key={tag.id} color={tagColors[index % tagColors.length]}>
              {tag.name}
            </RTag>
          ))}
        </div>
      );
    }
  },
  {
    title: t('pages_transaction_topupOrder_topupCount'),
    dataIndex: 'topupCount',
    render: (topupCount: number) => {
      return topupCount === 1 ? t('pages_transaction_topupOrder_topupCount_first') : topupCount;
    }
  },
  {
    title: t('common_supplier'),
    dataIndex: 'platformKey',
    render: (platformKey: string) => {
      const supplier = supplierList?.find((item) => item.key === platformKey);
      return supplier?.name || platformKey;
    }
  },
  {
    title: t('pages_transaction_topupsetting_popular_category'),
    dataIndex: 'categoryKey',
    render: (categoryKey: string) => {
      const category = categoryList?.find((item) => item.key === categoryKey);
      return category?.contentMapping[defaultFrontendLanguage || '']?.title || categoryKey;
    }
  },
  {
    title: t('pages_transaction_topupsetting_popular_channel'),
    dataIndex: 'channelKey',
    render: (channelKey: string) => {
      const channel = allChannelList?.find((item) => item.key === channelKey);
      return channel?.contentMapping[defaultFrontendLanguage || '']?.title || channelKey;
    }
  },
  {
    title: t('pages_transaction_topupsetting_Channel_topup_buyprice'),
    dataIndex: 'amount',
    render: (amount: number) => {
      return numberFormat(amount);
    }
  },
  {
    title: t('pages_transaction_topupOrder_points'),
    dataIndex: 'points',
    render: (points: number) => {
      return (
        <div>
          <div>
            {numberFormat(points)} {t('pages_transaction_transactionRecord_amount')}
          </div>
        </div>
      );
    }
  },
  {
    title: t('common_status'),
    dataIndex: 'status',
    // 此頁面只會有等待中狀態
    render: (status: TopupOrderStatus) => {
      switch (status) {
        case TopupOrderStatus.Success:
          return <RTag color="green">{t('common_pass')}</RTag>;
        case TopupOrderStatus.Closed:
          return <RTag>{t('common_close')}</RTag>;
        case TopupOrderStatus.Pending:
          return <RTag>{t('common_pending')}</RTag>;
      }
    }
  }
];

const ConditionSearch = () => {
  const { t } = useTranslation();

  const translatedOptions = useMemo(
    () =>
      CONDITION_OPTIONS.map((option) => ({
        label: t(option.label),
        value: option.value
      })),
    [t]
  );

  return (
    <RForm.Item label={t('common_condition')}>
      <RForm.Item name="conditionType" className="inline-block !mr-2" initialValue="account">
        <RSelect options={translatedOptions} />
      </RForm.Item>
      <RForm.Item name="conditionValue" className="inline-block">
        <RInput className="max-h-[32px]" />
      </RForm.Item>
    </RForm.Item>
  );
};

const SearchFormWrap = ({
  onSearch,
  onReset,
  supplierList,
  categoryOptions,
  channelOptions,
  isChannelsLoading,
  onCategoryChange
}: {
  onSearch: (values: SearchFormValues) => void;
  onReset: () => void;
  supplierList?: { key: string; name: string }[];
  categoryOptions: { label: string; value: string }[];
  channelOptions: { label: string; value: string }[];
  isChannelsLoading?: boolean;
  onCategoryChange: (value: string) => void;
}) => {
  const { t } = useTranslation();

  const allOption = useMemo(() => ({ label: t('common_all'), value: 'all' }), [t]);

  const handleSearch = (values: TempSearchFormValues) => {
    const { conditionType, conditionValue, ...rest } = values;
    const processedValues = Object.entries(rest).reduce(
      (acc, [key, value]) => ({
        ...acc,
        [key]: value === 'all' ? undefined : value
      }),
      {}
    );

    if (conditionType && conditionValue) {
      onSearch({
        ...processedValues,
        [conditionType]: conditionValue
      });
    } else {
      onSearch(processedValues);
    }
  };

  const supplierListOptions = useMemo(() => {
    return supplierList?.map((item) => ({ label: item.name, value: item.key }));
  }, [supplierList]);
  const supplierOptions = [allOption, ...(supplierListOptions || [])];

  return (
    <SearchForm<TempSearchFormValues>
      onSearch={handleSearch}
      onReset={onReset}
      className=""
      initialValues={{
        platformKey: 'all',
        channelKey: 'all',
        excludeTest: 1
      }}
    >
      {/* <RForm.Item name="date" label={t('pages_admin_addTimeFilter')}>
        <QuickDateSelect />
      </RForm.Item> */}
      <ConditionSearch />
      <RForm.Item name="platformKey" label={t('common_supplier')}>
        <RSelect options={supplierOptions} />
      </RForm.Item>
      <CategoryAndChannelSelect
        categoryOptions={categoryOptions}
        channelOptions={channelOptions}
        isChannelsLoading={isChannelsLoading}
        onCategoryChange={onCategoryChange}
      />
      <TagSelect name="tagId" />
      <RForm.Item name="excludeTest" label={t('common_filtertest')}>
        <RSelect
          options={[
            { label: t('common_yes'), value: 1 }, // 非測試帳號
            { label: t('common_no'), value: 0 } // all
          ]}
        />
      </RForm.Item>
    </SearchForm>
  );
};

const UnfinishedTopupOrdersPage = () => {
  const { t } = useTranslation();
  const { page, setPage, limit, setLimit } = usePagination({});
  const [params, setParams] = useState<SearchFormValues>({
    excludeTest: 1
  });
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const { defaultFrontendLanguage } = useFrontendLanguage();

  const { data: supplierList } = useTopupSupplier();
  const { data: categoryList } = useTopupCategory();
  const { data: channels, isLoading: isChannelsLoading } = useTopupChannel(selectedCategory);
  const { data: allChannels } = useTopupAllChannels();
  const { data, isPending } = useTopupIncompleteOrders({ page, limit, ...params });

  const categoryOptions = useMemo(
    () =>
      categoryList?.data?.map((item) => ({
        label: item.contentMapping[defaultFrontendLanguage]?.title,
        value: item.key
      })) ?? [],
    [categoryList?.data, defaultFrontendLanguage]
  );

  const channelOptions = useMemo(
    () => [
      { label: t('common_all'), value: 'all' },
      ...(channels?.data?.map((channel) => ({
        label: channel.contentMapping[defaultFrontendLanguage]?.title,
        value: channel.key
      })) ?? [])
    ],
    [channels?.data, defaultFrontendLanguage, t]
  );

  const tableColumns = useMemo(
    () =>
      getTableColumns(
        t,
        supplierList?.data,
        categoryList?.data,
        allChannels?.data,
        defaultFrontendLanguage
      ),
    [t, supplierList?.data, categoryList?.data, allChannels?.data, defaultFrontendLanguage]
  );

  const handleChangePage = (page: number, pageSize: number) => {
    setPage(page);
    setLimit(pageSize);
  };
  const handleSearch = (values: SearchFormValues) => {
    setPage(1);
    const processedValues = Object.entries(values).reduce(
      (acc, [key, value]) => ({
        ...acc,
        [key]: value === 'all' ? undefined : value
      }),
      {}
    );
    setParams(processedValues);
  };

  const handleReset = () => {
    setParams({ excludeTest: 1 });
  };

  return (
    <TableSearchLayout
      searchFields={
        <SearchFormWrap
          onSearch={handleSearch}
          onReset={handleReset}
          supplierList={supplierList?.data}
          categoryOptions={categoryOptions}
          channelOptions={channelOptions}
          isChannelsLoading={isChannelsLoading}
          onCategoryChange={setSelectedCategory}
        />
      }
    >
      <RTable
        loading={isPending}
        rowKey="id"
        dataSource={data?.data || []}
        columns={tableColumns}
        pagination={{
          current: page,
          pageSize: limit,
          total: data?.total || 0,
          showSizeChanger: true,
          onChange: handleChangePage
        }}
      />
    </TableSearchLayout>
  );
};

export default UnfinishedTopupOrdersPage;
