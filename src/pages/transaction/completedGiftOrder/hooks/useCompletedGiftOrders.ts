import { useQuery } from '@tanstack/react-query';

import { getCompletedGiftOrders } from '@/api/gift';
import { CompletedGiftOrderSearchParams } from '@/types/gift';

// Query keys for completed gift order related queries
export const completedGiftOrderKeys = {
  all: ['completedGiftOrder'] as const,
  completed: {
    all: ['completedGiftOrder', 'completed'] as const,
    lists: () => ['completedGiftOrder', 'completed', 'list'] as const,
    list: (params: CompletedGiftOrderSearchParams) => ['completedGiftOrder', 'completed', 'list', params] as const
  }
};

export const useCompletedGiftOrders = (params: CompletedGiftOrderSearchParams) => {
  return useQuery({
    queryKey: completedGiftOrderKeys.completed.list(params),
    queryFn: () => getCompletedGiftOrders(params),
    select: (data) => data.data
  });
};
