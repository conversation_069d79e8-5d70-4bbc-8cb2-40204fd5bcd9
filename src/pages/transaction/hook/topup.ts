// Query keys for topup related queries
type TopupQueryParams = {
  page?: number;
  limit?: number;
  [key: string]: unknown;
};

export const topupKeys = {
  all: ['topup'] as const,
  supplier: {
    all: ['topup', 'supplier'] as const,
    lists: () => ['topup', 'supplier', 'list'] as const
  },
  category: {
    all: ['topup', 'category'] as const,
    lists: () => ['topup', 'category', 'list'] as const
  },
  channel: {
    all: ['topup', 'channel'] as const,
    lists: () => ['topup', 'channel', 'list'] as const,
    list: (categoryKey: string | null) => ['topup', 'channel', 'list', categoryKey] as const
  },
  order: {
    all: ['topup', 'order'] as const,
    incomplete: {
      all: ['topup', 'order', 'incomplete'] as const,
      lists: () => ['topup', 'order', 'incomplete', 'list'] as const,
      list: (params: TopupQueryParams) => ['topup', 'order', 'incomplete', 'list', params] as const
    },
    history: {
      all: ['topup', 'order', 'history'] as const,
      lists: () => ['topup', 'order', 'history', 'list'] as const,
      list: (params: TopupQueryParams) => ['topup', 'order', 'history', 'list', params] as const
    }
  }
};
