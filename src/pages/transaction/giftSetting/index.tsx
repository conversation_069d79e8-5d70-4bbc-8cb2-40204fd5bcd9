import { useMutation } from '@tanstack/react-query';
import clsx from 'clsx';
import { useTranslation } from 'react-i18next';

import { updateGiftSetting } from '@/api/gift';
import RButton from '@/components/RButton';
import RForm from '@/components/RForm';
import RInputNumber from '@/components/RInputNumber';
import RSelect from '@/components/RSelect';
import RTable from '@/components/RTable';
import { useAntdApp } from '@/hooks/useAntdApp';
import { GiftSetting } from '@/types/gift';
import type { Vip } from '@/types/vip';
import { digitValidator } from '@/utils/validator';

import { useGiftSettingQuery } from './hooks/useGiftSettingQuery';
import { useVipQuery } from './hooks/useVipQuery';

type FormValue = GiftSetting;

const Title = ({ children, className }: { children: React.ReactNode; className?: string }) => {
  return <h2 className={clsx('text-sm font-bold', className)}>{children}</h2>;
};

const getVipOptions = (vipList: Vip[]) => {
  return vipList
    ?.filter((vip) => vip.status === 1)
    .map((vip) => ({
      label: vip.name,
      value: vip.id
    }));
};

const FormCell = ({
  index,
  fieldName,
  children,
  isFloat = false
}: {
  index: number;
  fieldName: string;
  children: React.ReactNode;
  isFloat?: boolean;
}) => {
  return (
    <RForm.Item
      className="!mb-0"
      name={['vipSetting', index, fieldName]}
      rules={[
        { required: true },
        {
          validator: (_, value) =>
            isFloat
              ? digitValidator({ value, digits: 10, decimalPlaces: 2 })
              : digitValidator({ value, digits: 10 })
        }
      ]}
    >
      {children}
    </RForm.Item>
  );
};

const initialValues = {
  receiveGiftVipLevel: 2,
  giveGiftVipLevel: 2,
  receiveGiftTimeLimit: 72,
  receiveGiftFrozenRemainTimeLimit: 192
};

const GiftSettingPage = () => {
  const { message } = useAntdApp();
  const { data: vipList, isPending: isVipListLoading } = useVipQuery();
  const { data: giftSetting, isPending: isGiftSettingLoading } = useGiftSettingQuery();
  const { t } = useTranslation();

  const updateGiftSettingMutation = useMutation({
    mutationFn: updateGiftSetting
  });

  const [form] = RForm.useForm();

  const vipOptions = getVipOptions(vipList || []);

  const handleSubmit = async () => {
    form.submit();
  };

  const handleUpdateGiftSetting = (values: FormValue) => {
    updateGiftSettingMutation.mutate(values as any);
  };

  const minGiftVipLevel = RForm.useWatch('giveGiftVipLevel', form);

  const columns = [
    {
      title: 'pages_giftSetting_vip',
      dataIndex: 'name',
      key: 'name',
      render: (value: string, __: Vip, index: number) => (
        <FormCell index={index} fieldName="level" isFloat>
          <span>{value}</span>
        </FormCell>
      )
    },
    {
      title: 'pages_giftSetting_reserveAmount',
      dataIndex: 'reserveAmount',
      key: 'reserveAmount',
      render: (_: number, __: Vip, index: number) => (
        <FormCell index={index} fieldName="reserveAmount" isFloat>
          <RInputNumber min={0} step={0.01} disabled={minGiftVipLevel - 1 > index} />
        </FormCell>
      )
    },
    {
      title: 'pages_giftSetting_minGiftAmount',
      dataIndex: 'minGiftAmount',
      key: 'minGiftAmount',
      render: (_: number, __: Vip, index: number) => (
        <FormCell index={index} fieldName="minGiftAmount" isFloat>
          <RInputNumber min={0} step={0.01} disabled={minGiftVipLevel - 1 > index} stringMode />
        </FormCell>
      )
    },
    {
      title: 'pages_giftSetting_giftTimes',
      dataIndex: 'giftTimes',
      key: 'giftTimes',
      render: (_: number, __: Vip, index: number) => (
        <FormCell index={index} fieldName="giftTimes">
          <RInputNumber min={0} step={1} disabled={minGiftVipLevel - 1 > index} />
        </FormCell>
      )
    },
    {
      title: 'pages_giftSetting_giftQuotas',
      dataIndex: 'giftQuotas',
      key: 'giftQuotas',
      render: (_: number, __: Vip, index: number) => (
        <FormCell index={index} fieldName="giftQuotas" isFloat>
          <RInputNumber min={0} step={0.01} disabled={minGiftVipLevel - 1 > index} stringMode />
        </FormCell>
      )
    },
    {
      title: 'pages_giftSetting_giftFeeRate',
      dataIndex: 'giftFeeRate',
      key: 'giftFeeRate',
      render: (_: number, __: Vip, index: number) => (
        <FormCell index={index} fieldName="giftFeeRate" isFloat>
          <RInputNumber min={0} step={0.1} disabled={minGiftVipLevel - 1 > index} stringMode />
        </FormCell>
      )
    }
  ];

  const tableColumns = columns.map((column) => ({
    ...column,
    title: t(column.title as string)
  }));

  useEffect(() => {
    if (giftSetting) {
      const timeout = setTimeout(() => {
        form.setFieldsValue(giftSetting);
      }, 100);
      return () => clearTimeout(timeout);
    }
  }, [giftSetting, form]);

  const isLoading = updateGiftSettingMutation.isPending || isVipListLoading || isGiftSettingLoading;

  return (
    <div className="flex flex-col p-6 m-4 bg-white border border-component-border">
      <RForm<FormValue>
        onFinish={handleUpdateGiftSetting}
        onFinishFailed={() => message.error(t('error_input_required'))}
        form={form}
        layout="vertical"
        initialValues={initialValues}
      >
        <div className="grid grid-cols-2 gap-y-3 gap-x-7 w-2/3">
          <Title className="col-span-2 mb-2">
            {t('pages_giftSetting_basic')}{' '}
            <span className="ml-2 text-xs font-normal text-text-secondary">
              {t('common_save_notice')}
            </span>
          </Title>

          <RForm.Item
            name="receiveGiftVipLevel"
            label={t('pages_giftSetting_receiveGiftVipLevel')}
            rules={[{ required: true, message: t('placeholder_input') }]}
          >
            <RSelect loading={isLoading} options={vipOptions} />
          </RForm.Item>

          <RForm.Item
            name="giveGiftVipLevel"
            label={t('pages_giftSetting_giveGiftVipLevel')}
            rules={[{ required: true }]}
          >
            <RSelect loading={isLoading} options={vipOptions} />
          </RForm.Item>
          <RForm.Item
            name="receiveGiftTimeLimit"
            label={`${t('pages_giftSetting_receiveGiftTimeLimit')} (${t('pages_giftSetting_receiveGiftTimeLimit_description')})`}
            rules={[{ required: true, message: t('placeholder_input') }]}
          >
            <RInputNumber min={0} suffix={t('common_hour')} />
          </RForm.Item>
          <RForm.Item
            name="receiveGiftFrozenRemainTimeLimit"
            label={`${t('pages_giftSetting_receiveGiftFrozenRemainTimeLimit')} (${t('pages_giftSetting_receiveGiftFrozenRemainTimeLimit_description')})`}
            rules={[{ required: true, message: t('placeholder_input') }]}
          >
            <RInputNumber min={0} suffix={t('common_hour')} />
          </RForm.Item>
        </div>
        <div className="mt-4">
          <Title className="mb-2">{t('pages_giftSetting_vipSetting')}</Title>
          <p className="mb-4 text-xs font-normal text-text-secondary">
            {t('pages_giftSetting_vipSetting_description')}
          </p>
          <RTable
            loading={isLoading}
            columns={tableColumns}
            dataSource={vipList || []}
            rowKey="id"
            pagination={false}
          />
        </div>
      </RForm>
      <RButton className="!w-30 mt-7" type="primary" onClick={handleSubmit}>
        {t('common_save')}
      </RButton>
    </div>
  );
};

export default GiftSettingPage;
