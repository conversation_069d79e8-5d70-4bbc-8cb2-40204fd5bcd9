import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Layout } from 'antd';
import { message, Spin } from 'antd';

import {
  editBasicSettings,
  editSoundSettings,
  getPlatformSettings,
  PlatformSettingsRequest
} from '@/api/platform';
import GeneralSettings from '@/components/_pages/platform/GerneralSettings';
import LoginSettings from '@/components/_pages/platform/LoginSettings';
import SideMenu from '@/components/_pages/platform/SideMenu';
import SoundEffectSettings from '@/components/_pages/platform/SoundEffectSettings';
import RModal from '@/components/RModal';
import { SoundEffectAPI, SoundEffectType } from '@/enums/platform';
import { AudioFile, GeneralSettingsData, LoginSettingsData } from '@/types/platform';

const { Content } = Layout;

export type MenuKey = 'general' | 'soundEffects' | 'loginSettings';

// 定義所有設定的資料型別
interface PlatformSettings {
  general: GeneralSettingsData;
  soundEffects: Record<SoundEffectType, AudioFile | undefined>;
  loginSettings: LoginSettingsData;
}

// 音效設置映射表
const SOUND_EFFECT_TO_API_FIELD_MAP: Record<SoundEffectType, string> = {
  [SoundEffectType.BACKGROUND_MUSIC]: SoundEffectAPI.BACKGROUND_MUSIC,
  [SoundEffectType.CLICK_OPEN_SOUND]: SoundEffectAPI.CLICK_OPEN_SOUND,
  [SoundEffectType.CLICK_CLOSE_SOUND]: SoundEffectAPI.CLICK_CLOSE_SOUND,
  [SoundEffectType.CLICK_HINT_SOUND]: SoundEffectAPI.CLICK_HINT_SOUND,
  [SoundEffectType.CLICK_FAILED_SOUND]: SoundEffectAPI.CLICK_FAILED_SOUND
};

// 使用 memo 包裝子組件
const MemoizedGeneralSettings = memo(GeneralSettings);
const MemoizedSoundEffectSettings = memo(SoundEffectSettings);
const MemoizedLoginSettings = memo(LoginSettings);

const createMenuItems = (t: (key: string) => string) => [
  {
    key: 'general',
    label: <p>{t('pages_platform_general_title')}</p>
  },
  {
    key: 'soundEffects',
    label: <p>{t('pages_platform_sound_title')}</p>
  },
  {
    key: 'loginSettings',
    label: <p>{t('pages_platform_login_title')}</p>
  }
];

// 音效類型到API字段的映射
const mappings = [
  {
    type: SoundEffectType.BACKGROUND_MUSIC,
    url: 'frontendBackgroundMusic',
    name: 'frontendBackgroundMusicName'
  },
  {
    type: SoundEffectType.CLICK_OPEN_SOUND,
    url: 'frontendClickOpenSound',
    name: 'frontendClickOpenSoundName'
  },
  {
    type: SoundEffectType.CLICK_CLOSE_SOUND,
    url: 'frontendClickCloseSound',
    name: 'frontendClickCloseSoundName'
  },
  {
    type: SoundEffectType.CLICK_HINT_SOUND,
    url: 'frontendClickHintSound',
    name: 'frontendClickHintSoundName'
  },
  {
    type: SoundEffectType.CLICK_FAILED_SOUND,
    url: 'frontendHintFailedSound',
    name: 'frontendHintFailedSoundName'
  }
];

// 音效相關的字段
const PLATFORM_SOUND_FIELDS = [
  SoundEffectAPI.BACKGROUND_MUSIC,
  SoundEffectAPI.CLICK_OPEN_SOUND,
  SoundEffectAPI.CLICK_CLOSE_SOUND,
  SoundEffectAPI.CLICK_HINT_SOUND,
  SoundEffectAPI.CLICK_FAILED_SOUND
];

// 設置處理函數類型
type SettingsHandler = (
  data: GeneralSettingsData | Record<SoundEffectType, AudioFile | undefined> | LoginSettingsData,
  currentSettings: PlatformSettings,
  apiSettings: Partial<PlatformSettingsRequest>
) => void;

const PlatformPage = () => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const menuItems = useMemo(() => createMenuItems(t), [t]);

  const [selectedMenu, setSelectedMenu] = useState<MenuKey>('general');
  const [changedFields, setChangedFields] = useState<Record<string, boolean>>({});
  const [targetMenu, setTargetMenu] = useState<MenuKey | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);

  // 獲取設置
  const { data: settings, isLoading: isLoadingSettings } = useQuery({
    queryKey: ['platformSettings'],
    queryFn: async () => {
      const { data: apiData } = await getPlatformSettings();

      if (!apiData) throw new Error('No data received from API');

      const soundEffects = {} as Record<SoundEffectType, AudioFile>;

      // 創建音效對應
      mappings.forEach(({ type, url, name }) => {
        soundEffects[type] = {
          url: (apiData[url as keyof typeof apiData] as string) || '',
          name: (apiData[name as keyof typeof apiData] as string) || ''
        };
      });

      return {
        general: {
          customerServiceLink: apiData.customerServiceLink || '',
          frontendChangeNameSetting: {
            isFree: apiData.frontendChangeNameSetting?.isFree || 0,
            freeTimes: apiData.frontendChangeNameSetting?.freeTimes || 0,
            fee: apiData.frontendChangeNameSetting?.fee || '0'
          },
          frontendBindPhoneSetting: {
            changeable: apiData.frontendBindPhoneSetting?.changeable || 0,
            fee: apiData.frontendBindPhoneSetting?.fee || '0'
          }
        },
        soundEffects,
        loginSettings: apiData.loginSetting || { line: 0, phone: 0, account: 0 }
      } as PlatformSettings;
    }
  });

  const soundSettingsMutation = useMutation({
    mutationFn: async (apiSettings: Partial<PlatformSettingsRequest>) => {
      return await editSoundSettings(apiSettings as PlatformSettingsRequest);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['platformSettings'] });
      // message.success(response.message);
      setChangedFields({});
    },
    onError: () => {
      // message.error(error.message);
    }
  });

  const basicSettingsMutation = useMutation({
    mutationFn: async (apiSettings: Partial<PlatformSettingsRequest>) => {
      return await editBasicSettings(apiSettings as PlatformSettingsRequest);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['platformSettings'] });
      // message.success(response.message || response.msg || 'Success');
      setChangedFields({});
    },
    onError: () => {
      // message.error(error.message);
    }
  });

  const isLoading =
    soundSettingsMutation.isPending || basicSettingsMutation.isPending || isLoadingSettings;

  const handleMenuClick = useCallback(
    (key: MenuKey) => {
      if (Object.values(changedFields).some((changed) => changed)) {
        setTargetMenu(key);
        setIsModalVisible(true);
      } else {
        setSelectedMenu(key);
      }
    },
    [changedFields]
  );

  // 異動資料不儲存就切換頁面
  const handleModalConfirm = useCallback(() => {
    if (targetMenu) {
      queryClient.invalidateQueries({ queryKey: ['platformSettings'] });
      setSelectedMenu(targetMenu);
      setChangedFields({});
    }
    setIsModalVisible(false);
    setTargetMenu(null);
  }, [targetMenu, queryClient]);

  // 取消切換頁面
  const handleModalCancel = useCallback(() => {
    setIsModalVisible(false);
    setTargetMenu(null);
  }, []);

  const handleSettingsChange = useCallback((menuKey: MenuKey) => {
    setChangedFields((prev) => ({
      ...prev,
      [menuKey]: true
    }));
  }, []);

  // 建立處理函數的映射表
  const settingsHandlers: Record<MenuKey, SettingsHandler> = {
    // 比對user是否有異動
    general: (data, _, apiSettings) => {
      const generalData = data as GeneralSettingsData;

      apiSettings.customerServiceLink = generalData.customerServiceLink;

      apiSettings.frontendChangeNameSetting = generalData.frontendChangeNameSetting;
      apiSettings.frontendBindPhoneSetting = generalData.frontendBindPhoneSetting;
    },
    loginSettings: (data, currentSettings, apiSettings) => {
      const loginData = data as LoginSettingsData;
      const currentLoginSettings = currentSettings.loginSettings;

      const changes = Object.entries(loginData).reduce((acc, [key, value]) => {
        const typedKey = key as keyof LoginSettingsData;
        if (value !== currentLoginSettings[typedKey]) {
          acc[typedKey] = value;
        }
        return acc;
      }, {} as Partial<LoginSettingsData>);

      if (Object.keys(changes).length > 0) {
        apiSettings.loginSetting = { ...currentLoginSettings, ...changes };
      }
    },
    soundEffects: (data, currentSettings, apiSettings) => {
      const soundEffects = data as Record<SoundEffectType, AudioFile | undefined>;

      Object.entries(soundEffects).forEach(([soundType, audioFile]) => {
        if (!audioFile) return;

        const apiField = SOUND_EFFECT_TO_API_FIELD_MAP[soundType as SoundEffectType];
        const originalAudioFile = currentSettings.soundEffects[soundType as SoundEffectType];

        // 檢查是否有新文件上傳
        if ('file' in audioFile && audioFile.file instanceof File) {
          apiSettings[apiField] = audioFile.file;
          apiSettings[`${apiField}Name`] = audioFile.name;
          return;
        }

        // 檢查 URL 或名稱是否有變更
        if (audioFile.url !== originalAudioFile?.url) {
          apiSettings[apiField] = audioFile.url;
        }
        if (audioFile.name !== originalAudioFile?.name) {
          apiSettings[`${apiField}Name`] = audioFile.name;
        }
      });
    }
  };

  const handleSave = async (
    data: GeneralSettingsData | Record<SoundEffectType, AudioFile | undefined> | LoginSettingsData
  ) => {
    if (!settings) return;

    // 如果已經在加載中，防止重複提交
    if (isLoading) {
      message.info(t('common_loading_message'));
      return;
    }

    const apiSettings: Partial<PlatformSettingsRequest> = {};

    const handler = settingsHandlers[selectedMenu];
    if (handler) {
      handler(data, settings, apiSettings);
    }

    // 如果沒有實際的變更，直接返回
    if (Object.keys(apiSettings).length === 0) {
      message.info(t('common_no_changes'));
      return;
    }

    // 根據設置類型調用不同的API
    const hasSoundFiles = PLATFORM_SOUND_FIELDS.some((field) => apiSettings[field] instanceof File);

    if (selectedMenu === 'soundEffects' && hasSoundFiles) {
      soundSettingsMutation.mutate(apiSettings);
    } else {
      basicSettingsMutation.mutate(apiSettings);
    }
  };

  // 組件映射表
  const componentMap = {
    general: (
      <MemoizedGeneralSettings
        onSave={handleSave}
        onChange={() => handleSettingsChange('general')}
        initialData={settings?.general}
      />
    ),
    soundEffects: (
      <MemoizedSoundEffectSettings
        onSave={handleSave}
        onChange={() => handleSettingsChange('soundEffects')}
        initialData={settings?.soundEffects}
      />
    ),
    loginSettings: (
      <MemoizedLoginSettings
        onSave={handleSave}
        onChange={() => handleSettingsChange('loginSettings')}
        initialData={settings?.loginSettings}
      />
    )
  };

  const renderContent = () => {
    if (isLoadingSettings) {
      return (
        <div className="flex items-center justify-center h-full">
          <Spin />
        </div>
      );
    }

    // 使用Spin包裝組件來顯示加載狀態
    return <Spin spinning={isLoading}>{componentMap[selectedMenu] || null}</Spin>;
  };

  return (
    <Layout>
      <div className="flex mt-3 ml-4 bg-white mr-9 border border-component-border">
        {/* Left sidebar */}
        <div className="w-48 border-r border-component-border">
          <SideMenu items={menuItems} selectedKey={selectedMenu} onMenuClick={handleMenuClick} />
        </div>

        {/* Main content */}
        <Content>
          <div className="flex-1 h-[75vh] max-w-[1440px]">{renderContent()}</div>
        </Content>
      </div>

      {/* 彈窗 */}
      <RModal
        title={t('common_alert')}
        open={isModalVisible}
        onOk={handleModalConfirm}
        onCancel={handleModalCancel}
        className="!w-[360px] !h-[270px]"
        centered
      >
        <div className="flex flex-col items-center">
          <div className="flex items-center justify-center w-12 h-12 mb-4 text-2xl text-white rounded-full bg-warning">
            !
          </div>
          <p className="text-center">
            {t('pages_platform_modal_content')}，
            <br />
            {t('pages_platform_model_comfirm')}？
          </p>
        </div>
      </RModal>
    </Layout>
  );
};

export default PlatformPage;
