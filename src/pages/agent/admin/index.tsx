import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { editAdminStatus, getAdminList, getRoleListOptions } from '@/api/admin';
import QuickDateSelect from '@/components/QuickDateSelect';
import RButton from '@/components/RButton';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RSelect from '@/components/RSelect';
import RTable from '@/components/RTable';
import SearchForm from '@/components/SearchForm';
import StatusLabel from '@/components/StatusLabel';
import StatusSelect from '@/components/StatusSelect';
import { useAccountDialog } from '@/hooks/useAccountDialog';
import useConfirmModal from '@/hooks/useConfirmModal';
import usePagination from '@/hooks/usePagination';
import TableSearchLayout from '@/layout/TableSearchLayout';
import { Admin, Role } from '@/types/admin';
import { formatTime } from '@/utils/time';

import AdminModal, { type FormValue as AdminFormValue } from './AdminModal';
import ResetOtpModal from './ResetOtpModal';
import ResetPasswordModal from './ResetPasswordModal';

type SearchFormValues = {
  account?: string;
  createdBy?: string;
  updatedBy?: string;
  roleId?: number;
  status?: 1 | 0;
  start?: number;
  end?: number;
};

type TempSearchFormValues = {
  roleId?: number;
  status?: 1 | 0;
} & {
  conditionType: string;
  conditionValue: string;
};

const conditionOptions = [
  { label: 'common_account', value: 'account' },
  { label: 'pages_admin_createdBy', value: 'createdBy' },
  { label: 'pages_admin_updatedBy', value: 'updatedBy' }
];

const columns = [
  {
    title: 'common_account',
    dataIndex: 'account'
  },
  {
    title: 'pages_admin_role',
    dataIndex: 'roles',
    render: (roles: Role[]) => roles.map((role) => role.name).join(', ')
  },
  {
    title: 'common_addTime',
    dataIndex: 'createdAt',
    render: (createdAt: number) => formatTime(createdAt)
  },
  {
    title: 'pages_admin_createdBy',
    dataIndex: 'createdBy'
  },
  {
    title: 'common_lastEditTime',
    dataIndex: 'updatedAt',
    render: (updatedAt: number) => formatTime(updatedAt)
  },
  {
    title: 'pages_admin_updatedBy',
    dataIndex: 'updatedBy'
  },
  {
    title: 'common_status',
    dataIndex: 'status',
    render: (status: 1 | 0) => <StatusLabel status={status} />
  }
];

const RoleSelect = () => {
  const { t } = useTranslation();
  const { data: roleList } = useQuery({
    queryKey: ['roleList'],
    queryFn: getRoleListOptions
  });

  return (
    <RForm.Item name="roleId" label={t('pages_admin_role')}>
      <RSelect
        options={
          roleList?.data?.map((role) => ({
            label: role.name,
            value: role.id
          })) || []
        }
      />
    </RForm.Item>
  );
};

const ConditionSearch = () => {
  const { t } = useTranslation();
  return (
    <RForm.Item label={t('common_condition')}>
      <RForm.Item name="conditionType" className="inline-block !mr-2">
        <RSelect
          options={conditionOptions.map((option) => ({
            label: t(option.label),
            value: option.value
          }))}
        />
      </RForm.Item>
      <RForm.Item name="conditionValue" className="inline-block">
        <RInput className="max-h-[32px]" />
      </RForm.Item>
    </RForm.Item>
  );
};

const SearchFormWrap = ({
  onSearch,
  onReset
}: {
  onSearch: (values: SearchFormValues) => void;
  onReset: (values: SearchFormValues) => void;
}) => {
  const { t } = useTranslation();

  const handleSearch = (values: TempSearchFormValues) => {
    const { conditionType, conditionValue, ...rest } = values;
    const searchValues = {
      ...rest,
      [conditionType]: conditionValue
    };
    onSearch(searchValues);
  };

  const handleReset = () => {
    onReset({});
  };

  return (
    <SearchForm<TempSearchFormValues> onSearch={handleSearch} onReset={handleReset} className="">
      <RForm.Item name="date" label={t('pages_admin_addTimeFilter')}>
        <QuickDateSelect />
      </RForm.Item>
      <ConditionSearch />
      <RoleSelect />
      <RForm.Item name="status" label={t('common_status')} initialValue={undefined}>
        <StatusSelect />
      </RForm.Item>
    </SearchForm>
  );
};

const ActionButtons = ({
  data,
  onEdit,
  onEditStatus,
  onResetPassword,
  onResetOtp,
  isEditStatusPending
}: {
  data: Admin;
  onEdit: (data: Admin) => void;
  onEditStatus: (data: Admin) => void;
  onResetPassword: (data: Admin) => void;
  onResetOtp: (data: Admin) => void;
  isEditStatusPending: boolean;
}) => {
  const { t } = useTranslation();
  return (
    <div className="flex gap-1">
      <RButton
        size="small"
        variant="outlined"
        color="primary"
        type="link"
        onClick={() => onEdit(data)}
      >
        {t('common_edit')}
      </RButton>
      <RButton
        size="small"
        variant="outlined"
        color="primary"
        type="link"
        onClick={() => onResetPassword(data)}
      >
        {t('common_reset_password')}
      </RButton>
      <RButton
        size="small"
        variant="outlined"
        color="primary"
        type="link"
        onClick={() => onResetOtp(data)}
      >
        {t('common_reset_otp')}
      </RButton>
      <RButton
        size="small"
        variant="outlined"
        color={data.status === 1 ? 'red' : 'success'}
        type="link"
        loading={isEditStatusPending}
        onClick={() => onEditStatus(data)}
      >
        {data.status === 1 ? t('common_inactive') : t('common_active')}
      </RButton>
    </div>
  );
};

const AdminPage = () => {
  const [open, setOpen] = useState(false);
  const { t } = useTranslation();
  const { page, setPage, limit, setLimit } = usePagination({});
  const [params, setParams] = useState({});
  const [initialValues, setInitialValues] = useState<AdminFormValue | undefined>(undefined);
  const [resetingPasswordUser, setResetingPasswordUser] = useState<Admin | undefined>(undefined);
  const [resetingOtpUser, setResetingOtpUser] = useState<Admin | undefined>(undefined);
  const { confirmModal } = useConfirmModal();
  const { showAccountDialog, AccountDialog } = useAccountDialog();

  const queryClient = useQueryClient();
  const { data, isPending } = useQuery({
    queryKey: ['adminList', { page, limit, ...params }],
    queryFn: () =>
      getAdminList({
        page: page,
        limit: limit,
        ...params
      })
  });

  const { mutate: editStatusMutate, isPending: isEditStatusPending } = useMutation({
    mutationFn: editAdminStatus,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['adminList'] });
    }
  });

  const handleCloseModal = () => {
    setOpen(false);
    setTimeout(() => {
      setInitialValues(undefined);
    }, 200);
  };

  const handleEdit = (data: Admin) => {
    setInitialValues({
      id: data.id,
      account: data.account,
      roleId: data.roles[0].id ?? 0,
      status: data.status
    });
    setOpen(true);
  };

  const handleResetPassword = (data: Admin) => {
    setResetingPasswordUser(data);
  };

  const handleResetOtp = (data: Admin) => {
    setResetingOtpUser(data);
  };

  const handleEditStatus = (data: Admin) => {
    editStatusMutate({
      id: data.id,
      status: data.status === 1 ? 0 : 1
    });
  };

  const openEditStatusModal = (data: Admin) => {
    confirmModal({
      content:
        data.status === 1
          ? t('pages_admin_confirm_status_disable')
          : t('pages_admin_confirm_status_enable'),
      onOk: () => {
        handleEditStatus(data);
      }
    });
  };

  const actionColumn = {
    title: 'common_action',
    render: (_: unknown, data: Admin) => (
      <ActionButtons
        data={data}
        onEdit={handleEdit}
        onResetPassword={handleResetPassword}
        onResetOtp={handleResetOtp}
        onEditStatus={openEditStatusModal}
        isEditStatusPending={isEditStatusPending}
      />
    )
  };

  const tableColumns = [...columns, actionColumn].map((column) => ({
    ...column,
    title: t(column.title)
  }));

  const handleChangePage = (page: number, pageSize: number) => {
    setPage(page);
    setLimit(pageSize);
  };

  const handleSearch = (values: SearchFormValues) => {
    setPage(1);
    setParams(values);
  };

  return (
    <TableSearchLayout
      searchFields={<SearchFormWrap onSearch={handleSearch} onReset={handleSearch} />}
    >
      <RButton className="mb-4" type="primary" onClick={() => setOpen(true)}>
        {t('common_add_name', { name: t('pages_admin') })}
      </RButton>
      <RTable
        loading={isPending}
        rowKey="id"
        dataSource={data?.data?.data || []}
        columns={tableColumns}
        pagination={{
          current: page,
          pageSize: limit,
          total: data?.data?.total || 0,
          showSizeChanger: true,
          onChange: handleChangePage
        }}
      />
      <AdminModal
        open={open}
        initialValues={initialValues}
        onClose={handleCloseModal}
        showAccountDialog={showAccountDialog}
      />
      {resetingPasswordUser && (
        <ResetPasswordModal
          open={!!resetingPasswordUser}
          onClose={() => setResetingPasswordUser(undefined)}
          user={resetingPasswordUser}
        />
      )}
      {resetingOtpUser && (
        <ResetOtpModal
          open={!!resetingOtpUser}
          onClose={() => setResetingOtpUser(undefined)}
          user={resetingOtpUser}
        />
      )}
      {AccountDialog}
    </TableSearchLayout>
  );
};

export default AdminPage;
