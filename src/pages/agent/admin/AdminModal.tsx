import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import clsx from 'clsx';

import { createAdmin, editAdmin, getRoleListOptions } from '@/api/admin';
import CopyIcon from '@/components/CopyIcon';
import FormModal from '@/components/FormModal';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RSelect from '@/components/RSelect';
import RSwitch from '@/components/RSwitch';
import { generatePasswordWithPattern } from '@/utils/randomText';

export type FormValue = {
  id?: number;
  account: string;
  password?: string;
  roleId: number;
  status: 1 | 0;
};

export const AdminModal = ({
  open,
  onOk,
  onClose,
  initialValues,
  showAccountDialog
}: {
  open: boolean;
  onOk?: () => void;
  onClose: () => void;
  initialValues?: FormValue;
  showAccountDialog: ({ account, password }: { account: string; password: string }) => void;
}) => {
  const { t } = useTranslation();
  const [form] = RForm.useForm<FormValue>();
  const [password, setPassword] = useState(generatePasswordWithPattern(3, 4));
  const queryClient = useQueryClient();
  const isEdit = !!initialValues;
  const { data: roleListOptionsData } = useQuery({
    queryKey: ['roleListOptions'],
    queryFn: getRoleListOptions
  });

  const handleClose = () => {
    form.resetFields();
    onClose();
  };

  const { mutate: createAdminMutate, isPending: isCreateAdminPending } = useMutation({
    mutationFn: createAdmin,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['adminList'] });
      onOk?.();
      const accountInfo = {
        account: form.getFieldValue('account'),
        password: password
      };
      handleClose();
      showAccountDialog({
        account: accountInfo.account,
        password: accountInfo.password
      });
    }
  });

  const { mutate: editAdminMutate, isPending: iseditAdminPending } = useMutation({
    mutationFn: editAdmin,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['adminList'] });
      onOk?.();
      handleClose();
    }
  });

  useEffect(() => {
    if (open && !isEdit) {
      setPassword(generatePasswordWithPattern(3, 4));
    }
  }, [open, isEdit]);

  const handleSubmit = (values: FormValue) => {
    if (isEdit) {
      editAdminMutate({ ...values, id: values.id! });
    } else {
      createAdminMutate({ ...values, password });
    }
  };

  const isLoading = isCreateAdminPending || iseditAdminPending;

  return (
    <FormModal<FormValue>
      form={form}
      title={t(isEdit ? 'common_edit_name' : 'common_add_name', { name: t('pages_admin') })}
      open={open}
      onClose={handleClose}
      onSubmit={handleSubmit}
      isLoading={isLoading}
      width={isEdit ? 360 : 780}
      formProps={{
        layout: 'vertical',
        className: clsx('grid gap-y-2 gap-x-15', isEdit ? 'grid-cols-1' : 'grid-cols-2')
      }}
      initialValues={initialValues || { status: 1 }}
    >
      <RForm.Item name="id" noStyle></RForm.Item>
      <RForm.Item
        label={t('common_account')}
        name="account"
        rules={[
          { required: true },
          {
            pattern: /^[a-zA-Z0-9]{6,20}$/,
            message: t('login_reset_password_placeholder_password')
          }
        ]}
      >
        <RInput disabled={isEdit} />
      </RForm.Item>
      {!isEdit && (
        <RForm.Item label={t('pages_admin_password')}>
          <div className="flex items-center gap-2">
            {password}
            <CopyIcon text={password} />
          </div>
        </RForm.Item>
      )}
      <RForm.Item label={t('pages_admin_role')} name="roleId" rules={[{ required: true }]}>
        <RSelect
          options={
            roleListOptionsData?.data?.map((item) => ({
              label: item.name,
              value: item.id
            })) ?? []
          }
        />
      </RForm.Item>
      <RForm.Item label={t('common_status')} name="status" valuePropName="checked">
        <RSwitch />
      </RForm.Item>
    </FormModal>
  );
};

export default AdminModal;
