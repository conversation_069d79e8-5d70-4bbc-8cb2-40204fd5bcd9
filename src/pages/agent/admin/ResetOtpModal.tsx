import { useMutation } from '@tanstack/react-query';

import { resetAdminOtp } from '@/api/admin';
import CheckedIcon from '@/assets/img/icon/check-circle.svg?react';
import InfoIcon from '@/assets/img/icon/info.svg?react';
import RModal from '@/components/RModal';
import { Admin } from '@/types/admin';

const OtpResetSuccessContent = () => {
  const { t } = useTranslation();
  return (
    <div className="mb-4 flex flex-col items-center">
      <div className="flex items-center gap-2 mb-4">
        <CheckedIcon width={48} height={48} className="fill-success" />
      </div>
      <div className="text-center">{t('components_resetOtpModal_success')}</div>
    </div>
  );
};

const ResetOtpDescription = () => {
  const { t } = useTranslation();
  return (
    <div className="mb-4 flex flex-col items-center">
      <div className="flex items-center gap-2 mb-4">
        <InfoIcon width={48} height={48} className="fill-warning" />
      </div>
      <div className="text-center">{t('components_resetOtpModal_description')}</div>
    </div>
  );
};

export const ResetOtpModal = ({
  open,
  onClose,
  user
}: {
  open: boolean;
  onClose: () => void;
  user: Admin;
}) => {
  const { t } = useTranslation();
  const [resetSuccess, setResetSuccess] = useState<boolean>(false);

  const { mutateAsync: resetOtpMutate, isPending } = useMutation({
    mutationFn: resetAdminOtp
  });

  const onSubmit = async () => {
    const response = await resetOtpMutate({ adminId: user.id });
    if (response.code === 200) {
      setResetSuccess(true);
    }
  };

  const handleClose = () => {
    setResetSuccess(false);
    onClose();
  };

  return (
    <RModal
      centered
      maskClosable={false}
      title={t('common_alert')}
      open={open}
      onCancel={handleClose}
      onOk={onSubmit}
      loading={isPending}
      okButtonProps={{ text: t('common_confirm'), show: !resetSuccess }}
      cancelButtonProps={{ text: t('common_cancel'), show: !resetSuccess }}
      destroyOnClose={true}
      forceRender={true}
    >
      {resetSuccess ? <OtpResetSuccessContent /> : <ResetOtpDescription />}
    </RModal>
  );
};

export default ResetOtpModal;
