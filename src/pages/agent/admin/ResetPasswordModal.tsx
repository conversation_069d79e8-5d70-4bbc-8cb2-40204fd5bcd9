import { useMutation } from '@tanstack/react-query';

import { resetAdminPassword } from '@/api/admin';
import CheckedIcon from '@/assets/img/icon/check-circle.svg?react';
import InfoIcon from '@/assets/img/icon/info.svg?react';
import CopyIcon from '@/components/CopyIcon';
import RModal from '@/components/RModal';
import { Admin } from '@/types/admin';

const NewPasswordContent = ({ newPwd }: { newPwd: string }) => {
  const { t } = useTranslation();
  return (
    <div className="mb-4 flex flex-col items-center">
      <div className="flex items-center gap-2 mb-4">
        <CheckedIcon width={48} height={48} className="fill-success" />
      </div>
      <div className="text-center">{t('components_resetPasswordModal_success')}</div>
      <div className="mt-2 flex gap-1">
        <span>
          {t('common_newPassword')}: {newPwd}
        </span>{' '}
        <CopyIcon text={newPwd} width={16} height={16} />
      </div>
    </div>
  );
};

const ResetPasswordDescription = () => {
  const { t } = useTranslation();
  return (
    <div className="mb-4 flex flex-col items-center">
      <div className="flex items-center gap-2 mb-4">
        <InfoIcon width={48} height={48} className="fill-warning" />
      </div>
      <div className="text-center">{t('components_resetPasswordModal_description')}</div>
    </div>
  );
};

export const ResetPasswordModal = ({
  open,
  onClose,
  user
}: {
  open: boolean;
  onClose: () => void;
  user: Admin;
}) => {
  const { t } = useTranslation();
  const [newPwd, setNewPwd] = useState<string | null>(null);

  const { mutateAsync: resetPasswordMutate, isPending } = useMutation({
    mutationFn: resetAdminPassword
  });

  const onSubmit = async () => {
    const response = await resetPasswordMutate({ adminId: user.id });
    if (response.code === 200) {
      const newPwdFromBE = response.data?.newPassword;
      if (newPwdFromBE) {
        setNewPwd(newPwdFromBE);
      }
    }
  };

  const handleClose = () => {
    setNewPwd(null);
    onClose();
  };

  return (
    <RModal
      centered
      maskClosable={false}
      title={t('common_alert')}
      open={open}
      onCancel={handleClose}
      onOk={onSubmit}
      loading={isPending}
      okButtonProps={{ text: t('common_confirm'), show: !newPwd }}
      cancelButtonProps={{ text: t('common_cancel'), show: !newPwd }}
      destroyOnClose={true}
      forceRender={true}
    >
      {newPwd ? <NewPasswordContent newPwd={newPwd} /> : <ResetPasswordDescription />}
    </RModal>
  );
};

export default ResetPasswordModal;
