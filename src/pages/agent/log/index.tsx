import { useMutation, useQuery } from '@tanstack/react-query';
import dayjs from 'dayjs';

import { adminLogListOptions, getAdminLogList, getLogDataExport } from '@/api/admin';
import ExportRecord from '@/components/ExportRecord';
import QuickDateSelect from '@/components/QuickDateSelect';
import RForm, { RFormItem } from '@/components/RForm';
import RInput from '@/components/RInput';
import RSelect from '@/components/RSelect';
import RTable from '@/components/RTable';
import SearchForm from '@/components/SearchForm';
import usePagination from '@/hooks/usePagination';
import TableSearchLayout from '@/layout/TableSearchLayout';
import { formatTime } from '@/utils/time';

type SearchFormValues = {
  account?: string;
  opPage?: number;
  content?: string;
  start?: number;
  end?: number;
};

const columns = [
  {
    title: 'common_account',
    dataIndex: 'account',
    width: 120
  },
  {
    title: 'pages_log_opPage',
    dataIndex: 'opPage',
    width: 120
  },
  {
    title: 'pages_log_content',
    dataIndex: 'content',
    width: 300,
    render: (content: string) => (
      <div style={{ maxWidth: 650 }}>
        <div className="whitespace-pre-wrap break-words">{content}</div>
      </div>
    )
  },
  {
    title: 'pages_log_time',
    dataIndex: 'createdAt',
    width: 180,
    render: (createdAt: number) => formatTime(createdAt)
  }
];
const defaultToday = [dayjs().startOf('day').valueOf(), dayjs().endOf('day').valueOf()];

const LogPageList = ({ className }: { className?: string }) => {
  const { t } = useTranslation();
  const { data: logOptionList } = useQuery({
    queryKey: ['logOptionList'],
    queryFn: adminLogListOptions
  });

  return (
    <RForm.Item name="opPage" label={t('pages_log_opPage')}>
      <RSelect
        options={logOptionList?.data?.map((item) => ({
          label: item.label,
          value: item.id
        }))}
        className={className}
      />
    </RForm.Item>
  );
};

const SearchFormWrap = ({
  onSearch,
  onReset
}: {
  onSearch: (values: SearchFormValues) => void;
  onReset: () => void;
}) => {
  const { t } = useTranslation();

  const handleSearch = (values: SearchFormValues) => {
    onSearch(values);
  };

  return (
    <SearchForm<SearchFormValues> onSearch={handleSearch} onReset={onReset} className="flex">
      <RForm.Item name="date" label={t('pages_log_operateTimeFilter')} initialValue={defaultToday}>
        <QuickDateSelect defaultActiveKey="today" />
      </RForm.Item>
      <RFormItem name="account" label={`${t('pages_admin')}${t('pages_admin_username')}`}>
        <RInput className="!w-80 max-h-[32px]" />
      </RFormItem>
      <LogPageList className="!w-80" />
      <RFormItem name="content" label={t('pages_log_record')}>
        <RInput className="!w-80 max-h-[32px]" />
      </RFormItem>
    </SearchForm>
  );
};

const LogPage = () => {
  const { t } = useTranslation();
  const { page, setPage, limit, setLimit } = usePagination({});
  const [params, setParams] = useState<SearchFormValues>({
    start: defaultToday[0],
    end: defaultToday[1]
  });

  const { data, isPending } = useQuery({
    queryKey: ['logList', { page, limit, ...params }],
    queryFn: () => getAdminLogList({ page, limit, ...params })
  });

  const {
    mutate: exportLog,
    data: exportData,
    isPending: renderModalContent
  } = useMutation({
    mutationFn: getLogDataExport
  });

  const tableColumns = [...columns].map((column) => ({
    ...column,
    title: t(column.title)
  }));

  const handleChangePage = (page: number, pageSize: number) => {
    setPage(page);
    setLimit(pageSize);
  };

  const handleSearch = (values: SearchFormValues) => {
    setPage(1);
    setParams(values);
  };

  const handleReset = () => {
    setParams({
      start: defaultToday[0],
      end: defaultToday[1],
      account: undefined,
      opPage: undefined,
      content: undefined
    });
  };

  return (
    <TableSearchLayout
      searchFields={<SearchFormWrap onSearch={handleSearch} onReset={handleReset} />}
    >
      <ExportRecord
        onClick={() => exportLog(params)}
        id={exportData?.data?.id}
        isLoading={renderModalContent}
      />
      <RTable
        loading={isPending}
        rowKey="id"
        dataSource={data?.data?.data || []}
        columns={tableColumns}
        scroll={{ x: 600 }}
        pagination={{
          current: page,
          pageSize: limit,
          total: data?.data?.total || 0,
          showSizeChanger: true,
          onChange: handleChangePage
        }}
      />
    </TableSearchLayout>
  );
};

export default LogPage;
