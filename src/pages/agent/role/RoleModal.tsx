import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { createRole, editRole, getAllPermissions } from '@/api/admin';
import FormModal from '@/components/FormModal';
import { PermissionSelect } from '@/components/PermissionSelect';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';

export type FormValue = {
  id?: number;
  name: string;
  permissions: number[];
};

const Block = ({ title, children }: { title: string; children: React.ReactNode }) => {
  return (
    <div className="flex flex-col gap-2">
      <div className="text-[13px] font-bold">{title}</div>
      {children}
    </div>
  );
};

export const RoleModal = ({
  open,
  onOk,
  onClose,
  initialValues
}: {
  open: boolean;
  onOk?: () => void;
  onClose: () => void;
  initialValues?: FormValue;
}) => {
  const { t } = useTranslation();
  const [form] = RForm.useForm<FormValue>();

  const queryClient = useQueryClient();
  const isEdit = !!initialValues?.id;

  const { data: permissionsData } = useQuery({
    queryKey: ['getAdminPermissions'],
    queryFn: getAllPermissions
  });

  const handleClose = () => {
    form.resetFields();
    onClose();
  };

  const { mutate: createMutation, isPending: isCreatePending } = useMutation({
    mutationFn: createRole,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['roleList'] });
      onOk?.();
      handleClose();
    }
  });

  const { mutate: editMutation, isPending: isEditPending } = useMutation({
    mutationFn: editRole,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['roleList'] });
      onOk?.();
      handleClose();
    }
  });

  const handleSubmit = (values: FormValue) => {
    if (isEdit) {
      editMutation({ ...values, id: values.id! });
    } else {
      createMutation({ ...values });
    }
  };

  const isLoading = isCreatePending || isEditPending;

  return (
    <FormModal
      title={t(isEdit ? 'common_edit_name' : 'common_add_name', { name: t('pages_role') })}
      form={form}
      initialValues={initialValues}
      open={open}
      onClose={handleClose}
      onSubmit={handleSubmit}
      isLoading={isLoading}
    >
      <div className="flex flex-col gap-2">
        <RForm.Item name="id" noStyle></RForm.Item>
        <Block title={t('pages_role_baseSetting')}>
          <RForm.Item
            label={<span className="text-text-secondary">{t('pages_role_name')}</span>}
            name="name"
            rules={[
              { required: true },
              { min: 1, max: 20, message: t('error_input_length_limit', { min: 1, max: 20 }) }
            ]}
          >
            <RInput className="!w-[320px]" />
          </RForm.Item>
        </Block>
        <Block title={t('pages_role_permissionSetting')}>
          <RForm.Item name="permissions" rules={[{ required: true }]}>
            <PermissionSelect data={permissionsData?.data ?? []} />
          </RForm.Item>
        </Block>
      </div>
    </FormModal>
  );
};

export default RoleModal;
