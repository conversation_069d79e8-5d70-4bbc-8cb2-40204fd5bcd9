import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { deleteRole, getRoleList } from '@/api/admin';
import RButton from '@/components/RButton';
import RTable from '@/components/RTable';
import useConfirmModal from '@/hooks/useConfirmModal';
import usePagination from '@/hooks/usePagination';
import TableSearchLayout from '@/layout/TableSearchLayout';
import { Admin, Permission, Role } from '@/types/admin';
import { formatTime } from '@/utils/time';

import RoleAdminModal from './RoleAdminModal';
import RoleModal, { type FormValue as RoleFormValue } from './RoleModal';

type RoleAdminModalState = {
  open: boolean;
  roleName: string;
  adminNameList: string[];
};

const ActionButtons = ({
  data,
  onEdit,
  onDelete,
  onCopy
}: {
  data: Role;
  onEdit: (data: Role) => void;
  onDelete: (data: Role) => void;
  onCopy: (data: Role) => void;
}) => {
  const { t } = useTranslation();

  const handleCopy = (data: Role) => {
    const copyData = { ...data };
    delete copyData.id;
    onCopy(copyData);
  };

  const handleDelete = () => {
    onDelete(data);
  };

  return (
    <div className="flex gap-1">
      <RButton
        size="small"
        variant="outlined"
        color="primary"
        type="link"
        onClick={() => onEdit(data)}
      >
        {t('common_edit')}
      </RButton>
      <RButton
        size="small"
        variant="outlined"
        color="green"
        type="link"
        onClick={() => handleCopy(data)}
      >
        {t('common_copy')}
      </RButton>
      <RButton size="small" variant="outlined" color="red" type="link" onClick={handleDelete}>
        {t('common_delete')}
      </RButton>
    </div>
  );
};

const getLeafPermissionIdList = (data: Permission): number[] => {
  if (data?.permissions?.length) {
    return data?.permissions?.map((el) => getLeafPermissionIdList(el)).flat();
  }
  return [data.id];
};

const RolePage = () => {
  const [open, setOpen] = useState(false);
  const { t } = useTranslation();
  const { page, setPage, limit, setLimit } = usePagination({});
  const [initialValues, setInitialValues] = useState<RoleFormValue | undefined>(undefined);
  const { confirmModal } = useConfirmModal();

  const [roleAdminModalState, setRoleAdminModalState] = useState<RoleAdminModalState>({
    open: false,
    roleName: '',
    adminNameList: []
  });

  const queryClient = useQueryClient();
  const { data, isPending } = useQuery({
    queryKey: ['roleList', { page, limit }],
    queryFn: () =>
      getRoleList({
        page: page,
        limit: limit
      })
  });

  const { mutate: deleteRoleMutation, isPending: isEditStatusPending } = useMutation({
    mutationFn: deleteRole,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['roleList'] });
    }
  });

  const handleCloseModal = () => {
    setOpen(false);
    setInitialValues(undefined);
  };

  const handleEdit = (data: Role) => {
    setInitialValues({
      ...data,
      permissions:
        data.permissions.map((permission) => getLeafPermissionIdList(permission)).flat() ?? []
    });
    setOpen(true);
  };

  const handleCopy = (data: Role) => {
    setInitialValues({
      ...data,
      permissions:
        data.permissions.map((permission) => getLeafPermissionIdList(permission)).flat() ?? []
    });
    setOpen(true);
  };

  const openDeleteConfirm = (data: Role) => {
    if (!data.id) return;

    confirmModal({
      content: t('common_confirm_delete_name', { name: data.name }),
      onOk: () => {
        deleteRoleMutation({ id: data.id! });
      }
    });
  };

  const actionColumn = {
    title: 'common_action',
    render: (_: unknown, data: Role) => (
      <ActionButtons
        data={data}
        onEdit={handleEdit}
        onCopy={handleCopy}
        onDelete={openDeleteConfirm}
      />
    )
  };

  const handleOpenRoleAdminModal = (name: string, admins: Admin[]) => {
    setRoleAdminModalState({
      open: true,
      roleName: name,
      adminNameList: admins.map((admin) => admin.account)
    });
  };

  const handleCloseRoleAdminModal = () => {
    setRoleAdminModalState({
      open: false,
      roleName: '',
      adminNameList: []
    });
  };

  const columns = [
    {
      title: 'pages_role_name',
      dataIndex: 'name'
    },
    {
      title: 'pages_role_admin',
      dataIndex: 'admins',
      render: (admins: Admin[], data: Role) => (
        <RButton type="link" onClick={() => handleOpenRoleAdminModal(data.name, admins)}>
          {admins.length}
        </RButton>
      )
    },
    {
      title: 'common_addTime',
      dataIndex: 'createdAt',
      render: (createdAt: number) => formatTime(createdAt)
    },
    {
      title: 'pages_admin_createdBy',
      dataIndex: 'createdBy'
    },
    {
      title: 'common_lastEditTime',
      dataIndex: 'updatedAt',
      render: (updatedAt: number) => formatTime(updatedAt)
    },
    {
      title: 'pages_admin_updatedBy',
      dataIndex: 'updatedBy'
    }
  ];

  const tableColumns = [...columns, actionColumn].map((column) => ({
    ...column,
    title: t(column.title)
  }));

  const handleChangePage = (page: number, pageSize: number) => {
    setPage(page);
    setLimit(pageSize);
  };

  return (
    <TableSearchLayout>
      <RButton className="mb-4" type="primary" onClick={() => setOpen(true)}>
        {t('common_add_name', { name: t('pages_role') })}
      </RButton>
      <RTable
        loading={isPending || isEditStatusPending}
        rowKey="id"
        dataSource={data?.data?.data || []}
        columns={tableColumns}
        pagination={{
          current: page,
          pageSize: limit,
          total: data?.data?.total || 0,
          showSizeChanger: true,
          onChange: handleChangePage
        }}
      />
      <RoleModal open={open} initialValues={initialValues} onClose={handleCloseModal} />
      <RoleAdminModal
        open={roleAdminModalState?.open}
        roleName={roleAdminModalState?.roleName}
        adminNameList={roleAdminModalState?.adminNameList}
        onClose={() => handleCloseRoleAdminModal()}
      />
    </TableSearchLayout>
  );
};

export default RolePage;
