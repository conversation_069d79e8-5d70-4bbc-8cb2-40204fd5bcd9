// import { Admin } from '@/types/admin';
import CopyIcon from '@/components/CopyIcon';
import RList from '@/components/RList';
import RModal from '@/components/RModal';

type RoleAdminModalProps = {
  open: boolean;
  roleName: string;
  adminNameList: string[];
  onClose: () => void;
};

const titleClassName = 'text-text-secondary';

const AdminItem = ({ adminAccount }: { adminAccount: string }) => {
  return (
    <RList.Item className="flex !justify-start gap-2 px-5 py-2">
      <CopyIcon text={adminAccount} width={12} height={12} />
      <span>{adminAccount}</span>
    </RList.Item>
  );
};

const RoleAdminModal = ({ open, onClose, roleName, adminNameList }: RoleAdminModalProps) => {
  const { t } = useTranslation();

  const handleCloseModal = () => {
    console.log('close');
    onClose();
  };

  return (
    <RModal
      title={t('pages_role_admin')}
      open={open}
      okButtonProps={{ show: false }}
      cancelButtonProps={{ text: t('common_close'), show: true }}
      onCancel={handleCloseModal}
    >
      <div className="flex flex-col gap-4">
        <div>
          <span className={titleClassName}>{t('pages_role_name')}: </span> {roleName}
        </div>
        <div>
          <div className={`${titleClassName} mb-1`}>{t('pages_role_adminList')}: </div>
          <RList
            className="h-[400px] max-h-[500px] scroll-list"
            size="small"
            bordered
            header={<div>{t('common_account')}</div>}
            dataSource={adminNameList || []}
            renderItem={(admin) => <AdminItem adminAccount={admin} />}
          />
        </div>
      </div>
    </RModal>
  );
};

export default RoleAdminModal;
