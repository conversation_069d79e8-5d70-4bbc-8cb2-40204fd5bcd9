import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import dayjs from 'dayjs';

import { deleteRecordExport, ExportStatus, getRecordExport, type RecordExport } from '@/api/admin';
import InfoIcon from '@/assets/img/icon/info.svg?react';
import RButton from '@/components/RButton';
import { RFormItem } from '@/components/RForm';
import RInput from '@/components/RInput';
import RTable from '@/components/RTable';
import SearchForm from '@/components/SearchForm';
import StatusLabel from '@/components/StatusLabel';
import useConfirmModal from '@/hooks/useConfirmModal';
import usePagination from '@/hooks/usePagination';
import TableSearchLayout from '@/layout/TableSearchLayout';
import { formatTime } from '@/utils/time';

type SearchFormValues = {
  id?: number;
  account?: string;
  start?: number;
  end?: number;
};
const defaultToday = [dayjs().startOf('day').valueOf(), dayjs().endOf('day').valueOf()];

const STATUS_MAP = {
  [ExportStatus.PROCESSING]: {
    label: 'pages_agent_recordExport_process',
    color: 'var(--tag-bg-processing)',
    textColor: 'var(--color-yellow)'
  },
  [ExportStatus.COMPLETED]: {
    label: 'pages_agent_recordExport_completed',
    color: 'var(--tag-bg-enabled)',
    textColor: 'var(--color-success)'
  },
  [ExportStatus.DELETED]: {
    label: 'common_delete_success',
    color: 'var(--tag-bg-disabled)',
    textColor: 'var(--color-warning)'
  },
  [ExportStatus.ERROR]: {
    label: 'pages_agent_recordExport_abnormal',
    color: 'var(--tag-bg-disabled)',
    textColor: 'var(--color-warning)'
  }
} as const;

const columns = [
  {
    title: 'common_id',
    dataIndex: 'id',
    width: 150
  },
  {
    title: 'common_data',
    dataIndex: 'opPage',
    width: 150
  },
  {
    title: 'pages_agent_recordExport_period',
    dataIndex: 'startTime',
    render: (startTime: number, record: RecordExport) => {
      return record.startTime ? `${formatTime(startTime)} ~ ${formatTime(record.endTime)} ` : '-';
    },
    width: 200
  },
  {
    title: 'common_addTime',
    dataIndex: 'createdAt',
    width: 180,
    render: (createdAt: number) => formatTime(createdAt)
  },
  {
    title: 'common_updatedTime',
    dataIndex: 'updatedAt',
    width: 180,
    render: (createdAt: number) => formatTime(createdAt)
  },
  {
    title: 'common_operator',
    dataIndex: 'account',
    width: 120
  },
  {
    title: 'common_status',
    dataIndex: 'status',
    width: 120,
    render: (status: keyof typeof STATUS_MAP) => (
      <StatusLabel
        status={status}
        label={STATUS_MAP[status].label}
        color={STATUS_MAP[status].color}
        textColor={STATUS_MAP[status].textColor}
      />
    )
  }
];

const SearchFormWrap = ({
  onSearch,
  onReset
}: {
  onSearch: (values: SearchFormValues) => void;
  onReset: (values: SearchFormValues) => void;
}) => {
  const { t } = useTranslation();

  const handleSearch = (values: SearchFormValues) => {
    onSearch(values);
  };

  const handleReset = () => {
    onReset({
      start: defaultToday[0],
      end: defaultToday[1]
    });
  };

  return (
    <SearchForm<SearchFormValues> onSearch={handleSearch} onReset={handleReset} className="flex">
      <RFormItem name="id" label={t('common_id')}>
        <RInput className="!w-80 max-h-[32px]" />
      </RFormItem>
      <RFormItem name="account" label={`${t('common_operator')}`}>
        <RInput className="!w-80 max-h-[32px]" />
      </RFormItem>
    </SearchForm>
  );
};

const ActionButtons = ({
  ondownload,
  ondelete,
  isdeletePending
}: {
  ondownload: () => void;
  ondelete: () => void;
  isdeletePending: boolean;
}) => {
  const { t } = useTranslation();

  return (
    <div className="flex gap-1">
      <RButton
        size="small"
        variant="outlined"
        color="primary"
        type="link"
        onClick={() => ondownload()}
      >
        {t('common_download')}
      </RButton>
      <RButton
        size="small"
        variant="outlined"
        color="red"
        type="link"
        loading={isdeletePending}
        onClick={() => ondelete()}
      >
        {t('common_delete')}
      </RButton>
    </div>
  );
};

const RecordExportPage = () => {
  const { t } = useTranslation();
  const { page, setPage, limit, setLimit } = usePagination({});
  const [params, setParams] = useState<SearchFormValues>({});
  const queryClient = useQueryClient();
  const { confirmModal } = useConfirmModal();

  const { data, isPending } = useQuery({
    queryKey: ['exportRecordList', { page, limit, ...params }],
    queryFn: () => getRecordExport({ page, limit, ...params })
  });

  const { mutate: deleteRecordExportMutation, isPending: isDeletePending } = useMutation({
    mutationFn: (id: number) => deleteRecordExport({ id }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['exportRecordList'] });
    }
  });

  const actionColumn = {
    title: 'common_action',
    render: (_: unknown, data: RecordExport) =>
      data.status === 2 && (
        <ActionButtons
          ondownload={() => handleDownload(data)}
          ondelete={() => handleDelete(data)}
          isdeletePending={isDeletePending}
        />
      )
  };

  const openDeleteConfirm = (data: RecordExport) => {
    if (!data.id) return;
    confirmModal({
      content: t('common_confirm_delete_name', { name: data.id }),
      onOk: () => deleteRecordExportMutation(data.id)
    });
  };

  const handleDownload = (data: RecordExport) => {
    if (!data.link) return;
    const a = document.createElement('a');
    a.href = data.link;
    a.click();
  };

  const handleDelete = (data: RecordExport) => {
    openDeleteConfirm(data);
  };

  const tableColumns = [...columns, actionColumn].map((column) => ({
    ...column,
    title: t(column.title)
  }));

  const handleChangePage = (page: number, pageSize: number) => {
    setPage(page);
    setLimit(pageSize);
  };

  const handleSearch = (values: SearchFormValues) => {
    setPage(1);
    setParams(values);
  };

  return (
    <TableSearchLayout
      searchFields={<SearchFormWrap onSearch={handleSearch} onReset={handleSearch} />}
    >
      <div className="text-text mb-4 bg-[#FF83171A] border border-[#FF8317] rounded py-1.5 px-2.5 w-fit">
        <div className="flex gap-1">
          <InfoIcon className="w-4.5 h-4.5 fill-[#FF8317]" />
          <p>{t('pages_agent_recordExport_description')}</p>
        </div>
      </div>
      <RTable
        loading={isPending}
        rowKey="id"
        dataSource={data?.data?.data || []}
        columns={tableColumns}
        scroll={{ x: 600 }}
        pagination={{
          current: page,
          pageSize: limit,
          total: data?.data?.total || 0,
          showSizeChanger: true,
          onChange: handleChangePage
        }}
      />
    </TableSearchLayout>
  );
};

export default RecordExportPage;
