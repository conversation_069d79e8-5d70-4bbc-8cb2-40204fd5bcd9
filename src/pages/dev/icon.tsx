import { useEffect, useState } from 'react';

const icons = import.meta.glob('../../assets/img/icon/*.svg', { query: '?url', import: 'default' });

const IconGallery = () => {
  const [iconList, setIconList] = useState<Array<{ name: string; url: string }>>([]);

  useEffect(() => {
    const loadIcons = async () => {
      const entries = await Promise.all(
        Object.entries(icons).map(async ([path, importer]) => {
          const url = await (importer as () => Promise<string>)();
          const name = path.split('/').pop()!;
          return { name, url };
        })
      );
      setIconList(entries);
    };

    loadIcons();
  }, []);

  return (
    <div className="grid grid-cols-6 gap-4 p-8">
      {iconList.map(({ name, url }) => (
        <div key={name} className="text-center">
          <img src={url} alt={name} className="w-12 h-12 mx-auto" />
          <p className="mt-2 text-xs">{name}</p>
        </div>
      ))}
    </div>
  );
};

export default IconGallery;
