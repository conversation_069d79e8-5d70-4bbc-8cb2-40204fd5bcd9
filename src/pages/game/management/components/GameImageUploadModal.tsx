import { useMutation } from '@tanstack/react-query';
import { useQueryClient } from '@tanstack/react-query';

import { uploadGameImage } from '@/api/game';
import FormModal from '@/components/FormModal';
import RForm from '@/components/RForm';
import RUploader from '@/components/RUploader';
import { Game } from '@/types/game';

import { gameListKeys } from '../hooks';

const uploadImageList = [
  {
    key: 'icon',
    title: 'game_general_picture',
    width: 400,
    height: 400,
    isRequired: true
  },
  {
    key: 'rkmv',
    title: 'game_potrait_picture',
    width: 436,
    height: 508
  },
  {
    key: 'rkmh',
    title: 'game_banner_picture',
    width: 688,
    height: 208
  }
];

const GameImageUploadModal = ({
  game,
  open,
  onClose
}: {
  game: Game;
  open: boolean;
  onClose: () => void;
}) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const uploadGameImageMutation = useMutation({
    mutationFn: uploadGameImage,
    onSuccess: () => {
      onClose();
      queryClient.invalidateQueries({ queryKey: gameListKeys.lists() });
    }
  });

  const [form] = RForm.useForm();
  const icon = RForm.useWatch('icon', form);
  const rkmv = RForm.useWatch('rkmv', form);
  const rkmh = RForm.useWatch('rkmh', form);

  const preview = {
    icon: icon ? URL.createObjectURL(icon) : game?.icon,
    rkmv: rkmv ? URL.createObjectURL(rkmv) : game?.rkmv,
    rkmh: rkmh ? URL.createObjectURL(rkmh) : game?.rkmh
  };

  const handleSubmit = (values: { id: string; icon?: File; rkmv?: File; rkmh?: File }) => {
    uploadGameImageMutation.mutate(values);
  };

  return (
    <FormModal
      open={open}
      onSubmit={handleSubmit}
      onClose={onClose}
      title={t('game_upload_game_image')}
      width={780}
      form={form}
    >
      <p className="mb-4 text-xs">
        <span className="text-text-secondary">
          {t('common_supplier')}/{t('game')}:
        </span>
        {game?.provider} / {game?.name}
      </p>
      <div className="grid grid-cols-2 gap-4 text-xs">
        <RForm.Item name="id" noStyle initialValue={game?.id}></RForm.Item>

        {uploadImageList.map((item) => (
          <div key={item.key}>
            <p className="text-text-secondary">
              {item.isRequired && <span className="mr-1 text-warning">*</span>}
              {t(item.title)}
            </p>
            <RForm.Item
              name={item.key}
              extra={
                <p className="flex flex-col text-xs text-warning">
                  <span>{t('game_upload_game_image_description')}</span>
                  <span>
                    {t('game_upload_game_image_size', { width: item.width, height: item.height })}
                  </span>
                </p>
              }
            >
              <RUploader accept={'image/webp,video/mp4'} />
            </RForm.Item>
            <p className="text-text-secondary">{t('common_preview')}</p>
            {preview[item.key as keyof typeof preview] ? (
              <img
                src={preview[item.key as keyof typeof preview] as string}
                alt={item.title}
                className="object-contain max-w-[200px] max-h-[200px]"
              />
            ) : (
              '-'
            )}
          </div>
        ))}
      </div>
    </FormModal>
  );
};

export default GameImageUploadModal;
