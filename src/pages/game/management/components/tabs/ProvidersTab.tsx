import { useMutation } from '@tanstack/react-query';
import React from 'react';

import { editGameProviderStatus } from '@/api/game';
import { ActionButtons } from '@/components/ActionButtons';
import RTable from '@/components/RTable';
import StatusLabel from '@/components/StatusLabel';
import useConfirmModal from '@/hooks/useConfirmModal';
import { GameProvider, GameProviderWalletType } from '@/types/game';
import { formatTime } from '@/utils/time';

import { useGameProviderQuery } from '../../hooks';

interface ProvidersTabProps {
  isActive: boolean;
}

const ProvidersTab: React.FC<ProvidersTabProps> = ({ isActive }) => {
  const { t } = useTranslation();
  const { confirmModal } = useConfirmModal();

  // Use the custom providers hook
  const providerQuery = useGameProviderQuery({
    enabled: isActive
  });

  const updateStatusMutation = useMutation({
    mutationFn: editGameProviderStatus,
    onSuccess: () => {
      providerQuery.refetch();
    }
  });

  const onUpdateStatus = (id: string, currentStatus: number) => {
    // Implement status update logic here
    confirmModal({
      content:
        currentStatus === 1
          ? t('common_confirm_change_status_disable', { name: t('game_provider') })
          : t('common_confirm_change_status_enable', { name: t('game_provider') }),
      onOk: () => {
        updateStatusMutation.mutate({ id, status: currentStatus === 1 ? 0 : 1 });
      }
    });
  };

  const renderWalletType = (walletType: GameProviderWalletType) => {
    switch (walletType) {
      case GameProviderWalletType.SINGLE:
        return t('game_provider_wallet_type_single');
      case GameProviderWalletType.TRANSFER:
        return t('game_provider_wallet_type_transfer');
      default:
        return '';
    }
  };

  // Define table columns to match wireframe design
  const columns = [
    {
      title: t('game_provider_id'), // 供應商ID // Provider ID
      dataIndex: 'id',
      key: 'id',
      width: 100
    },
    {
      title: t('game_provider'), // 供應商名稱 // Provider Name
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: t('game_provider_wallet_type'), // 運作模式 // Game Type
      dataIndex: 'walletType',
      key: 'walletType',
      render: (walletType: GameProviderWalletType) => renderWalletType(walletType)
    },
    {
      title: t('common_status'), // 狀態 // Status
      dataIndex: 'status',
      key: 'status',
      render: (status: number) => <StatusLabel status={status} />
    },
    {
      title: t('common_lastEditAdmin'), // 最後編輯管理員 // Last Modified User
      dataIndex: 'updatedBy',
      key: 'updatedBy'
    },
    {
      title: t('common_lastEditTime'), // 最後編輯時間 // Last Modified Time
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      render: (updatedAt: number) => <div>{formatTime(updatedAt)}</div>
    },
    {
      title: t('common_action'), // 功能 // Action
      dataIndex: 'action',
      key: 'action',
      render: (_: unknown, record: GameProvider) => (
        <ActionButtons
          data={record}
          buttons={['status']}
          onEditStatus={() => onUpdateStatus(record.id, record.status)}
        />
      )
    }
  ];

  return (
    <RTable
      loading={providerQuery.isPending}
      rowKey="id"
      dataSource={providerQuery.data || []}
      columns={columns}
    />
  );
};

export default ProvidersTab;
