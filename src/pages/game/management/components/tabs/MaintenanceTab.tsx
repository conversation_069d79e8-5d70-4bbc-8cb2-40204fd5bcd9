import { useMutation } from '@tanstack/react-query';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import {
  cancelGameMaintenance,
} from '@/api/game';
import OperatorCell from '@/components/cells/OperatorCell';
import RButton from '@/components/RButton';
import RTable from '@/components/RTable';
import RTag from '@/components/RTag';
import useConfirmModal from '@/hooks/useConfirmModal';
import TableSearchLayout from '@/layout/TableSearchLayout';
import { GameMaintenance, GameMaintenanceMode, GameMaintenanceStatus } from '@/types/game';
import { formatTime } from '@/utils/time';

import { useGameMaintenance } from '../../hooks';
import MaintenanceModal from '../../MaintenanceModal';
import { MaintenanceSearchParams, SearchFieldConfig } from '../../types';
import GameManagementSearchForm from '../GameManagementSearchForm';

interface MaintenanceTabProps {
  isActive: boolean;
}

const statusTagMap = {
  [GameMaintenanceStatus.UNDER_MAINTENANCE]: 'red',
  [GameMaintenanceStatus.SCHEDULED]: 'orange',
  [GameMaintenanceStatus.ENDED]: 'gray',
  [GameMaintenanceStatus.CANCELED]: 'gray'
};

const MaintenanceTab: React.FC<MaintenanceTabProps> = ({ isActive }) => {
  const { t } = useTranslation();
  const { confirmModal } = useConfirmModal();
  const [maintenanceModalOpen, setMaintenanceModalOpen] = useState(false);

  // Search state
  const [searchParams, setSearchParams] = useState<MaintenanceSearchParams>({});

  // Maintenance data query using the new hook
  const {
    data: maintenanceResponse,
    isLoading,
    refetch: refetchMaintenance
  } = useGameMaintenance({
    searchParams,
    enabled: isActive
  });

  const cancelMutation = useMutation({
    mutationFn: cancelGameMaintenance,
    onSuccess: () => {
      refetchMaintenance();
    }
  });

  const handleAddMaintenance = () => {
    setMaintenanceModalOpen(true);
  };

  const onPressCancelMaintenance = (id: string) => {
    confirmModal({
      content: t('common_confirm_cancel_maintenance'),
      onOk: () => {
        cancelMutation.mutate({ id });
      }
    });
  };

  // Search handlers
  const handleSearch = (values: MaintenanceSearchParams) => {
    // Remove 'all' values from search params
    const filteredValues = Object.entries(values).reduce((acc, [key, value]) => {
      if (value !== 'all') {
        acc[key] = value;
      }
      return acc;
    }, {} as MaintenanceSearchParams);
    setSearchParams(filteredValues);
  };

  const handleReset = () => {
    setSearchParams({});
  };

  const data = maintenanceResponse as GameMaintenance[];
  const loading = isLoading;

  // Helper function to format maintenance mode
  const formatMaintenanceMode = (record: GameMaintenance) => {
    // Map the mode number to display text using translation keys
    const modeMap: Record<number, string> = {
      [GameMaintenanceMode.NOW]: t('maintenance_mode_now'),
      [GameMaintenanceMode.SCHEDULE]: t('maintenance_mode_schedule')
    };
    return modeMap[record.mode];
  };

  const formatStatus = (status: GameMaintenanceStatus) => {
    // Map the status number to display text using translation keys
    const statusMap: Record<number, string> = {
      [GameMaintenanceStatus.UNDER_MAINTENANCE]: t('maintenance_mode_undermaintenance'),
      [GameMaintenanceStatus.SCHEDULED]: t('maintenance_mode_scheduled'),
      [GameMaintenanceStatus.ENDED]: t('maintenance_mode_ended'),
      [GameMaintenanceStatus.CANCELED]: t('maintenance_mode_canceled')
    };
    return statusMap[status];
  };

  // Define table columns based on wireframe
  const columns = [
    {
      title: t('game_provider'), // 供應商
      dataIndex: 'providerName',
      key: 'providerName'
    },
    {
      title: t('common_category'), // 分類
      dataIndex: 'categoryName',
      key: 'categoryName',
      render: (categoryName: string) => categoryName || '-'
    },
    {
      title: t('game_name'), // 遊戲
      dataIndex: 'gameName',
      key: 'gameName',
      render: (gameName: string) => gameName || '-'
    },
    {
      title: t('maintenance_mode'), // 維護模式 (Maintenance Mode)
      dataIndex: 'mode',
      key: 'mode',
      render: (_: unknown, record: GameMaintenance) => formatMaintenanceMode(record)
    },
    {
      title: t('maintenance_time'), // 時間
      dataIndex: 'timeRange',
      key: 'timeRange',
      render: (_: unknown, record: GameMaintenance) => (
        <div>
          {record.mode === GameMaintenanceMode.NOW ? (
            <div>-</div>
          ) : (
            <>
              <div>{formatTime(record.timeStart)}~</div>
              <div>{formatTime(record.timeEnd)}</div>
            </>
          )}
        </div>
      )
    },
    {
      title: t('common_status'), // 狀態
      dataIndex: 'status',
      key: 'status',
      render: (status: GameMaintenanceStatus) => (
        <RTag color={statusTagMap[status]}>{formatStatus(status)}</RTag>
      )
    },
    {
      title: t('maintenance_add_time_admin'), // 新增管理員/時間
      dataIndex: 'createdInfo',
      key: 'createdInfo',
      render: (_: unknown, record: GameMaintenance) => (
        <OperatorCell record={{ updatedBy: record.createdBy, updatedAt: record.createdAt }} />
      )
    },
    {
      title: t('maintenance_last_time_admin'), // 最後編輯管理員/時間
      dataIndex: 'updatedInfo',
      key: 'updatedInfo',
      render: (_: unknown, record: GameMaintenance) => (
        <OperatorCell record={{ updatedBy: record.updatedBy, updatedAt: record.updatedAt }} />
      )
    },

    {
      title: t('common_action'), // 功能
      dataIndex: 'action',
      key: 'action',
      render: (_: unknown, record: GameMaintenance) => {
        if (
          record.status === GameMaintenanceStatus.CANCELED ||
          record.status === GameMaintenanceStatus.ENDED
        ) {
          return null;
        }
        return (
          <RButton
            size="small"
            variant="outlined"
            color="red"
            loading={cancelMutation.isPending}
            onClick={() => onPressCancelMaintenance(record.id)}
          >
            {t('maintenance_mode_cancel')}
          </RButton>
        );
      }
    }
  ];

  // Define search fields based on wireframe
  const searchFields: SearchFieldConfig[] = [
    {
      name: 'mode',
      type: 'select',
      translationKey: 'game_maintenance_mode',
      defaultValue: 'all',
      options: [
        { label: t('maintenance_mode_all'), value: 'all' }, // 全部
        { label: t('maintenance_mode_now'), value: GameMaintenanceMode.NOW }, // 立即維運
        { label: t('maintenance_mode_schedule'), value: GameMaintenanceMode.SCHEDULE } // 指定時間維運
      ]
    },
    {
      name: 'status',
      type: 'select',
      translationKey: 'common_status', // 狀態
      defaultValue: 'all',
      options: [
        // 狀態 1:維護中 2:已排程 3:已結束 4:已取消
        { label: t('common_all'), value: 'all' }, // 全部
        {
          label: t('maintenance_mode_undermaintenance'),
          value: GameMaintenanceStatus.UNDER_MAINTENANCE
        }, // 維運中
        { label: t('maintenance_mode_scheduled'), value: GameMaintenanceStatus.SCHEDULED }, // 已排程
        { label: t('maintenance_mode_ended'), value: GameMaintenanceStatus.ENDED } // 已結束
      ]
    }
  ];

  return (
    <TableSearchLayout
      hasTab={true}
      searchFields={
        <GameManagementSearchForm
          fields={searchFields}
          onSearch={handleSearch}
          onReset={handleReset}
        />
      }
    >
      <RButton className="mb-4" type="primary" onClick={handleAddMaintenance}>
        {t('add_game_maintenance')}
      </RButton>
      <RTable loading={loading} rowKey="id" dataSource={data} columns={columns} />
      {maintenanceModalOpen && (
        <MaintenanceModal
          open={maintenanceModalOpen}
          onClose={() => {
            setMaintenanceModalOpen(false);
          }}
        />
      )}
    </TableSearchLayout>
  );
};

export default MaintenanceTab;
