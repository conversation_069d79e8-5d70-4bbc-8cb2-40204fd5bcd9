import { useMutation } from '@tanstack/react-query';
import React from 'react';
import { useTranslation } from 'react-i18next';

import { editGameCategoryStatus } from '@/api/game';
import { ActionButtons } from '@/components/ActionButtons';
import RTable from '@/components/RTable';
import StatusLabel from '@/components/StatusLabel';
import useConfirmModal from '@/hooks/useConfirmModal';
import { GameCategory } from '@/types/game';
import { formatTime } from '@/utils/time';

import { useGameCategoryQuery } from '../../hooks';

interface CategoriesTabProps {
  isActive: boolean;
}

const CategoriesTab: React.FC<CategoriesTabProps> = ({ isActive }) => {
  const { t } = useTranslation();
  const { confirmModal } = useConfirmModal();

  // Use the custom categories hook
  const categoryQuery = useGameCategoryQuery({
    enabled: isActive
  });

  const updateStatusMutation = useMutation({
    mutationFn: editGameCategoryStatus,
    onSuccess: () => {
      categoryQuery.refetch();
    }
  });

  const onUpdateStatus = (id: string, currentStatus: number) => {
    confirmModal({
      content:
        currentStatus === 1
          ? t('common_confirm_change_status_disable', { name: t('game_category') })
          : t('common_confirm_change_status_enable', { name: t('game_category') }),
      onOk: () => {
        updateStatusMutation.mutate({ id, status: currentStatus === 1 ? 0 : 1 });
      }
    });
  };

  // Define table columns to match wireframe design
  const columns = [
    {
      title: t('game_category_name'), // 分類名稱 // Category Name
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: t('common_status'), // 狀態 // Status
      dataIndex: 'status',
      key: 'status',
      render: (status: number) => <StatusLabel status={status} />
    },
    {
      title: t('common_lastEditAdmin'), // 最後編輯管理員
      dataIndex: 'updatedBy',
      key: 'updatedBy'
    },
    {
      title: t('common_lastEditTime'), // 最後編輯時間
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      render: (updatedAt: number) => <div>{formatTime(updatedAt)}</div>
    },
    {
      title: t('common_action'), // 功能 // Action
      dataIndex: 'action',
      key: 'action',
      render: (_: unknown, record: GameCategory) => (
        <ActionButtons
          data={record}
          buttons={['status']}
          onEditStatus={() => onUpdateStatus(record.id, record.status)}
          isEditStatusPending={updateStatusMutation.isPending}
        />
      )
    }
  ];

  return (
    <RTable
      rowKey="id"
      loading={categoryQuery.isPending}
      dataSource={categoryQuery.data || []}
      columns={columns}
    />
  );
};

export default CategoriesTab;
