import { useMutation } from '@tanstack/react-query';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { editGameStatus } from '@/api/game';
import OperatorCell from '@/components/cells/OperatorCell';
import RButton from '@/components/RButton';
import RTable from '@/components/RTable';
import StatusButton from '@/components/StatusButton';
import StatusLabel from '@/components/StatusLabel';
import useConfirmModal from '@/hooks/useConfirmModal';
import TableSearchLayout from '@/layout/TableSearchLayout';
import { Game } from '@/types/game';

import GameImageReviewModal from '../../GameImageReviewModal';
import GameLimitModal from '../../GameLimitModal';
import { useGameCategoryQuery, useGameList, useGameProviderQuery, useVipListQuery } from '../../hooks';
import { GameListInitialFilter } from '../../hooks/useGameList';
import { GameSearchParams, SearchFieldConfig } from '../../types';
import GameBatchImageUploadModal from '../GameBatchImageUploadModal';
import GameImageUploadModal from '../GameImageUploadModal';
import GameManagementSearchForm from '../GameManagementSearchForm';

interface GamesTabProps {
  isActive: boolean;
}

const GamesTab: React.FC<GamesTabProps> = ({ isActive }) => {
  const { t } = useTranslation();
  const { confirmModal } = useConfirmModal();

  // Search state
  const [searchParams, setSearchParams] = useState<GameSearchParams>(GameListInitialFilter);
  const [editingStartConditions, setEditingStartConditions] = useState<Game | null>(null);
  const [previewingGameImage, setPreviewingGameImage] = useState<Game | null>(null);
  const [isOpenBatchImageUploadModal, setIsOpenBatchImageUploadModal] = useState(false);
  const [uploadingImageGame, setUploadingImageGame] = useState<Game | null>(null);

  // Game data query using the new hook
  const gameListQuery = useGameList({
    searchParams,
    enabled: isActive
  });
  const gameProviderQuery = useGameProviderQuery({
    enabled: isActive
  });
  const gameCategoryQuery = useGameCategoryQuery({
    enabled: isActive
  });

  const vipListQuery = useVipListQuery({
    enabled: isActive
  }
  );

  const updateStatusMutation = useMutation({
    mutationFn: editGameStatus,
    onSuccess: () => {
      gameListQuery.refetch();
    }
  });

  const onUpdateStatus = (id: string, currentStatus: number) => {
    confirmModal({
      content:
        currentStatus === 1
          ? t('common_confirm_change_status_disable', { name: t('game') })
          : t('common_confirm_change_status_enable', { name: t('game') }),
      onOk: () => {
        updateStatusMutation.mutate({ id, status: currentStatus === 1 ? 0 : 1 });
      }
    });
  };

  // Search handlers
  const handleSearch = (values: GameSearchParams) => {
    // Remove 'all' values from search params but preserve pagination
    const filteredValues = Object.entries(values).reduce((acc, [key, value]) => {
      if (value !== 'all') {
        acc[key] = value;
      }
      return acc;
    }, {} as GameSearchParams);

    // Preserve pagination and reset to first page for new search
    setSearchParams({
      ...filteredValues,
      page: 1,
      limit: searchParams.limit
    });
  };

  const handleReset = () => {
    setSearchParams(GameListInitialFilter);
  };

  const gameList = gameListQuery.data?.data as Game[];
  const total = gameListQuery.data?.total || 0;
  const loading = gameListQuery.isPending;

  // Helper function to format JP support
  const formatJpSupport = (useJp: number) => {
    return useJp === 1 ? t('common_yes') : t('common_no');
  };

  // Helper function to format multi-open support
  const formatMultiSupport = (useMulti: number) => {
    return useMulti === 1 ? t('common_yes') : t('common_no');
  };

  const formatCondition = (conditions: Game['conditions']) => {
    if (!conditions?.vipLimit) return '-';
    const vip = vipListQuery.data?.find((vip) => vip.level === conditions?.vipLimit);
    return vip?.name || '-';
  };

  // Define table columns based on wireframe and API data structure
  const columns = [
    {
      title: t('game_provider'), // 供應商
      dataIndex: 'provider',
      key: 'provider'
    },
    {
      title: t('game_category_name'), // 分類
      dataIndex: 'category',
      key: 'category'
    },
    {
      title: t('game_name'), // 遊戲名稱
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: t('game_icon'), // 遊戲圖
      dataIndex: 'icon',
      key: 'icon',
      render: (_: string, record: Game) => (
        <a
          href="#"
          onClick={(e) => {
            e.preventDefault();
            setPreviewingGameImage(record);
          }}
        >
          {t('common_check')}
        </a>
      )
    },
    {
      title: t('game_conditions'), // 啟動條件
      dataIndex: 'conditions',
      key: 'conditions',
      render: (conditions: Game['conditions']) => formatCondition(conditions)
    },
    {
      title: t('game_jp_support'), // 支援JP
      dataIndex: 'useJp',
      key: 'useJp',
      render: formatJpSupport
    },
    {
      title: t('game_multi_support'), // 支援多開
      dataIndex: 'useMulti',
      key: 'useMulti',
      render: formatMultiSupport
    },
    {
      title: t('common_status'), // 狀態
      dataIndex: 'status',
      key: 'status',
      render: (status: number) => <StatusLabel status={status} />
    },
    {
      title: t('game_last_edit_admin'), // 最後編輯管理員/時間
      dataIndex: 'updatedInfo',
      key: 'updatedInfo',
      render: (_: unknown, record: Game) => (
        <OperatorCell
          record={{ updatedBy: record.updatedBy, updatedAt: Number(record.updatedAt) }}
        />
      )
    },
    {
      title: t('common_action'), // 功能
      dataIndex: 'action',
      key: 'action',
      render: (_: unknown, record: Game) => (
        <div className="flex gap-1">
          <StatusButton
            status={record.status}
            isEditStatusPending={
              updateStatusMutation.isPending && record.id === updateStatusMutation.variables?.id
            }
            onEditStatus={() => onUpdateStatus(record.id, record.status)}
          />
          <RButton
            size="small"
            variant="outlined"
            color="primary"
            type="link"
            onClick={() => setUploadingImageGame(record)}
          >
            {t('game_upload_game_image')}
          </RButton>
          <RButton
            size="small"
            variant="outlined"
            color="primary"
            type="link"
            onClick={() => setEditingStartConditions(record)}
          >
            {t('game_start_conditions')}
          </RButton>
        </div>
      )
    }
  ];

  // Define search fields based on wireframe
  const searchFields: SearchFieldConfig[] = [
    {
      name: 'providerId',
      type: 'select',
      isLoading: gameProviderQuery.isPending,
      translationKey: 'game_provider', // 供應商
      defaultValue: 'all',
      options: [
        { label: t('common_all'), value: 'all' },
        ...(gameProviderQuery.data?.map((provider) => ({
          label: provider.name,
          value: provider.id
        })) || [])
      ]
    },
    {
      name: 'categoryId',
      type: 'select',
      isLoading: gameCategoryQuery.isPending,
      translationKey: 'game_category', // 分類
      defaultValue: 'all',
      options: [
        { label: t('common_all'), value: 'all' },
        ...(gameCategoryQuery.data?.map((category) => ({
          label: category.name,
          value: category.id
        })) || [])
      ]
    },
    {
      name: 'status',
      type: 'select',
      translationKey: 'common_status', // 狀態
      defaultValue: 'all',
      options: [
        { label: t('common_all'), value: 'all' },
        { label: t('common_enabled'), value: 1 },
        { label: t('common_disabled'), value: 0 }
      ]
    },
    {
      name: 'noIcon',
      type: 'select',
      translationKey: 'game_icon_status', // 是否缺少一般遊戲圈
      defaultValue: GameListInitialFilter.noIcon,
      options: [
        { label: t('common_yes'), value: 1 },
        { label: t('common_no'), value: 0 }
      ]
    },
    {
      name: 'name',
      type: 'input',
      translationKey: 'game_name_filter' // 遊戲名稱（模糊),
    }
  ];

  return (
    <TableSearchLayout
      hasTab={true}
      searchFields={
        <GameManagementSearchForm<GameSearchParams>
          fields={searchFields}
          onSearch={handleSearch}
          onReset={handleReset}
        />
      }
    >
      <RButton className="mb-4" type="primary" onClick={() => setIsOpenBatchImageUploadModal(true)}>
        {t('game_batch_upload')}
      </RButton>
      <RTable
        loading={loading}
        rowKey="id"
        dataSource={gameList}
        columns={columns}
        pagination={{
          current: searchParams.page,
          pageSize: searchParams.limit,
          total: total,
          showSizeChanger: true,
          onChange: (page, pageSize) => {
            setSearchParams((prev) => ({ ...prev, page, limit: pageSize }));
          }
        }}
      />
      {!!editingStartConditions && (
        <GameLimitModal
          open={!!editingStartConditions}
          gameId={editingStartConditions?.id}
          initialGameLimit={editingStartConditions?.conditions?.vipLimit}
          onClose={() => setEditingStartConditions(null)}
        />
      )}
      {!!previewingGameImage && (
        <GameImageReviewModal
          open={!!previewingGameImage}
          game={previewingGameImage}
          onClose={() => setPreviewingGameImage(null)}
        />
      )}
      <GameBatchImageUploadModal
        isOpen={isOpenBatchImageUploadModal}
        onClose={() => setIsOpenBatchImageUploadModal(false)}
      />
      {!!uploadingImageGame && (
        <GameImageUploadModal
          open={!!uploadingImageGame}
          game={uploadingImageGame}
          onClose={() => setUploadingImageGame(null)}
        />
      )}
    </TableSearchLayout>
  );
};

export default GamesTab;
