import { Tabs } from 'antd';
import clsx from 'clsx';
import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';

interface TabItem {
  key: string;
  labelKey: string; // Translation key
}

interface GameTabsProps {
  onActiveTabChange?: (activeTab: string) => void;
}

// Game management tabs configuration - moved outside component to avoid re-renders
const GAME_TABS: TabItem[] = [
  {
    key: 'maintenance',
    labelKey: 'game_maintenance'
  },
  {
    key: 'providers',
    labelKey: 'game_providers'
  },
  {
    key: 'categories',
    labelKey: 'game_categories'
  },
  {
    key: 'games',
    labelKey: 'games'
  }
];

const BASE_PATH = '/game/management';
const DEFAULT_TAB = 'maintenance';

/**
 * GameTabs component that handles game management tabs
 * Contains all tab logic and configuration internally
 */
const GameTabs: React.FC<GameTabsProps> = ({ onActiveTabChange }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { t } = useTranslation();

  // Get active tab from URL parameters
  const activeTab = useMemo(() => {
    const urlParams = new URLSearchParams(location.search);
    const tab = urlParams.get('tab');
    return tab || DEFAULT_TAB;
  }, [location.search]);

  // Generate tab items with translated labels
  const tabItems = useMemo(() => {
    return GAME_TABS.map((tab) => ({
      key: tab.key,
      label: t(tab.labelKey)
    }));
  }, [t]);

  // Handle tab change
  const handleTabChange = (key: string) => {
    const url = new URL(window.location.href);
    url.pathname = BASE_PATH;
    url.searchParams.set('tab', key);

    navigate(`${url.pathname}${url.search}`, { replace: false });
  };

  // Set default tab if none is specified
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const hasTabParam = urlParams.has('tab');

    if (!hasTabParam) {
      const url = new URL(window.location.href);
      url.pathname = BASE_PATH;
      url.searchParams.set('tab', DEFAULT_TAB);
      navigate(`${url.pathname}${url.search}`, { replace: true });
    }
  }, [location.search, location.pathname, navigate]);

  // Notify parent component of active tab changes
  useEffect(() => {
    if (onActiveTabChange) {
      onActiveTabChange(activeTab);
    }
  }, [activeTab, onActiveTabChange]);

  return (
    <div className="bg-white shadow-content">
      <div className="px-4">
        <Tabs
          activeKey={activeTab}
          onChange={handleTabChange}
          items={tabItems}
          className={clsx('game-tabs', 'game-management-tabs')}
        />
      </div>
    </div>
  );
};

export default GameTabs;
