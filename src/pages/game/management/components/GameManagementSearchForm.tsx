import { Col, Form, Row } from 'antd';
import { useTranslation } from 'react-i18next';

import RButton from '@/components/RButton';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RSelect from '@/components/RSelect';

import { SearchFieldConfig } from '../types';

interface GameManagementSearchFormProps<T = Record<string, any>> {
  fields: SearchFieldConfig[];
  onSearch: (values: T) => void;
  onReset: () => void;
}

/**
 * Generic search form component for game management
 * Renders different field types based on configuration
 */
const GameManagementSearchForm = <T extends Record<string, any>>({
  fields,
  onSearch,
  onReset
}: GameManagementSearchFormProps<T>) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  const handleSubmit = (values: Record<string, any>) => {
    // Remove empty values
    const filteredValues = Object.entries(values).reduce((acc, [key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        // @ts-ignore
        acc[key] = value;
      }
      return acc;
    }, {} as T);

    onSearch(filteredValues);
  };

  const handleReset = () => {
    form.resetFields();
    onReset();
  };

  useEffect(() => {
    // update default value for parent
    // make sure form is ready
    setTimeout(() => {
      onSearch(form.getFieldsValue());
    }, 100);
  }, []);

  const renderField = (field: SearchFieldConfig) => {
    const placeholder =
      field.placeholder ||
      (field.type === 'select'
        ? t('common_please_select', { name: t(field.translationKey) })
        : t('common_please_enter', { name: t(field.translationKey) }));

    switch (field.type) {
      case 'input':
        return <RInput placeholder={placeholder} />;
      case 'select':
        return (
          <RSelect
            loading={field.isLoading}
            placeholder={placeholder}
            options={field.options || []}
          />
        );
      default:
        return null;
    }
  };

  return (
    <RForm form={form} onFinish={handleSubmit} layout="inline">
      <Row gutter={16} className='gap-y-2'>
        {fields.map((field) => (
          <RForm.Item
            label={t(field.translationKey)}
            name={field.name}
            initialValue={field.defaultValue}
            key={field.name}
          >
            {renderField(field)}
          </RForm.Item>
        ))}
        <Col span={6}>
          <div className="flex gap-2">
            <RButton type="primary" htmlType="submit">
              {t('common_search')}
            </RButton>
            <RButton type="default" onClick={handleReset}>
              {t('common_reset')}
            </RButton>
          </div>
        </Col>
      </Row>
    </RForm>
  );
};

export default GameManagementSearchForm;
