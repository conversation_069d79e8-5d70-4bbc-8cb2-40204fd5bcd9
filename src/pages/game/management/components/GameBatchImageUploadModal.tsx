import { useMutation } from '@tanstack/react-query';
import { useQueryClient } from '@tanstack/react-query';
import { Upload, UploadFile } from 'antd';
import { UploadChangeParam } from 'antd/es/upload';
import clsx from 'clsx';
import type { UploadRequestOption } from 'rc-upload/lib/interface';

import { uploadGameFile } from '@/api/game';
import UploadIcon from '@/assets/img/icon/upload.svg?react';
import RModal from '@/components/RModal';
import useImageUpload from '@/components/RUploader/useImageUpload';

import { gameListKeys } from '../hooks';
const { Dragger } = Upload;

const UploadDragger = ({ setFileList }: { setFileList: (files: File[]) => void }) => {
  const { accept } = useImageUpload({
    fileType: 'webp',
    maxSize: 0.1
  });

  const { t } = useTranslation();
  const handleCustomRequest = (options: UploadRequestOption) => {
    const { file, onSuccess, onError } = options;

    const handleUpload = async () => {
      try {
        if (file instanceof File) {
          const url = URL.createObjectURL(file);
          onSuccess?.({ url });
        } else {
          throw new Error('Invalid file type');
        }
      } catch (err) {
        onError?.(err instanceof Error ? err : new Error('Upload failed'));
      }
    };

    handleUpload();
  };

  const handleChange = (info: UploadChangeParam<UploadFile<any>>) => {
    setFileList(info.fileList.map((file) => file.originFileObj as File));
  };

  return (
    <Dragger
      action=""
      accept={accept}
      multiple={true}
      customRequest={handleCustomRequest}
      maxCount={150}
      onChange={handleChange}
    >
      <div className="flex flex-col justify-center items-center">
        <UploadIcon className="w-6 h-6 text-text-icon" />
        <p className="text-text-secondary">{t('components_gameBatchImageUploadModal_upload')}</p>
      </div>
    </Dragger>
  );
};

const GameBatchImageUploadModal = ({
  isOpen,
  onClose
}: {
  isOpen: boolean;
  onClose: () => void;
}) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const [files, setFiles] = useState<File[]>([]);

  const { mutate: uploadGameFileMutation } = useMutation({
    mutationFn: uploadGameFile,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: gameListKeys.lists() });
      onClose();
    }
  });

  const ruleList = [
    {
      title: t('game_batch_upload_rules'),
      rules: [
        t('game_batch_upload_rules_format'),
        t('game_batch_upload_rules_amount'),
        t('game_batch_upload_rules_size')
      ]
    },
    {
      title: t('game_batch_upload_rules_size_rules'),
      rules: [
        `${t('game_general_picture')}: ${t('game_upload_game_image_size', { width: 400, height: 400 })}`,
        `${t('game_potrait_picture')}: ${t('game_upload_game_image_size', { width: 420, height: 208 })}`,
        `${t('game_banner_picture')}: ${t('game_upload_game_image_size', { width: 680, height: 208 })}`
      ]
    },
    {
      title: t('game_batch_upload_rules_parse_rules'),
      rules: [
        t('game_batch_upload_rules_parse_rules_format'),
        t('game_batch_upload_rules_parse_rules_example')
      ]
    }
  ];

  const handleOk = () => {
    uploadGameFileMutation({ files });
  };

  return (
    <RModal
      open={isOpen}
      onCancel={onClose}
      title={t('game_batch_upload')}
      onOk={handleOk}
      okButtonProps={{
        show: true,
        disabled: files.length === 0
      }}
    >
      <UploadDragger setFileList={setFiles} />
      <div className="mt-5 bg-[#FF83171A] border border-[#FF8317] rounded-sm p-2 text-xs grid grid-cols-2 gap-2">
        {ruleList.map((rule, index) => (
          <div
            key={rule.title}
            className={clsx('flex flex-col gap-1', index === 2 && 'col-span-2')}
          >
            <div className="font-bold">{rule.title}</div>
            {rule.rules.map((el) => (
              <span key={el} className="">
                {el}
              </span>
            ))}
          </div>
        ))}
      </div>
    </RModal>
  );
};

export default GameBatchImageUploadModal;
