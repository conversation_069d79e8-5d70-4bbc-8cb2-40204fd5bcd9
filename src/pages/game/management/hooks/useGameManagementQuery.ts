import { useQuery } from '@tanstack/react-query';

import { BaseSearchParams, PaginationProps } from '../types';

interface UseGameManagementQueryProps<T, P extends BaseSearchParams> {
  queryKey: string;
  queryFn: (params: P & { page: number; limit: number }) => Promise<any>;
  pagination: PaginationProps;
  searchParams: P;
  enabled?: boolean;
  select?: (data: any) => { data: T[]; total: number };
}

/**
 * Generic hook for game management data queries
 * Handles pagination, search params, and data selection
 */
export const useGameManagementQuery = <T, P extends BaseSearchParams>({
  queryKey,
  queryFn,
  pagination,
  searchParams,
  enabled = true,
  select
}: UseGameManagementQueryProps<T, P>) => {
  const { data, isPending, error } = useQuery({
    queryKey: [queryKey, pagination.page, pagination.limit, searchParams],
    queryFn: () =>
      queryFn({
        page: pagination.page,
        limit: pagination.limit,
        ...searchParams
      } as P & { page: number; limit: number }),
    enabled,
    select:
      select ||
      ((data: Record<string, unknown>) => ({
        data: (data?.data as Record<string, unknown>)?.data || [],
        total: (data?.data as Record<string, unknown>)?.total || 0
      }))
  });

  return {
    data: data?.data || [],
    total: data?.total || 0,
    isPending,
    error
  };
};
