import { useQuery } from '@tanstack/react-query';

import { getGameOptions } from '@/api/game';

// Query keys
export const gameOptionsKeys = {
  all: ['gameOptions'] as const,
  lists: (providerId?: string, categoryId?: string) =>
    [...gameOptionsKeys.all, 'list', providerId, categoryId] as const,
  details: () => [...gameOptionsKeys.all, 'detail'] as const,
  detail: (id: number | string) => [...gameOptionsKeys.details(), id] as const
};

interface UseGameOptionsProps {
  providerId?: string;
  categoryId?: string;
  enabled?: boolean;
}

const useGameOptions = (params: UseGameOptionsProps = {}) => {
  const enabled = params.enabled ?? true;
  // handle 3 cases
  const gameOptionParams: UseGameOptionsProps = {};
  if (!params.providerId) {
    gameOptionParams.providerId = undefined;
    gameOptionParams.categoryId = undefined;
  } else if (params.providerId && !params.categoryId) {
    gameOptionParams.providerId = params.providerId;
    gameOptionParams.categoryId = undefined;
  } else {
    gameOptionParams.providerId = params.providerId;
    gameOptionParams.categoryId = params.categoryId;
  }

  const query = useQuery({
    queryKey: gameOptionsKeys.lists(gameOptionParams.providerId, gameOptionParams.categoryId),
    queryFn: () => getGameOptions(gameOptionParams),
    select: (data) => data?.data || { providers: [], categories: [] },
    enabled
  });

  return query;
};

export default useGameOptions;
