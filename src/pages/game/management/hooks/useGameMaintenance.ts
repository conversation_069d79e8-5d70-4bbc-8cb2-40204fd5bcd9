import { useQuery } from '@tanstack/react-query';

import { getGameMaintenanceList } from '@/api/game';

import { MaintenanceSearchParams } from '../types';

// Query keys
export const gameMaintenanceKeys = {
  all: ['gameMaintenance'] as const,
  lists: () => [...gameMaintenanceKeys.all, 'list'] as const,
  list: (params: MaintenanceSearchParams) => [...gameMaintenanceKeys.lists(), params] as const,
  details: () => [...gameMaintenanceKeys.all, 'detail'] as const,
  detail: (id: number | string) => [...gameMaintenanceKeys.details(), id] as const
};

interface UseGameMaintenanceOptions {
  searchParams?: MaintenanceSearchParams;
  enabled?: boolean;
}

const useGameMaintenance = ({
  searchParams = {},
  enabled = true
}: UseGameMaintenanceOptions = {}) => {
  const query = useQuery({
    queryKey: gameMaintenanceKeys.list(searchParams),
    queryFn: () => getGameMaintenanceList(searchParams),
    enabled,
    select: (data) => data?.data?.data || []
  });

  return query;
};

export default useGameMaintenance;
