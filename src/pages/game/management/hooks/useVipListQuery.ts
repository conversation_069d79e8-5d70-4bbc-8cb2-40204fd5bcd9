import { useQuery } from '@tanstack/react-query';

import { getVipList } from '@/api/vip';

// Query keys
export const vipListKeys = {
  all: ['vipList'] as const,
  lists: () => [...vipListKeys.all, 'list'] as const,
  list: (params: { name?: string; status?: number }) => [...vipListKeys.lists(), params] as const
};

interface UseVipListQueryOptions {
  enabled?: boolean;
  params?: { name?: string; status?: number };
}

const useVipListQuery = ({ enabled = true, params = {} }: UseVipListQueryOptions = {}) => {
  const query = useQuery({
    queryKey: vipListKeys.list(params),
    queryFn: () => getVipList(params),
    enabled,
    select: (data) => data?.data || []
  });

  return query;
};

export default useVipListQuery;
