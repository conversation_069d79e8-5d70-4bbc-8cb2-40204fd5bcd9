import { useQuery } from '@tanstack/react-query';

import { getGameList } from '@/api/game';
import { cleanSearchParams } from '@/utils/object';

import { GameSearchParams } from '../types';

// Query keys
export const gameListKeys = {
  all: ['gameList'] as const,
  lists: () => [...gameListKeys.all, 'list'] as const,
  list: (params: GameSearchParams) => [...gameListKeys.lists(), params] as const,
  details: () => [...gameListKeys.all, 'detail'] as const,
  detail: (id: string) => [...gameListKeys.details(), id] as const
};

export const GameListInitialFilter = {
  noIcon: 0,
  page: 1,
  limit: 10
};

interface UseGameListOptions {
  searchParams?: GameSearchParams;
  enabled?: boolean;
}

const useGameList = ({
  searchParams = GameListInitialFilter,
  enabled = true
}: UseGameListOptions = {}) => {
  const cleanedSearchParams = useMemo(() => {
    return cleanSearchParams<GameSearchParams>(searchParams);
  }, [searchParams]); cleanSearchParams(searchParams);
  const query = useQuery({
    queryKey: gameListKeys.list(cleanedSearchParams),
    queryFn: () => getGameList(cleanedSearchParams),
    enabled,
    select: (data) => data?.data
  });

  return query;
};

export default useGameList;
