import { useQuery } from '@tanstack/react-query';

import { getGameCategoryList } from '@/api/game';

// Query keys
export const gameCategoryKeys = {
  all: ['gameCategories'] as const,
  lists: () => [...gameCategoryKeys.all, 'list'] as const,
  details: () => [...gameCategoryKeys.all, 'detail'] as const,
  detail: (id: number) => [...gameCategoryKeys.details(), id] as const
};

interface UseGameCategoryQueryOptions {
  enabled?: boolean;
}

const useGameCategoryQuery = ({ enabled = true }: UseGameCategoryQueryOptions = {}) => {
  const query = useQuery({
    queryKey: gameCategoryKeys.lists(),
    queryFn: () => getGameCategoryList(),
    enabled,
    select: (data) => data.data || []
  });

  return query;
};

export default useGameCategoryQuery;
