import { useQuery } from '@tanstack/react-query';

import { getGameProviderList } from '@/api/game';

// Query keys
export const gameProviderKeys = {
  all: ['gameProviders'] as const,
  lists: () => [...gameProviderKeys.all, 'list'] as const,
  details: () => [...gameProviderKeys.all, 'detail'] as const,
  detail: (id: string) => [...gameProviderKeys.details(), id] as const
};

interface UseGameProviderQueryOptions {
  enabled?: boolean;
}

const useGameProviderQuery = ({ enabled = true }: UseGameProviderQueryOptions = {}) => {
  const query = useQuery({
    queryKey: gameProviderKeys.lists(),
    queryFn: getGameProviderList,
    enabled,
    select: (data) => data?.data || []
  });

  return query;
};

export default useGameProviderQuery;
