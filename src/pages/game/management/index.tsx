import { useState } from 'react';

import TableSearchLayout from '@/layout/TableSearchLayout';

import GameTabs from './components/GameTabs';
import { CategoriesTab, GamesTab, MaintenanceTab, ProvidersTab } from './components/tabs';

const GameManagementPage = () => {
  const [activeTab, setActiveTab] = useState('games'); // Default to games tab

  // Handle active tab change from GameTabs component
  const handleActiveTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  // Render the appropriate tab content
  const renderTabContent = () => {
    switch (activeTab) {
      case 'providers':
        return <ProvidersTab isActive={activeTab === 'providers'} />;
      case 'categories':
        return <CategoriesTab isActive={activeTab === 'categories'} />;
      case 'games':
        return <GamesTab isActive={activeTab === 'games'} />;
      case 'maintenance':
        return <MaintenanceTab isActive={activeTab === 'maintenance'} />;
      default:
        return <GamesTab isActive={activeTab === 'games'} />;
    }
  };

  return (
    <div>
      <GameTabs onActiveTabChange={handleActiveTabChange} />
      <TableSearchLayout hasTab={true}>{renderTabContent()}</TableSearchLayout>
    </div>
  );
};

export default GameManagementPage;
