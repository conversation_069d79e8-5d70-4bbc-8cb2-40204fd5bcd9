import { useMutation } from '@tanstack/react-query';
import { Form } from 'antd';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import { editGameLimit } from '@/api/game';
import RForm from '@/components/RForm';
import RModal from '@/components/RModal';
import RSelect from '@/components/RSelect';

import { useGameList, useVipListQuery } from './hooks';
import { GameListInitialFilter } from './hooks/useGameList';

interface GameLimitModalProps {
  open: boolean;
  gameId?: string;
  initialGameLimit?: number;
  onClose: () => void;
}

const GameLimitModal = ({ open, onClose, initialGameLimit, gameId }: GameLimitModalProps) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  const vipListQuery = useVipListQuery();

  const gameListQuery = useGameList({
    searchParams: GameListInitialFilter,
    enabled: open
  });

  const editGameLimitMutate = useMutation({
    mutationFn: editGameLimit,
    onSuccess: () => {
      gameListQuery.refetch();
      onClose();
    }
  });

  useEffect(() => {
    form.resetFields();
    setTimeout(() => {
      form.setFieldsValue({ vipLimit: initialGameLimit || 0 });
    }, 100);
  }, [open, form, initialGameLimit]);

  const handleSubmit = (values: any) => {
    if (!gameId) return;
    if (values.vipLimit === 0) {
      values.vipLimit = null;
    }
    editGameLimitMutate.mutate({
      id: gameId,
      vipLimit: values.vipLimit
    });
  };

  const handleClose = () => {
    form.resetFields();
    onClose();
  };

  const limitOptions = [
    {
      label: t('common_none'),
      value: 0
    }
  ].concat(
    vipListQuery.data?.map((item) => ({
      label: item.name,
      value: item.id
    })) || []
  );

  return (
    <RModal
      title={t('game_conditions')} // 新增遊戲維運
      open={open}
      onCancel={handleClose}
      onOk={() => form.submit()}
      loading={editGameLimitMutate.isPending}
      width={600}
      okButtonProps={{ text: t('common_confirm'), show: true }} // 確定
      cancelButtonProps={{ text: t('common_cancel'), show: true }} // 取消
    >
      <RForm form={form} onFinish={handleSubmit} layout="vertical">
        <RForm.Item
          label={`${t('pages_player_vip_vipLevelSetting')}:`} // * 開放VIP等級
          name="vipLimit"
          required
        >
          <RSelect
            placeholder={t('common_please_select', { name: t('pages_player_vip_vipLevelSetting') })} // 請選擇供應商
            options={limitOptions}
          />
        </RForm.Item>
      </RForm>
    </RModal>
  );
};

export default GameLimitModal;
