import { Col, Row } from 'antd';
import { useTranslation } from 'react-i18next';

import RModal from '@/components/RModal';
import { Game } from '@/types/game';

interface GameImageReviewModalProps {
  open: boolean;
  game: Game;
  onClose: () => void;
}

const GameImageReviewModal = ({ open, onClose, game }: GameImageReviewModalProps) => {
  const { t } = useTranslation();

  const emptyImage = <div className="min-h-[150px] w-[150px] bg-gray-400"></div>;

  return (
    <RModal
      title={t('game_picture_preview')} // 新增遊戲維運
      open={open}
      onCancel={onClose}
      width={600}
      okButtonProps={{ show: false }} // 確定
      cancelButtonProps={{ text: t('common_cancel'), show: true }} // 取消
    >
      <div className="text-gray-400">
        <div className="flex gap-1">
          <div>{t('game_picture_preview')}:</div>
          <div className="text-black">
            {game.provider}/{game.name}
          </div>
        </div>
        <Row className="mt-4">
          <Col span={12} className="flex flex-col gap-1">
            <div>{t('game_general_picture')}:</div>
            {game.icon ? (
              <img
                src={game.icon}
                className="max-w-[200px] max-h-[200px] object-contain"
                alt="game"
              />
            ) : (
              emptyImage
            )}
          </Col>
          <Col span={12} className="flex flex-col gap-1">
            <div>{t('game_potrait_picture')}:</div>
            {game.rkmv ? (
              <img
                src={game.rkmv}
                className="max-w-[200px] max-h-[200px] object-contain"
                alt="game"
              />
            ) : (
              emptyImage
            )}
          </Col>
        </Row>
        <Row className="mt-4">
          <Col span={8} className="flex flex-col gap-1">
            <div>{t('game_banner_picture')}:</div>
            {game.rkmh ? (
              <img
                src={game.rkmh}
                className="max-w-[200px] max-h-[200px] object-contain"
                alt="game"
              />
            ) : (
              emptyImage
            )}
          </Col>
        </Row>
      </div>
    </RModal>
  );
};

export default GameImageReviewModal;
