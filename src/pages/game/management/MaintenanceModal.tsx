import { useMutation } from '@tanstack/react-query';
import { Col, DatePicker, Form, message, Radio, Row } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { createGameMaintenance } from '@/api/game';
import RForm from '@/components/RForm';
import RModal from '@/components/RModal';
import RSelect from '@/components/RSelect';
import { GameMaintenanceMode } from '@/types/game';

import { useGameMaintenance, useGameOptions } from './hooks';

const { RangePicker } = DatePicker;

interface MaintenanceModalProps {
  open: boolean;
  onClose: () => void;
}

type CategoryFromGameOption = {
  categoryId: string;
  categoryName: string;
  games: {
    id: string;
    gameName: string;
  }[];
};

const MaintenanceModal = ({ open, onClose }: MaintenanceModalProps) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [selectedProvider, setSelectedProvider] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [maintenanceMode, setMaintenanceMode] = useState<GameMaintenanceMode>(
    GameMaintenanceMode.NOW
  );
  const gameMaintenanceQuery = useGameMaintenance();

  // Get game options for dropdowns using the new hook
  const gameProviderQuery = useGameOptions();
  const gameCategoryQuery = useGameOptions({ providerId: selectedProvider });

  const createMaintenanceMutate = useMutation({
    mutationFn: createGameMaintenance,
    onSuccess: () => {
      gameMaintenanceQuery.refetch();
      message.success(t('common_operation_success'));
      onClose();
    },
    onError: () => {
      message.error(t('common_operation_failed'));
    }
  });

  useEffect(() => {
    form.resetFields();
    setSelectedProvider('');
    setSelectedCategory('');
    setMaintenanceMode(GameMaintenanceMode.NOW);
  }, [open, form]);

  const handleSubmit = (values: any) => {
    const { timeRange, mode, ...otherValues } = values;

    // For immediate maintenance, set start time to now and end time to 1 hour later
    let timeStart: number | null = null;
    let timeEnd: number | null = null;

    if (mode === GameMaintenanceMode.SCHEDULE) {
      if (!timeRange || timeRange.length !== 2) {
        message.error(t('common_please_select', { name: t('maintenance_time_range') }));
        return;
      }
      [timeStart, timeEnd] = timeRange.map((time: dayjs.Dayjs) => time.valueOf());
    }

    createMaintenanceMutate.mutate({
      ...otherValues,
      mode,
      timeStart,
      timeEnd
    });
  };

  const handleClose = () => {
    form.resetFields();
    setSelectedProvider('');
    setSelectedCategory('');
    setMaintenanceMode(GameMaintenanceMode.NOW);
    onClose();
  };

  const handleProviderChange = (value: string) => {
    setSelectedProvider(value);
    setSelectedCategory('');
    form.setFieldsValue({ categoryId: undefined, gameId: undefined });
  };

  const handleCategoryChange = (value: string) => {
    setSelectedCategory(value);
    form.setFieldsValue({ gameId: undefined });
  };

  const handleModeChange = (e: any) => {
    const mode = e.target.value;
    setMaintenanceMode(mode);
    form.setFieldsValue({ mode });
  };

  const providerOptions =
    (
      gameProviderQuery.data as {
        providerId: string;
        providerName: string;
      }[]
    )?.map((provider) => ({
      label: provider.providerName,
      value: provider.providerId
    })) || [];

  const categoryOptions =
    (gameCategoryQuery.data as CategoryFromGameOption[])?.map((category) => ({
      label: category.categoryName,
      value: category.categoryId
    })) || [];

  const gameOptions = useMemo(() => {
    const gameCategoryData = gameCategoryQuery.data as CategoryFromGameOption[];
    const selectedCategoryGames = gameCategoryData?.find(
      (category) => category.categoryId === selectedCategory
    )?.games;
    const options =
      selectedCategoryGames?.map((game) => ({
        label: game.gameName,
        value: game.id
      })) || [];
    return options;
  }, [gameCategoryQuery.data, selectedCategory]);

  const disabledDate = (current: dayjs.Dayjs) => {
    // Disable dates before today
    return current && current < dayjs().startOf('day');
  };

  const disabledTime = (current: dayjs.Dayjs, type: 'start' | 'end') => {
    if (!current) return {};

    const now = dayjs();
    const isToday = current.isSame(now, 'day');

    if (isToday && type === 'start') {
      return {
        disabledHours: () => Array.from({ length: now.hour() }, (_, i) => i),
        disabledMinutes: (selectedHour: number) => {
          if (selectedHour === now.hour()) {
            return Array.from({ length: now.minute() }, (_, i) => i);
          }
          return [];
        }
      };
    }

    return {};
  };

  return (
    <RModal
      title={t('add_game_maintenance')} // 新增遊戲維運
      open={open}
      onCancel={handleClose}
      onOk={() => form.submit()}
      loading={createMaintenanceMutate.isPending}
      width={600}
      okButtonProps={{ text: t('common_confirm'), show: true }} // 確定
      cancelButtonProps={{ text: t('common_cancel'), show: true }} // 取消
    >
      <RForm form={form} onFinish={handleSubmit} layout="vertical">
        <Row gutter={16}>
          <Col span={12}>
            <RForm.Item
              label={`${t('game_provider')}:`} // * 供應商：
              name="providerId"
              rules={[
                {
                  required: true,
                  message: t('common_please_select', { name: t('game_provider') })
                }
              ]}
            >
              <RSelect
                placeholder={t('common_please_select', { name: t('game_provider') })} // 請選擇供應商
                options={providerOptions}
                loading={gameProviderQuery.isPending}
                onChange={handleProviderChange}
                showSearch
                filterOption={(input, option) =>
                  String(option?.label ?? '')
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
              />
            </RForm.Item>
          </Col>
          <Col span={12}>
            <RForm.Item
              label={`${t('game_category_name')}:`} // * 分類：
              name="categoryId"
            >
              <RSelect
                placeholder={t('common_please_select', { name: t('game_category_name') })} // 請選擇分類
                options={categoryOptions}
                loading={gameCategoryQuery.isPending}
                onChange={handleCategoryChange}
                disabled={!selectedProvider}
                showSearch
                filterOption={(input, option) =>
                  String(option?.label ?? '')
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
              />
            </RForm.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <RForm.Item
              label={`${t('game_name')}:`} // * 遊戲：
              name="gameId"
            >
              <RSelect
                placeholder={t('common_please_select', { name: t('game_name') })} // 請選擇遊戲
                options={gameOptions}
                loading={gameCategoryQuery.isPending}
                disabled={!selectedProvider || !selectedCategory}
                showSearch
                filterOption={(input, option) =>
                  String(option?.label ?? '')
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
              />
            </RForm.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <RForm.Item
              label={`${t('maintenance_mode')}:`} // * 維運模式：
              name="mode"
              rules={[
                {
                  required: true,
                  message: t('common_please_select', { name: t('maintenance_mode') })
                }
              ]}
              initialValue={GameMaintenanceMode.NOW}
            >
              <Radio.Group onChange={handleModeChange}>
                <Radio value={GameMaintenanceMode.NOW}>{t('maintenance_mode_now')}</Radio>{' '}
                {/* 立即維運 */}
                <Radio value={GameMaintenanceMode.SCHEDULE}>
                  {t('maintenance_mode_schedule')}
                </Radio>{' '}
                {/* 指定時間維運 */}
              </Radio.Group>
            </RForm.Item>
          </Col>
        </Row>

        {maintenanceMode === GameMaintenanceMode.SCHEDULE && (
          <Row gutter={16}>
            <Col span={24}>
              <RForm.Item
                label={t('maintenance_time_range')}
                name="timeRange"
                rules={[
                  {
                    required: true,
                    message: t('common_please_select', { name: t('maintenance_time_range') })
                  }
                ]}
              >
                <RangePicker
                  showTime={{
                    format: 'HH:mm'
                  }}
                  format="YYYY-MM-DD HH:mm"
                  placeholder={[t('maintenance_start_time'), t('maintenance_end_time')]}
                  disabledDate={disabledDate}
                  disabledTime={disabledTime}
                  style={{ width: '100%' }}
                />
              </RForm.Item>
            </Col>
          </Row>
        )}
      </RForm>
    </RModal>
  );
};

export default MaintenanceModal;
