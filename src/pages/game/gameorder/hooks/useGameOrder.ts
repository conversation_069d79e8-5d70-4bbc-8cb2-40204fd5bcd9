import { useMutation, useQuery } from '@tanstack/react-query';
import { useCallback, useState } from 'react';

import { getGameProviderList } from '@/api/game';
import {
  type GameOrderResponse,
  getArcadeGameOrder,
  getCategoryCount,
  getFishingGameOrder,
  getSlotGameOrder,
  getTableGameOrder
} from '@/api/gameOrder';
import { GameOrderResult } from '@/enums/game';
import { TimeSelect } from '@/enums/timeSelect';
import { GameCategoryCount, GameProvider } from '@/types/game';

// 遊戲類型配置
const GAME_TYPES = {
  slot: {
    key: 'slot',
    label: 'pages_game_gametype_slot',
    apiFunction: getSlotGameOrder,
    categoryKey: 'slot' as keyof GameCategoryCount
  },
  fish: {
    key: 'fish',
    label: 'pages_game_gametype_fish',
    apiFunction: getFishingGameOrder,
    categoryKey: 'fish' as keyof GameCategoryCount
  },
  table: {
    key: 'table',
    label: 'pages_game_gametype_table',
    apiFunction: getTableGameOrder,
    categoryKey: 'table' as keyof GameCategoryCount
  },
  arcade: {
    key: 'arcade',
    label: 'pages_game_gametype_arcade',
    apiFunction: getArcadeGameOrder,
    categoryKey: 'arcade' as keyof GameCategoryCount
  }
} as const;

export type GameType = keyof typeof GAME_TYPES;

// 搜尋參數類型
export type BaseSearchParams = {
  timeFilter?: TimeSelect;
  start?: number;
  end?: number;
  playerAccount?: string;
  gameAccount?: string;
  id?: string;
  excludeTest?: 1 | 0;
};

export type FullSearchParams = BaseSearchParams & {
  status: 0 | 1;
  providers?: string[];
  matchId?: string;
  result?: GameOrderResult | 'all';
};

// 遊戲狀態管理 hook
export const useGameOrderState = () => {
  const [gameData, setGameData] = useState<Record<GameType, GameOrderResponse | undefined>>({
    slot: undefined,
    fish: undefined,
    table: undefined,
    arcade: undefined
  });

  const [loadingStates, setLoadingStates] = useState<Record<GameType, boolean>>({
    slot: false,
    fish: false,
    table: false,
    arcade: false
  });

  const [categoryCount, setCategoryCount] = useState<GameCategoryCount | undefined>(undefined);

  // 清空所有遊戲相關狀態
  const clearAllGameData = useCallback(() => {
    setGameData({
      slot: undefined,
      fish: undefined,
      table: undefined,
      arcade: undefined
    });
    setLoadingStates({
      slot: false,
      fish: false,
      table: false,
      arcade: false
    });
    setCategoryCount(undefined);
  }, []);

  // 更新特定遊戲類型的數據
  const updateGameData = useCallback((gameType: GameType, data: GameOrderResponse | undefined) => {
    setGameData((prev) => ({
      ...prev,
      [gameType]: data
    }));
  }, []);

  // 更新 loading 狀態
  const updateLoadingState = useCallback((gameType: GameType, loading: boolean) => {
    setLoadingStates((prev) => ({
      ...prev,
      [gameType]: loading
    }));
  }, []);

  return {
    gameData,
    loadingStates,
    categoryCount,
    setCategoryCount,
    clearAllGameData,
    updateGameData,
    updateLoadingState
  };
};

// API 調用管理 hook
export const useGameOrderAPI = (gameState: ReturnType<typeof useGameOrderState>, limit: number) => {
  const { setCategoryCount, updateGameData, updateLoadingState } = gameState;

  const { mutate: fetchCategoryCount } = useMutation<GameCategoryCount, Error, FullSearchParams>({
    mutationFn: async (params: FullSearchParams) => {
      const categoryParams = {
        timeFilter: params.timeFilter,
        start: params.start,
        end: params.end,
        playerAccount: params.playerAccount,
        gameAccount: params.gameAccount,
        id: params.id,
        excludeTest: params.excludeTest
      };
      const { data } = await getCategoryCount(categoryParams);
      if (!data) {
        throw new Error('Failed to fetch game category count');
      }
      return data;
    },
    onSuccess: (data) => {
      setCategoryCount(data);
    }
  });

  // 通用的 API 調用函數
  const callAPIs = useCallback(
    (
      params: FullSearchParams,
      targetPage: number = 1,
      gameType: GameType,
      fetchCategory: boolean = true
    ) => {
      if (fetchCategory) {
        fetchCategoryCount(params);
      }

      updateLoadingState(gameType, true);

      const gameConfig = GAME_TYPES[gameType];
      const { result: gameResult, ...apiParams } = params;

      //   console.log(`Calling API for game type: ${gameType}`, {
      //     apiFunction: gameConfig.apiFunction.name,
      //     params: {
      //       page: targetPage,
      //       limit,
      //       ...apiParams,
      //       result: gameResult === 'all' ? undefined : gameResult
      //     }
      //   });

      gameConfig
        .apiFunction({
          page: targetPage,
          limit,
          ...apiParams,
          result: gameResult === 'all' ? undefined : gameResult
        })
        .then(({ data }) => {
          if (data) {
            updateGameData(gameType, data);
          }
        })
        .catch((error) => {
          console.error(`Error fetching ${gameType} data:`, error);
          updateGameData(gameType, undefined);
        })
        .finally(() => {
          updateLoadingState(gameType, false);
        });
    },
    [fetchCategoryCount, limit, updateGameData, updateLoadingState]
  );

  return {
    callAPIs,
    fetchCategoryCount
  };
};

// 遊戲提供商查詢 hook
export const useGameProviders = () => {
  return useQuery<GameProvider[]>({
    queryKey: ['gameProviderList'],
    queryFn: async () => {
      const { data } = await getGameProviderList();
      return data ?? [];
    }
  });
};

// 導出遊戲類型配置
export { GAME_TYPES };
