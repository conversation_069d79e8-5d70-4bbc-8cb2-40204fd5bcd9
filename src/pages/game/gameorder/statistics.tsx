import { GameOrderResponse } from '@/api/gameOrder';
import { numberFormat } from '@/utils/numbers';

type GameOrderStatisticsProps = {
  data?: GameOrderResponse;
};

const GameOrderStatistics = ({ data }: GameOrderStatisticsProps) => {
  const { t } = useTranslation();
  // console.log(data);

  const statistics = [
    {
      label: t('pages_transaction_topupOrder_totalPlayer'),
      value: data?.statistic?.accountCount ?? 0
    },
    {
      label: t('pages_game_gameOrder_total'),
      value: data?.statistic?.orderCount ?? 0
    }
  ];

  const summary = [
    {
      label: t('pages_game_gameOrder_pageAmount'),
      value: data?.statistic?.sumPageBetPoint ?? 0
    },
    {
      label: t('pages_game_gameOrder_Amount_total'),
      value: data?.statistic?.sumTtlBetPoint ?? 0
    },
    {
      label: t('pages_game_gameOrder_pageProfit'),
      value: data?.statistic?.sumPageProfit ?? 0
    },
    {
      label: t('pages_game_gameOrder_pageProfit_total'),
      value: data?.statistic?.sumTtlProfit ?? 0
    }
  ];

  return (
    <div>
      <div className="flex gap-7 justify-end">
        {statistics.map(({ label, value }) => (
          <div key={label}>
            <span className="text-text-secondary mr-1">{label}:</span>
            <span>{numberFormat(value)}</span>
          </div>
        ))}
      </div>
      <div className="flex gap-7 mt-1">
        {summary.map(({ label, value }) => (
          <div key={label}>
            <span className="text-text-secondary mr-1">{label}:</span>
            <span>{numberFormat(value)}</span>
          </div>
        ))}
      </div>
    </div>
  );
};
export default GameOrderStatistics;
