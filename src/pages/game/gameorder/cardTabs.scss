.card-tabs.ant-tabs {
  .ant-tabs-nav {
    .ant-tabs-tab {
      position: relative;
      border-top: none;
      width: 120px;
      justify-content: center;
      padding: 4px;
      border: 1px solid var(--color-component-border);

      &.ant-tabs-tab-active {
        .ant-tabs-tab-btn {
          div {
            &:first-child {
              color: var(--color-text) !important;
            }
            &:last-child {
              color: var(--color-text-secondary);
            }
          }
        }
        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 3px;
          background: #1d252d;
          border-radius: 8px 8px 0 0;
        }
      }
    }
  }
}
