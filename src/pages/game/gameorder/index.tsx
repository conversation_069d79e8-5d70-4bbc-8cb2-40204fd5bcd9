import './cardTabs.scss';

import { useMutation } from '@tanstack/react-query';
import type { ColumnsType } from 'antd/es/table';
import clsx from 'clsx';
import dayjs from 'dayjs';

import { exportGameOrder, getBetContent } from '@/api/gameOrder';
import InformationIcon from '@/assets/img/icon/information.svg?react';
import ExportRecord from '@/components/ExportRecord';
import QuickDateSelect from '@/components/QuickDateSelect';
import { RButton } from '@/components/RButton';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RMultipleSelect from '@/components/RMultipleSelect';
import RSelect from '@/components/RSelect';
import RTable from '@/components/RTable';
import { RTabs } from '@/components/RTabs';
import RTag from '@/components/RTag';
import RTooltip from '@/components/Rtooltip';
import SearchForm from '@/components/SearchForm';
import { GameOrderResult } from '@/enums/game';
import { TimeSelect } from '@/enums/timeSelect';
import usePagination from '@/hooks/usePagination';
import TableSearchLayout from '@/layout/TableSearchLayout';
import { GameOrder } from '@/types/game';
import { numberFormat } from '@/utils/numbers';
import { formatTime } from '@/utils/time';

import { GAME_RESULT_MAP } from './gameResultMap';
import {
  type BaseSearchParams,
  type FullSearchParams,
  GAME_TYPES,
  type GameType,
  useGameOrderAPI,
  useGameOrderState,
  useGameProviders
} from './hooks/useGameOrder';
import GameOrderStatistics from './statistics';

const TIME_FILTER_OPTIONS = [
  { label: 'pages_game_updateTime', value: TimeSelect.UPDATE_AT },
  { label: 'pages_game_orderTime', value: TimeSelect.CREATE_AT }
] as const;

type TempSearchFormValues = {
  conditionType: string;
  conditionValue: string;
  excludeTest: 1 | 0;
  timeFilter: (typeof TIME_FILTER_OPTIONS)[number]['value'];
  start?: number;
  end?: number;
};

const CONDITION_OPTIONS = [
  { label: 'pages_player_account', value: 'playerAccount' },
  { label: 'pages_game_account', value: 'gameAccount' },
  { label: 'pages_game_orderId', value: 'betId' }
] as const;

const defaultToday = [dayjs().startOf('day').valueOf(), dayjs().endOf('day').valueOf()];

const getTableColumns = (
  t: (key: string) => string,
  activeTab?: GameType
): ColumnsType<GameOrder> => [
  {
    title: `${t('common_account')} / ${t('pages_game_accountUser')}`,
    dataIndex: 'playerAccount',
    width: 180,
    render: (playerAccount: string, record: GameOrder) => (
      <div>
        <div>{playerAccount}/</div>
        <div>{record.gameAccount}</div>
      </div>
    )
  },
  {
    title: t('pages_game_id'),
    dataIndex: 'betId',
    width: 150
  },
  {
    title: t('common_supplier'),
    dataIndex: 'providerName',
    width: 100
  },
  {
    title: t('pages_game_name'),
    dataIndex: 'gameName',
    width: 100
  },
  // 電子、棋牌和街機遊戲才顯示 matchId 欄位，
  ...(activeTab === 'table' || activeTab === 'arcade'
    ? [
        {
          title: t('pages_game_matchId'),
          dataIndex: 'matchId',
          render: (matchId: string) => matchId || '-'
        }
      ]
    : []),
  // 電子、棋牌、街機和 slot 遊戲都顯示詳情欄位
  ...(activeTab === 'table' || activeTab === 'arcade' || activeTab === 'slot'
    ? [
        {
          title: t('pages_game_gameOrder_betContent'),
          dataIndex: 'betContent',
          render: (betContent: string, record: GameOrder) => {
            const handleCheckClick = (e: React.MouseEvent) => {
              e.preventDefault();
              getBetContent({ id: record.id, status: record.status }).then((res) => {
                window.open(res.data?.url, '_blank');
              });
            };

            const CheckLink = () => (
              <a
                href="#"
                onClick={handleCheckClick}
                className="text-blue-600 hover:text-blue-800 underline cursor-pointer"
              >
                {t('common_check')}
              </a>
            );

            if (!betContent) {
              return (
                <>
                  -/
                  <CheckLink />
                </>
              );
            }

            return <div>{betContent}</div>;
          }
        }
      ]
    : []),
  {
    title: t('common_betAmount'),
    dataIndex: 'betAmount',
    width: 100,
    render: (betAmount: number) => {
      if (betAmount === null || betAmount === undefined || isNaN(betAmount)) {
        return <span>-</span>;
      }
      return numberFormat(betAmount, 2);
    },
    sorter: (a, b) => a.betAmount - b.betAmount
  },
  {
    title: t('common_profit'),
    dataIndex: 'profit',
    width: 100,
    render: (profit: number) => {
      if (profit === null || profit === undefined || isNaN(profit)) {
        return <span>-</span>;
      }

      const className = clsx({
        'text-success': profit > 0,
        'text-warning': profit < 0
      });
      const prefix = profit < 0 ? '-' : '';
      return (
        <span className={className}>
          {prefix}
          {numberFormat(Math.abs(profit), 2)}
        </span>
      );
    },
    sorter: (a, b) => a.profit - b.profit
  },
  {
    title: t('common_result'),
    dataIndex: 'result',
    render: (status: GameOrderResult) => {
      const config = GAME_RESULT_MAP[status];
      return config ? (
        <RTag color={config.color} textColor={config.textColor}>
          {t(config.label)}
        </RTag>
      ) : (
        '-'
      );
    }
  },
  {
    title: `${t('pages_game_orderTime')}`,
    dataIndex: 'createdAt',
    render: (createdAt: number) => {
      return createdAt ? formatTime(createdAt) : '-';
    }
  },
  {
    title: `${t('pages_game_updateTime')}`,
    dataIndex: 'updatedAt',
    render: (updatedAt: number) => {
      return updatedAt ? formatTime(updatedAt) : '-';
    }
  }
];

const ConditionSearch = () => {
  const { t } = useTranslation();

  const translatedOptions = useMemo(
    () =>
      CONDITION_OPTIONS.map((option) => ({
        label: t(option.label),
        value: option.value
      })),
    [t]
  );

  return (
    <RForm.Item label={t('common_condition')}>
      <RForm.Item name="conditionType" className="inline-block !mr-2" initialValue="playerAccount">
        <RSelect options={translatedOptions} />
      </RForm.Item>
      <RForm.Item name="conditionValue" className="inline-block">
        <RInput className="max-h-[32px]" />
      </RForm.Item>
    </RForm.Item>
  );
};

const SearchFormWrap = ({
  onSearch,
  onReset
}: {
  onSearch: (values: BaseSearchParams) => void;
  onReset: (values: BaseSearchParams) => void;
}) => {
  const { t } = useTranslation();

  const timeFilterOptions = useMemo(() => {
    return TIME_FILTER_OPTIONS.map((option) => ({
      label: t(option.label),
      value: option.value
    }));
  }, [t]);

  const handleSearch = (values: TempSearchFormValues) => {
    const { conditionType, conditionValue, ...processedValues } = values;

    const searchValues =
      conditionType && conditionValue
        ? { ...processedValues, [conditionType]: conditionValue }
        : processedValues;

    onSearch(searchValues);
  };

  const handleReset = () => {
    // 重置表單到預設值
    onReset({
      start: defaultToday[0],
      end: defaultToday[1],
      excludeTest: 1,
      timeFilter: TimeSelect.UPDATE_AT,
      playerAccount: undefined,
      gameAccount: undefined,
      id: undefined
    });
  };

  return (
    <SearchForm<TempSearchFormValues>
      onSearch={handleSearch}
      onReset={handleReset}
      className=""
      initialValues={{
        excludeTest: 1,
        timeFilter: TimeSelect.UPDATE_AT
      }}
    >
      <div className="flex items-center gap-1">
        <RTooltip title={t(`pages_game_order_tooltip`)} color="gray">
          <InformationIcon className="w-4.5 h-4.5 fill-info" />
        </RTooltip>
        <RForm.Item name="timeFilter" label={t('pages_transaction_topupOrder_timeSelect')}>
          <RSelect options={timeFilterOptions} />
        </RForm.Item>
      </div>
      <RForm.Item name="date" label={t('common_timeSelect')} initialValue={defaultToday}>
        <QuickDateSelect defaultActiveKey="today" />
      </RForm.Item>
      <ConditionSearch />
      <RForm.Item name="excludeTest" label={t('common_filtertest')}>
        <RSelect
          options={[
            { label: t('common_yes'), value: 1 }, // 非測試帳號
            { label: t('common_no'), value: 0 } // all
          ]}
        />
      </RForm.Item>
    </SearchForm>
  );
};

const getDefaultFullParams = (baseParams: BaseSearchParams): FullSearchParams => {
  return {
    ...baseParams,
    status: 1,
    providers: undefined,
    result: 'all',
    matchId: undefined
  };
};

const GameOrderPage = () => {
  const { t } = useTranslation();
  const { page, setPage, limit, setLimit } = usePagination({});
  const [resetKey, setResetKey] = useState(0);

  // SearchFormWrap
  const [baseParams, setBaseParams] = useState<BaseSearchParams>({
    start: defaultToday[0],
    end: defaultToday[1],
    excludeTest: 1,
    timeFilter: TimeSelect.UPDATE_AT
  });

  // 完整搜尋參數
  const [fullParams, setFullParams] = useState<FullSearchParams>(() =>
    getDefaultFullParams(baseParams)
  );

  const [activeTab, setActiveTab] = useState<GameType>('slot');

  const gameState = useGameOrderState();
  const { gameData, loadingStates, categoryCount, clearAllGameData, setCategoryCount } = gameState;

  const isPending = loadingStates[activeTab];

  const { callAPIs } = useGameOrderAPI(gameState, limit);

  const { data: providerList } = useGameProviders();

  const currentData = gameData[activeTab];

  const handleTabChangeWithReset = (key: string) => {
    const gameType = key as GameType;
    setActiveTab(gameType);
    // 重置表單到預設值
    setResetKey((prev) => prev + 1);

    const defaultParams: FullSearchParams = getDefaultFullParams(baseParams);
    setFullParams(defaultParams);
  };

  const getTableData = () => {
    return currentData?.data ?? [];
  };

  const tableColumns = useMemo(() => getTableColumns(t, activeTab), [t, activeTab]);

  const handleChangePage = (page: number, pageSize: number) => {
    setPage(page);
    setLimit(pageSize);

    callAPIs(fullParams, page, activeTab);
  };

  // 統一的搜尋處理函數
  const handleSearch = (newParams: Partial<FullSearchParams>, resetPage: boolean = true) => {
    if (resetPage) {
      setPage(1);
    }

    const updatedParams = { ...fullParams, ...newParams };
    setFullParams(updatedParams);
    clearAllGameData();
    callAPIs(updatedParams, 1, activeTab);
  };

  // SearchFormWrap 的搜尋
  const handleBaseSearch = (values: BaseSearchParams) => {
    setBaseParams(values);
    handleSearch(values);
  };

  // SearchFormWrap 的重置
  const handleBaseReset = () => {
    const newBaseParams: BaseSearchParams = {
      start: defaultToday[0],
      end: defaultToday[1],
      excludeTest: 1,
      timeFilter: TimeSelect.UPDATE_AT,
      playerAccount: undefined,
      gameAccount: undefined,
      id: undefined
    };

    setBaseParams(newBaseParams);
    clearAllGameData();
    setPage(1);
    setActiveTab('slot');

    // 設置預設的完整參數
    const defaultParams = getDefaultFullParams(newBaseParams);
    setFullParams(defaultParams);

    // 清空 categoryCount 數據
    setCategoryCount(undefined);
  };

  // 更新完整參數
  const handleFullSearch = (values: FullSearchParams) => {
    handleSearch(values);
  };

  // 重置tabs內表單
  const handleFullReset = () => {
    handleSearch(getDefaultFullParams(baseParams));
    setResetKey((prev) => prev + 1);
  };

  const getTotal = () => {
    return currentData?.total ?? 0;
  };

  // 生成標籤項目
  const tabItems = useMemo(
    () =>
      Object.values(GAME_TYPES).map(({ key, label, categoryKey }) => ({
        label: (
          <div className="flex flex-col items-center">
            <div>{t(label)}</div>
            <div>
              ({categoryCount?.[categoryKey]?.settled ?? '-'} /{' '}
              {categoryCount?.[categoryKey]?.unsettled ?? '-'})
            </div>
          </div>
        ),
        key
      })),
    [t, categoryCount]
  );
  const {
    mutate: getExportGameOrder,
    data: exportData,
    isPending: renderModalContent
  } = useMutation({
    mutationFn: exportGameOrder
  });

  return (
    <TableSearchLayout
      searchFields={<SearchFormWrap onSearch={handleBaseSearch} onReset={handleBaseReset} />}
    >
      <div className="flex justify-end gap-5">
        <GameOrderStatistics data={currentData} />
      </div>
      <div className="flex justify-between items-center">
        <RTabs
          type="card"
          items={tabItems}
          className="card-tabs"
          onChange={handleTabChangeWithReset}
          activeKey={activeTab}
        />
        <ExportRecord
          onClick={() =>
            getExportGameOrder({
              category: activeTab,
              ...fullParams,
              result: fullParams.result === 'all' ? undefined : fullParams.result
            })
          }
          id={exportData?.data?.id ?? undefined}
          isLoading={renderModalContent}
          className="!mb-0"
        />
      </div>
      <RForm
        key={resetKey}
        className="bg-white !p-4 "
        layout="inline"
        onFinish={handleFullSearch}
        initialValues={{
          status: 1,
          providers: undefined,
          result: 'all' as unknown as GameOrderResult // 會顯示為 'all'
        }}
      >
        <RForm.Item name="status" label={t('pages_game_gameOrder_status')}>
          <RSelect
            options={[
              { label: t('pages_game_gameOrder_status_settled'), value: 1 },
              { label: t('pages_game_gameOrder_status_unsettled'), value: 0 }
            ]}
          />
        </RForm.Item>
        <RForm.Item name="providers" label={t('common_supplier')}>
          <RMultipleSelect
            options={
              providerList?.map((item) => ({
                label: item.name,
                value: item.id
              })) ?? []
            }
            className="!w-40"
          />
        </RForm.Item>
        {(activeTab === 'table' || activeTab === 'arcade') && (
          <RForm.Item name="matchId" label={t('pages_game_matchId')}>
            <RInput placeholder={t('pages_game_matchId_placeholder')} className="!h-8" />
          </RForm.Item>
        )}
        <RForm.Item name="result" label={t('common_result')}>
          <RSelect
            options={[
              { label: t('common_all'), value: 'all' },
              { label: t('common_win'), value: GameOrderResult.Win },
              { label: t('common_lose'), value: GameOrderResult.Lose },
              { label: t('common_draw'), value: GameOrderResult.Draw },
              { label: t('common_cancel'), value: GameOrderResult.Cancel },
              { label: t('common_unsettled'), value: GameOrderResult.Unsettled },
              { label: t('common_else'), value: GameOrderResult.Else }
            ]}
          />
        </RForm.Item>
        <RForm.Item label={null}>
          <RButton type="primary" htmlType="submit">
            {t('common_search')}
          </RButton>
          <RButton className="!ml-2" type="default" onClick={handleFullReset}>
            {t('common_reset')}
          </RButton>
        </RForm.Item>
      </RForm>
      <RTable
        dataSource={getTableData()}
        columns={tableColumns}
        rowKey="id"
        loading={isPending}
        pagination={{
          current: page,
          pageSize: limit,
          total: getTotal(),
          showSizeChanger: true,
          onChange: handleChangePage
        }}
      />
    </TableSearchLayout>
  );
};

export default GameOrderPage;
