import { createBrowserRouter, RouterProvider } from 'react-router-dom';

import BaseLayout from '@/layout/BaseLayout';
import Page403 from '@/pages/403';
import Page404 from '@/pages/404';
import Page500 from '@/pages/500';
import ErrorBoundaryPage from '@/pages/errorBoundary';
import IndexPage from '@/pages/login/index';
import { PermissionProvider } from '@/providers/PermissionProvider';
import ProtectedRoute from '@/router/ProtectedRoute';
import generatedRoutes from '~react-pages';

const routes = [
  {
    path: '/',
    element: <IndexPage />,
    index: true
  },
  {
    path: '/403',
    element: <Page403 />
  },
  {
    path: '/500',
    element: <Page500 />
  },
  {
    element: (
      <PermissionProvider>
        <ProtectedRoute />
      </PermissionProvider>
    ),
    errorElement: <ErrorBoundaryPage />,
    children: [
      {
        element: <BaseLayout />,
        children: generatedRoutes
      }
    ]
  },
  {
    path: '*',
    element: <Page404 />
  }
];

if (import.meta.env.DEV) {
  // const IconPage = lazy(() => import('./pages/dev/icon'));
  const IconPage = (await import('@/pages/dev/icon')).default;

  routes.push({
    path: '/dev/icons',
    element: <IconPage />
  });
}

const router = createBrowserRouter(routes);

const Router = () => {
  return <RouterProvider router={router} />;
};

export default Router;
