import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import { defaultRoutes } from '@/router/defaultRoutes';

export interface Permission {
  id: number;
  name: string;
  label: string;
  permissions?: {
    id: number;
    name: string;
    label: string;
    permissions?: {
      id: number;
      name: string;
      label: string;
    }[];
  }[];
}

interface PermissionState {
  permissions: Permission[];
  isPermissionsLoaded: boolean;
  setPermissions: (permissions: Permission[]) => void;
  clearPermissions: () => void;
  hasPermission: (permissionName: string) => boolean;
  getFlattenedPermissions: () => string[];
  hasRoutePermission: (path: string) => boolean;
  getDefaultRoute: () => string;
  getFilteredPermissions: () => Permission[];
}

// 創建路徑到權限ID的map
const createPathPermissionMap = () => {
  const pathPermissionMap = new Map<string, { parentName: string; childName: string }>();

  Object.entries(defaultRoutes).forEach(([parentKey, config]) => {
    config.routes.forEach((route) => {
      const fullPath = `/${parentKey.toLowerCase()}/${route.pageName}`;
      pathPermissionMap.set(fullPath, {
        parentName: config.permissionId,
        childName: route.permissionId
      });
    });
  });

  return pathPermissionMap;
};

const pathPermissionMap = createPathPermissionMap();
// console.log('pathPermissionMap', pathPermissionMap);

// 提取共用的權限扁平化函數
const flattenPermissions = (perms: Permission[]): string[] => {
  return perms.reduce((acc: string[], curr) => {
    acc.push(curr.name);
    if (curr.permissions) {
      acc.push(...flattenPermissions(curr.permissions));
    }
    return acc;
  }, []);
};

// 提取共用的權限過濾邏輯
const filterValidPermissions = (permissions: Permission[]): Permission[] => {
  return permissions
    .map((level1) => {
      const validLevel2 = level1.permissions?.filter((level2) => level2.permissions?.length);

      if (!validLevel2 || validLevel2.length === 0) return null;

      return {
        id: level1.id,
        name: level1.name,
        label: level1.label,
        permissions: validLevel2.map((item) => ({
          id: item.id,
          name: item.name,
          label: item.label
        }))
      };
    })
    .filter((item): item is NonNullable<typeof item> => item !== null);
};

export const usePermissionStore = create<PermissionState>()(
  persist(
    (set, get) => ({
      permissions: [],
      isPermissionsLoaded: false,

      setPermissions: (permissions) => set({ permissions, isPermissionsLoaded: true }),

      clearPermissions: () => set({ permissions: [], isPermissionsLoaded: false }),

      hasPermission: (permissionName) => {
        const { permissions } = get();
        return flattenPermissions(permissions).includes(permissionName);
      },

      getFlattenedPermissions: () => {
        const { permissions } = get();
        return flattenPermissions(permissions);
      },

      hasRoutePermission: (path: string): boolean => {
        const permissions = get().permissions;
        if (!permissions || permissions.length === 0) return false;

        // 根路徑或放行
        const pathParts = path.split('/').filter(Boolean);
        if (pathParts.length < 1) return true;

        const permissionNames = pathPermissionMap.get(path);
        if (!permissionNames) return false;

        const parentPermission = permissions.find((p) => p.name === permissionNames.parentName);
        if (!parentPermission) return false;

        const childPermission = parentPermission.permissions?.find(
          (p) => p.name === permissionNames.childName
        );
        if (!childPermission) return false;

        return Boolean(childPermission.permissions && childPermission.permissions.length > 0);
      },

      getDefaultRoute: () => {
        const permissions = get().permissions;

        if (!permissions || permissions.length === 0) {
          return '/403';
        }

        // 遍歷所有路由配置
        for (const [parentKey, config] of Object.entries(defaultRoutes)) {
          // 查找對應的父級權限
          const parent = permissions.find((p) => p.name === config.permissionId);
          if (!parent) continue;

          // 處理子路由權限
          const children = config.routes
            .map((route) => {
              const child = parent.permissions?.find((p) => p.name === route.permissionId);
              return child && child.permissions && child.permissions.length > 0 ? route : null;
            })
            .filter(Boolean);

          if (children.length > 0) {
            const firstChild = children[0];
            if (firstChild) {
              return `/${parentKey.toLowerCase()}/${firstChild.pageName}`;
            }
          }
        }

        return '/403';
      },

      getFilteredPermissions: () => {
        const { permissions } = get();
        return filterValidPermissions(permissions);
      }
    }),
    {
      name: 'permissions',
      partialize: (state) => ({
        permissions: state.permissions
      })
    }
  )
);
