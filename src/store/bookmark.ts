import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface BookmarkState {
  bookmarks: string[];
}

interface BookmarkActions {
  setBookmarks: (bookmarks: string[]) => void;
  removeBookmark: (bookmark: string) => void;
  clearBookmarks: () => void;
}

export const useBookmarkStore = create<BookmarkState & BookmarkActions>()(
  persist(
    (set) => ({
      bookmarks: [],
      setBookmarks: (bookmarks) => set({ bookmarks }),
      removeBookmark: (bookmark) =>
        set((state) => ({ bookmarks: state.bookmarks.filter((b) => b !== bookmark) })),
      clearBookmarks: () => set({ bookmarks: [] })
    }),
    {
      name: 'bookmarks'
    }
  )
);
