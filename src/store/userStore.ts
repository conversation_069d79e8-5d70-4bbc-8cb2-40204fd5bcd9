import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import { getUserInfo, UserInfoResponse } from '@/api/auth';

import { useBookmarkStore } from './bookmark';
import { usePermissionStore } from './permissionStore';

export type UserState = {
  userInfo: UserInfoResponse;
  isLoading: boolean;
};

export type UserActions = {
  setUser: (user: UserState['userInfo']) => void;
  clearUser: () => void;
  setToken: (token: string) => void;
  fetchUserInfo: () => Promise<void>;
};

export type UserStore = UserState & UserActions;

export const defaultUserState: UserState = {
  userInfo: {
    adminId: '',
    account: '',
    permissions: [],
    token: ''
  },
  isLoading: false
};

export const useUserStore = create<UserStore>()(
  persist(
    (set, get) => ({
      ...defaultUserState,

      setUser: (user: UserState['userInfo']) => set({ userInfo: user }),

      clearUser: () => {
        set({ userInfo: defaultUserState.userInfo });
        usePermissionStore.getState().clearPermissions();
        useBookmarkStore.getState().clearBookmarks();
      },

      setToken: (token: string) =>
        set((state) => ({
          userInfo: { ...state.userInfo, token }
        })),

      fetchUserInfo: async () => {
        set({ isLoading: true });
        try {
          const response = await getUserInfo();
          if (response.code === 200 && response.data) {
            const userInfo = response.data as UserInfoResponse;
            set({
              userInfo: {
                ...userInfo,
                token: get().userInfo.token
              }
            });
            // 更新權限
            if (Array.isArray(userInfo.permissions)) {
              usePermissionStore.getState().setPermissions(userInfo.permissions);
            }
          }
        } catch (error) {
          console.error('Failed to fetch user info:', error);
          if (error && typeof error === 'object' && 'code' in error && error.code !== 401) {
            get().clearUser();
          }
        } finally {
          set({ isLoading: false });
        }
      }
    }),
    {
      name: 'userInfo',
      partialize: (state) => ({
        userInfo: {
          adminId: state.userInfo.adminId,
          account: state.userInfo.account,
          token: state.userInfo.token,
          permissions: state.userInfo.permissions
        }
      })
    }
  )
);
