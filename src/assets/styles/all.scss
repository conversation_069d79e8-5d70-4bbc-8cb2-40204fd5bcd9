@use './base.scss';
@use './antd.scss';

/* 在 custom-menu 底下客製化 AntD Menu item */
.custom-menu {
  background-color: transparent;
  color: var(--color-text);
  font-size: 14px;
  :where(.ant-menu-item) {
    font-size: 14px;
  }
}
.platform-side-menu .ant-menu-item {
  border-bottom: 1px solid var(--color-component-border);
}

.custom-tabs {
  .ant-tabs-tab:first-child {
    .ant-tabs-tab-btn {
      margin-left: 16px;
    }
  }
}

/* Menu item hover 效果 */
.custom-menu :where(.ant-menu-item):hover .custom-menu :where(.ant-menu-submenu-title):hover {
  background-color: var(--color-bg-hover);
  color: var(--color-primary);
}
/* 內部的 svg icon hover/selected時也變色 */
.menu-icon {
  fill: var(--color-text-icon);
}
.custom-menu .ant-menu-item:hover .menu-icon,
.custom-menu .ant-menu-item-selected .menu-icon,
.custom-menu .ant-menu-submenu-title:hover .menu-icon,
.custom-menu .ant-menu-submenu-selected > .ant-menu-submenu-title .menu-icon {
  fill: var(--color-primary);
}

/* Zoom animation for dropdown */
.zoom-transition {
  transition: all 0.2s ease-in-out;
}

.zoom-enter-active {
  opacity: 1;
  transform: scale(1);
}

.zoom-leave-active {
  opacity: 0;
  transform: scale(0.95);
  transform-origin: top right;
}

.dropdown {
  background-color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  margin-top: 8px;
  z-index: 100;
}

/* 覆盖Ant Design Header 樣式 */
:where(.css-dev-only-do-not-override-1qoovwj).ant-layout-header {
  line-height: 28px !important;
  height: 64px !important;
}

/* 確保頁面頂部導航中的元素垂直居中對齊 */
.ant-layout-header .flex.items-center {
  height: 100%;
}

/* 統一下拉菜單位置 */
.header-dropdown-wrapper {
  position: relative;
  height: 100%;
  display: flex;
  align-items: center;
}

.header-dropdown {
  position: absolute;
  top: 48px !important;
  right: 0;
  width: 200px;
}
