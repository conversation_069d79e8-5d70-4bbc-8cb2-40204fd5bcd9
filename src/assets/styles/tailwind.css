/* tailwind跟scss相沖, 會有問題, 所以拆開來import */
@import 'tailwindcss';

/* 請使用tailwindcss定義好的css var */
@theme static {
  --text-size-base: 12px;

  --color-primary: #376995;
  --color-success: #38c522;
  --color-info: #2699fb;
  --color-warning: #ff4343;
  --color-bg-hover: #d9e4ee;
  --color-header-bg: #cad7e1;
  --color-bg-selected: #fbfbfb;
  --color-green: #45bc9c;
  --color-yellow: #f2a23a;
  --color-bg-primary: #f6f9fb; /* 頁面背景色 */
  --color-bg-secondary: #eaeef1; /* 書籤背景色 表頭底色 */
  --color-text: #444444; /* 文字色 */
  --color-text-secondary: #8ea1b1; /* 文字輔色 */
  --color-text-placeholder: #c2cbd4; /* 文字placeholder */
  --color-text-icon: #8999a8;
  --color-component-border: #e2e4e6;
  --color-component-active: #e6edf3;
  --color-bg-info: #f8f8f8;

  --tag-bg-enabled: #e6f9ea;
  --tag-bg-disabled: #ffeaea;
  --tag-bg-processing: #f2a23a29;
  --tag-bg-else: #8EA1B129;
  --shadow-content: 0px 2px 6px #333d461a;
  --shadow-button: 0px 3px 6px #00000029;
}
