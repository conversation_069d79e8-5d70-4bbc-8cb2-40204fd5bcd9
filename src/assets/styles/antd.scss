.app-theme {
  font-size: var(--text-size-base);
  // color: var(--color-text);

  --ant-color-primary: var(--color-primary);
  --ant-color-error: var(--color-warning);
  --ant-border-radius: 4px;
  --ant-color-text: var(--color-text);
  --ant-font-size: var(--text-size-base);
  --ant-color-text-base: var(--color-text);
  --ant-form-label-font-size: var(--ant-font-size);
  --ant-form-label-color: var(--ant-color-text);
  --ant-color-primary-hover: var(--ant-color-primary);
  --ant-red-6: var(--color-warning);
  --ant-green-6: var(--color-green);
  --ant-color-split: var(--color-bg-secondary); //捲軸背景顏色
  --ant-color-link: var(--color-info);

  .ant-layout {
    --ant-layout-body-bg: var(--color-bg-primary);
  }

  .ant-menu {
    --ant-menu-item-selected-color: var(--color-primary);
    --ant-menu-sub-menu-item-selected-color: var(--color-primary);
    --ant-menu-item-selected-bg: transparent;
    --ant-menu-color-item-bg-active: transparent;
    --ant-menu-color-item-bg-selected: transparent;
    --ant-menu-item-hover-bg: var(--color-bg-hover);
    --ant-menu-item-border-radius: 0;
  }
  .ant-input {
    --ant-input-padding-block: 8px;
    --ant-input-padding-inline: 10px;
    --ant-input-active-border-color: var(--color-primary);
    --ant-input-hover-border-color: var(--color-text-secondary);
    --ant-color-border: var(--color-component-border);
  }
  .ant-input-affix-wrapper-password,
  .ant-input-password {
    --ant-input-padding-block: 8px;
    --ant-input-padding-inline: 10px;
    --ant-input-active-border-color: var(--color-primary);
    --ant-input-hover-border-color: var(--color-text-secondary);
    --ant-menu-item-border-radius: 0;
  }
  .ant-input-filled:focus,
  .ant-input-filled:focus-within {
    --ant-input-active-border-color: var(--color-primary);
  }

  .ant-btn {
    --ant-button-default-hover-color: var(--ant-color-primary);
    --ant-button-default-hover-border-color: var(--ant-color-primary);
    --ant-button-default-color: var(--color-text-secondary);
    --ant-button-default-border-color: var(--color-component-border);
    --ant-color-primary-text-hover: var(--ant-color-primary);
  }

  .ant-select-item {
    --ant-select-option-font-size: var(--ant-font-size);
    --ant-select-option-hover-bg: var(--color-bg-info);
    --ant-select-option-selected-bg: var(--color-component-active);
  }

  .ant-table {
    --ant-table-header-color: var(--color-text);
    --ant-table-header-bg: var(--color-bg-secondary);
    --ant-table-header-border-radius: 0;
    --ant-table-cell-padding-block: 10px;
    --ant-line-height: 1.3;
    --ant-table-cell-font-size: var(--text-size-base);
    --ant-table-border-color: var(--color-component-border);
    --ant-table-sticky-scroll-bar-bg: var(--color-text-placeholder); //捲軸顏色
  }

  .ant-pagination {
    --ant-pagination-item-size: 24px;
    --ant-pagination-item-active-bg: transparent;
    --ant-pagination-item-bg: transparent;
  }
  // // 移除 tabs 的 margin
  .ant-tabs {
    --ant-tabs-horizontal-margin: 0;
    --ant-tabs-title-font-size: 12px;
  }
  .ant-tag {
    --ant-tag-default-color: var(--color-text-icon);
    --ant-tag-default-bg: var(--color-bg-info);
  }
  .ant-list {
    --ant-list-header-bg: var(--color-bg-secondary);
  }
}

.ant-switch.ant-switch-checked,
.ant-switch.ant-switch-checked:hover:not(.ant-switch-disabled) {
  background-color: var(--color-success);
}

/* checkbox */
.ant-checkbox-checked .ant-checkbox-inner,
.ant-checkbox-wrapper:not(.ant-checkbox-wrapper-disabled):hover
  .ant-checkbox-checked:not(.ant-checkbox-disabled)
  .ant-checkbox-inner {
  background-color: var(--color-info);
  border-radius: 0;
  border-color: var(--color-component-border);
}
.ant-checkbox-indeterminate .ant-checkbox-inner:after {
  height: 2px;
  background-color: var(--color-info);
}

.ant-select-dropdown {
  border-radius: 0;
  padding: 4px 0;
  .ant-select-item-option-selected {
    border-radius: 0;
  }
}

/* table */
.ant-table-wrapper .ant-table {
  scrollbar-color: inherit;
}

.ant-table-container {
  border: 1px solid var(--color-component-border);
}

.ant-table-thead {
  background-color: var(--color-bg-secondary);
  .ant-table-cell {
    --ant-table-cell-padding-block: 16px;
  }
}

.responsive-scroll-table {
  &.ant-table-wrapper {
    flex: 1;
    min-height: 0;
  }
  .ant-spin-nested-loading {
    height: 100%;
  }
  .ant-spin-container {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .ant-table {
    flex: 1;
    min-height: 0;
  }
  .ant-table-container {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .ant-table-body {
    flex: 1;
  }
}

// table 空樣式zindex太高導致蓋到彈窗
.ant-table-placeholder {
  z-index: 0 !important;
}

.ant-table-empty {
  .ant-table-tbody > tr > th,
  .ant-table-tbody > tr > td {
    border-bottom: none;
  }
}

.ant-pagination {
  .ant-pagination-prev,
  .ant-pagination-next {
    line-height: 24px;
    --ant-pagination-item-size: 12px;
  }
}

.ant-modal {
  --ant-modal-title-font-size: 14px;

  .ant-modal-content {
    border-radius: 4px;
    padding: 0;
  }

  .ant-modal-header {
    padding: 12px 20px;
    border-bottom: 1px solid var(--color-component-border);
  }
  .ant-modal-close {
    top: 16px;
    right: 20px;
    height: 0;
    width: 16px;
  }
  .ant-modal-body {
    padding: 28px 20px;
  }
  .ant-modal-footer {
    text-align: center;
    margin-top: 0;
    padding: 12px 20px;
  }
}
.login-page-form .ant-form-item .ant-form-item-label > label {
  font-size: 14px !important;
}

.ant-form-item .ant-form-item-explain-error {
  //別問我我也不知道為什麼要用綠色的？？
  color: var(--color-warning);
}

.ant-tabs {
  .ant-tabs-tab-btn {
    color: var(--color-text-secondary); // 預設的 tab 文字顏色
  }
  .ant-tabs-item-hover-color {
    color: var(--color-primary);
  }

  .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
    color: var(--color-primary);
  }
  .ant-tabs-ink-bar {
    background-color: var(--color-primary);
  }
}

// Layout Tabs - Generic styling for game-level tabs
.game-tabs {
  .ant-tabs-nav {
    margin-bottom: 0;
  }

  .ant-tabs-tab {
    padding: 12px 24px;
    font-size: 12px;
    font-weight: 500;

    &:hover {
      .ant-tabs-tab-btn {
        color: var(--color-text);
      }
    }
  }

  .ant-tabs-tab.ant-tabs-tab-active {
    .ant-tabs-tab-btn {
      color: var(--color-text);
      font-weight: 600;
    }
  }

  .ant-tabs-ink-bar {
    height: 3px;
    background-color: var(--color-primary);
  }
}

.ant-btn-color-primary.ant-btn-variant-outlined:hover {
  background-color: #3769951a !important;
}

.ant-btn-color-red.ant-btn-variant-outlined:hover {
  background-color: #ff43431a !important;
}

.ant-btn-color-green.ant-btn-variant-outlined:hover {
  background-color: #42b5961a !important;
}

.btn-success {
  --ant-color-primary: var(--color-success);
  &.ant-btn-variant-outlined {
    --ant-button-default-color: var(--color-success);
    --ant-button-default-border-color: var(--color-success);
    color: var(--color-success);
    &:hover {
      background-color: #38c5221a !important;
      color: var(--color-success);
    }
  }
}

.ant-modal-confirm {
  .ant-modal-confirm-title {
    padding: 12px 20px;
    border-bottom: 1px solid var(--color-component-border);
  }
  .ant-modal-confirm-paragraph {
    width: 100%;
  }

  .ant-modal-confirm-content {
    padding: 28px 20px;
    text-align: center;
  }

  .ant-modal-confirm-btns {
    text-align: center;
    margin-top: 0;
    padding: 12px 20px;
    .ant-btn {
      padding: 0 48px;
    }
    display: flex;
    flex-direction: row-reverse;
    gap: 16px;
    justify-content: center;
  }
}

/* list */
.ant-list {
  .ant-list-header {
    padding: 10px 20px;
  }
  &.ant-list-bordered {
    border-radius: 0;
  }
}

.scroll-list {
  &.ant-list {
    display: flex;
    flex-direction: column;
  }
  .ant-spin-nested-loading {
    flex: 1;
    // height: 100%;
    min-height: 0;
  }
  .ant-spin-container {
    height: 100%;
    overflow-y: auto;
  }
  // .ant-list-items {
  //   height: 100%;
  // }
}

/** button */
.ant-btn-variant-link {
  text-decoration: underline;
  text-decoration-color: var(--color-info);
}
.ant-btn {
  padding: 6px var(--ant-button-padding-inline);
  &.ant-btn-sm {
    padding: 4px 8px;
  }
}

/* input */
.ant-input-search {
  .ant-input {
    border-inline-end: none;
  }
  .ant-input-group-addon {
    border: none;
  }
  .ant-input-search-button {
    border: none;
    background-color: transparent;
  }
}
.ant-input-number-affix-wrapper {
  .ant-input-number-suffix {
    background-color: #f5f5f5;
    margin-inline-end: 0;
    margin-inline-start: 0;
    height: 30px;
    padding: 8px 10px;
    border-left: 1px solid var(--color-component-border);
  }
}

/* tag */
.ant-tag.tag-primary {
  color: #fff;
  background: var(--color-primary);
  border-color: var(--color-primary);
  border-radius: 8px;
}
.ant-tag.tag-outline-primary {
  color: var(--color-primary);
  background: #fff;
  border-color: var(--color-primary);
  border-radius: 8px;
  padding: 0 10px;
}

.custom-table {
  .ant-table-container {
    .ant-table-body,
    .ant-table-content {
      scrollbar-width: thin;
      scrollbar-color: #eaeaea transparent;
      scrollbar-gutter: stable;
    }
  }
}

.ant-upload-list-text {
  max-height: 200px;
  overflow-y: auto;
}
