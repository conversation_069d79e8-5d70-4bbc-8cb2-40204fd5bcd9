import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

import { Language } from '@/enums/language';

// Todo: 動態引入glob vite
const localeModules: Record<string, { default: Record<string, string> }> = import.meta.glob(
  './locales/*.json',
  { eager: true }
);

const resources: Record<string, { translation: Record<string, string> }> = {};

Object.entries(localeModules).forEach(([path, module]) => {
  // 從路徑中提取語言代碼
  const match = path.match(/\.\/locales\/(.+)\.json$/);
  if (match && match[1]) {
    const langCode = match[1];
    resources[langCode] = {
      translation: module.default
    };
  }
});

// 從 localStorage 獲取使用者選擇的語言
const storedLanguage = localStorage.getItem('language');

// 獲取瀏覽器語系
const detectBrowserLanguage = (): string => {
  const browserLanguage = navigator.language;

  if (browserLanguage.toLowerCase().startsWith('zh')) {
    return Language.ZH_TW;
  } else {
    return Language.EN;
  }
};

const determineLanguage = (): string => {
  if (storedLanguage) {
    try {
      return JSON.parse(storedLanguage);
    } catch {
      return storedLanguage;
    }
  }
  return detectBrowserLanguage();
};

i18n.use(initReactI18next).init({
  resources,
  fallbackLng: Language.ZH_TW,
  lng: determineLanguage(),
  interpolation: {
    //不自動轉義文本內容的ＨＴＭＬ標籤  defaultLanguage
    escapeValue: false
  }
});

export default i18n;
