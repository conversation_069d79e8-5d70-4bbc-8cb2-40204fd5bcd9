import { HolderOutlined } from '@ant-design/icons';
import type { DragEndEvent } from '@dnd-kit/core';
import { DndContext } from '@dnd-kit/core';
import type { SyntheticListenerMap } from '@dnd-kit/core/dist/hooks/utilities';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Button } from 'antd';
import React, { useContext, useMemo } from 'react';

import RTable, { RTableProps } from '@/components/RTable';

interface RowContextProps {
  setActivatorNodeRef?: (element: HTMLElement | null) => void;
  listeners?: SyntheticListenerMap;
}

const RowContext = React.createContext<RowContextProps>({});

const DragHandle: React.FC = () => {
  const { setActivatorNodeRef, listeners } = useContext(RowContext);
  return (
    <Button
      type="text"
      size="small"
      icon={<HolderOutlined />}
      style={{ cursor: 'move' }}
      ref={setActivatorNodeRef}
      {...listeners}
    />
  );
};

interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  'data-row-key': string;
  'data-is-sortable'?: boolean;
}

const Row: React.FC<RowProps> = (props) => {
  const isSortable = props['data-is-sortable'] !== false;

  const sortableProps = useSortable({
    id: props['data-row-key'],
    disabled: !isSortable
  });

  const {
    attributes,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
    transform,
    transition,
    isDragging
  } = sortableProps;

  const style: React.CSSProperties = {
    ...props.style,
    ...(isSortable ? {
      transform: CSS.Translate.toString(transform),
      transition,
      ...(isDragging ? { position: 'relative', zIndex: 9999 } : {})
    } : {})
  };

  const contextValue = useMemo<RowContextProps>(
    () => ({
      setActivatorNodeRef: isSortable ? setActivatorNodeRef : undefined,
      listeners: isSortable ? listeners : undefined
    }),
    [setActivatorNodeRef, listeners, isSortable]
  );

  return (
    <RowContext.Provider value={contextValue}>
      <tr {...props} ref={isSortable ? setNodeRef : undefined} style={style} {...(isSortable ? attributes : {})} />
    </RowContext.Provider>
  );
};

interface DragTableProps<T extends object> extends RTableProps<T> {
  onSortChange: (data: T[]) => void;
  keyName: string | number | symbol;
  dataSource: T[];
  columns: RTableProps<T>['columns'];
  excludeFirstRowFromDrag?: boolean;
}

const DragTable = <T extends object>({
  onSortChange,
  dataSource,
  keyName,
  columns,
  excludeFirstRowFromDrag = false,
  ...props
}: DragTableProps<T>) => {
  // Split data into fixed first row and sortable rows
  const firstRow = excludeFirstRowFromDrag && dataSource.length > 0 ? dataSource[0] : null;
  const sortableData = excludeFirstRowFromDrag && dataSource.length > 0 ? dataSource.slice(1) : dataSource;

  const dragTableColumns = [
    {
      key: 'sort',
      width: 30,
      render: (_: unknown, record: T) => {
        // Don't show drag handle for the first row if it's excluded from dragging
        if (excludeFirstRowFromDrag && firstRow && record[keyName as keyof T] === firstRow[keyName as keyof T]) {
          return null;
        }
        return <DragHandle />;
      }
    },
    ...columns
  ];

  const onDragEnd = ({ active, over }: DragEndEvent) => {
    if (active.id !== over?.id) {
      const activeIndex = sortableData.findIndex(
        (record) => record[keyName as keyof T] === active?.id
      );
      const overIndex = sortableData.findIndex((record) => record[keyName as keyof T] === over?.id);
      const movedSortableData = arrayMove(sortableData, activeIndex, overIndex);

      // Only pass sortable data to onSortChange callback
      onSortChange?.(movedSortableData);
    }
  };

  // Only include sortable items in the drag context
  // @ts-expect-error keyName is not null
  const items = sortableData.map((i) => i[keyName]);

  // Custom row component that knows about sortability
  const CustomRow: React.FC<RowProps> = (rowProps) => {
    const rowKey = rowProps['data-row-key'];
    const isSortable = !excludeFirstRowFromDrag || !firstRow || rowKey !== String(firstRow[keyName as keyof T]);

    return <Row {...rowProps} data-is-sortable={isSortable} />;
  };

  return (
    <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
      <SortableContext items={items} strategy={verticalListSortingStrategy}>
        <RTable<T>
          rowKey={keyName as string}
          components={{ body: { row: CustomRow } }}
          columns={dragTableColumns}
          dataSource={dataSource}
          {...props}
        />
      </SortableContext>
    </DndContext>
  );
};

export default DragTable;
