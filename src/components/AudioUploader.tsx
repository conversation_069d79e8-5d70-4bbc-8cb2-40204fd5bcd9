// src/components/AudioUploader.tsx
import { message, Upload } from 'antd';
import type { RcFile, UploadProps } from 'antd/es/upload/interface';
import type { UploadRequestOption } from 'rc-upload/lib/interface';

import UploadIcon from '@/assets/img/icon/upload.svg?react';
import { AudioFile } from '@/types/platform';

interface AudioUploaderProps {
  audioFile?: AudioFile;
  onUploadSuccess: (file: File, url: string) => void;
}

const AudioUploader = ({ audioFile, onUploadSuccess }: AudioUploaderProps) => {
  const { t } = useTranslation();
  const uploadProps: UploadProps = {
    name: 'file',
    accept: 'audio/*',
    multiple: false,
    showUploadList: false,
    beforeUpload: (file: RcFile) => {
      const isAudio = file.type.startsWith('audio/');
      if (!isAudio) {
        message.error(t('components_audioUploader_description'));
        return Upload.LIST_IGNORE;
      }
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        message.error(t('components_audioUploader_limit'));
        return Upload.LIST_IGNORE;
      }
      return true;
    },
    onChange: (info) => {
      if (info.file.status === 'error') {
        message.error(`${info.file.name} 上傳失敗`);
      }
    },
    customRequest: (options: UploadRequestOption) => {
      const { file, onSuccess, onError } = options;

      const handleUpload = async () => {
        try {
          if (file instanceof File) {
            const url = URL.createObjectURL(file);
            onSuccess?.({ url });
            onUploadSuccess(file, url);
          } else {
            throw new Error('Invalid file type');
          }
        } catch (err) {
          onError?.(err instanceof Error ? err : new Error('Upload failed'));
        }
      };

      handleUpload();
    }
  };

  return (
    <Upload {...uploadProps}>
      <div className="flex items-center gap-4 transition-colors">
        <div className="flex items-center gap-1 px-2 py-0.5 bg-bg-secondary border border-gray-200 rounded-sm cursor-pointer text-text hover:text-primary">
          <UploadIcon className="w-6 h-6" />
          <span>{t('common_upload')}</span>
        </div>
        {audioFile?.url ? (
          <div className="flex items-center gap-4">
            <span className="block overflow-hidden truncate text-text max-w-60 whitespace-nowrap">
              {audioFile.name}
            </span>
            <audio controls src={audioFile.url} className="h-12" />
          </div>
        ) : (
          <div className="py-2 pl-2 border-2 border-dotted rounded-sm text-text-secondary w-72 border-component-border">
            {t('components_audioUploader_upload')}
          </div>
        )}
      </div>
    </Upload>
  );
};

export default AudioUploader;
