import clsx from 'clsx';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';

import RDatePicker from '@/components/RDatePicker';

type QuickTimeSelectItem = {
  key: string;
  label: string;
  value: [dayjs.Dayjs, dayjs.Dayjs];
};

const QuickTimeSelectItemList: QuickTimeSelectItem[] = [
  {
    key: 'today',
    label: 'components_quickDateSelect_today',
    value: [dayjs().startOf('day'), dayjs().endOf('day')]
  },
  {
    key: 'yesterday',
    label: 'components_quickDateSelect_yesterday',
    value: [dayjs().subtract(1, 'day').startOf('day'), dayjs().subtract(1, 'day').endOf('day')]
  },
  {
    key: 'thisWeek',
    label: 'components_quickDateSelect_thisWeek',
    value: [dayjs().startOf('week'), dayjs().endOf('week')]
  },
  {
    key: 'lastWeek',
    label: 'components_quickDateSelect_lastWeek',
    value: [dayjs().subtract(1, 'week').startOf('week'), dayjs().subtract(1, 'week').endOf('week')]
  },
  {
    key: 'thisMonth',
    label: 'components_quickDateSelect_thisMonth',
    value: [dayjs().startOf('month'), dayjs().endOf('month')]
  },
  {
    key: 'lastMonth',
    label: 'components_quickDateSelect_lastMonth',
    value: [
      dayjs().subtract(1, 'month').startOf('month'),
      dayjs().subtract(1, 'month').endOf('month')
    ]
  },
  {
    key: 'custom',
    label: 'components_quickDateSelect_custom',
    value: [dayjs().startOf('day'), dayjs().endOf('day')]
  }
];

const QuickTimeButtonList = ({
  activeKey,
  onChange
}: {
  activeKey: string;
  onChange: (key: string, value: [number, number]) => void;
}) => {
  const { t } = useTranslation();
  const handleQuickTimeClick = useCallback(
    (key: string, value: [dayjs.Dayjs, dayjs.Dayjs]) => {
      onChange(key, [value[0].valueOf(), value[1].valueOf()]);
    },
    [onChange]
  );
  const button = (item: QuickTimeSelectItem) => {
    return (
      <div
        className={clsx(
          'px-3 py-2 cursor-pointer text-text-secondary hover:bg-bg-info text-xs text-nowrap',
          activeKey === item.key &&
            'bg-text-secondary text-white hover:bg-text-secondary rounded-sm'
        )}
        key={item.key}
        onClick={() => handleQuickTimeClick(item.key, item.value)}
      >
        {t(item.label)}
      </div>
    );
  };
  return (
    <div className="flex border rounded-sm border-component-border">
      {QuickTimeSelectItemList.map((item) => button(item))}
    </div>
  );
};

QuickTimeSelectItemList.map((item) => ({
  key: item.key,
  label: item.label,
  value: item.value
}));

export const QuickDateSelect = ({ ...props }) => {
  const { value, onChange, defaultActiveKey } = props;
  const [activeKey, setActiveKey] = useState<string>(defaultActiveKey || '');

  const handleChange = useCallback(
    (key: string, value: [number, number]) => {
      setActiveKey(key);
      onChange?.(value);
    },
    [onChange]
  );

  const handleDateChange = useCallback(
    (dates: null | (Dayjs | null)[]) => {
      if (dates && dates[0] && dates[1]) {
        handleChange('custom', [dates[0]?.valueOf() ?? 0, dates[1]?.valueOf() ?? 0]);
      }
    },
    [handleChange]
  );

  return (
    <div className="flex gap-2">
      <QuickTimeButtonList activeKey={activeKey} onChange={handleChange} />
      <RDatePicker.RangePicker
        value={value ? [dayjs(value[0]), dayjs(value[1])] : null}
        onChange={handleDateChange}
        showTime
      />
    </div>
  );
};

export default QuickDateSelect;
