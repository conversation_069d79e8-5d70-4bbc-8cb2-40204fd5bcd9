import { Spin } from 'antd';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import DownloadIcon from '@/assets/img/icon/download.svg?react';
import CopyIcon from '@/components/CopyIcon';
import { RTooltip } from '@/components/Rtooltip';
import { cn } from '@/utils/classname';

import RButton from './RButton';
import RModal from './RModal';

type ExportRecordProps = {
  onClick: () => Promise<void> | void;
  id?: number;
  isLoading?: boolean;
  className?: string;
};

const ExportRecord = ({ onClick, id, isLoading = false, className }: ExportRecordProps) => {
  const { t } = useTranslation();
  const [openModal, setOpenModal] = useState(false);
  const navigate = useNavigate();

  const handleClick = async () => {
    onClick();
    setOpenModal(true);
  };

  const handleNavigate = () => {
    setOpenModal(false);
    navigate('/agent/recordExport');
  };

  const handleClose = () => {
    setOpenModal(false);
  };

  const renderModalContent = () => (
    <div>
      <p className="mb-5">
        {`${t('pages_agent_recordExport_modalInfo')} `}
        <span className="text-blue-500 cursor-pointer" onClick={handleNavigate}>
          {t('pages_agent_recordExport_title')}
        </span>
        {` ${t('pages_agent_recordExport_modalInfo_content')}`}
      </p>
      {isLoading ? (
        <div className="flex items-center justify-center py-4">
          <Spin />
        </div>
      ) : (
        id && (
          <div className="flex items-center gap-4">
            <span className="text-text-secondary">
              {t('pages_agent_recordExport_title')}
              {`${t('common_id')} : `}
            </span>
            {id}
            <CopyIcon text={id.toString()} />
          </div>
        )
      )}
      <div className="mx-auto mt-15 w-30">
        <RButton type="default" className="!w-30" onClick={handleClose}>
          {t('common_close')}
        </RButton>
      </div>
    </div>
  );

  return (
    <>
      <div className={cn('flex justify-end mb-4', className)}>
        <RTooltip placement="bottomRight" title={t('common_export')} arrow={false} color="gray">
          <span>
            <RButton type="default" onClick={handleClick} loading={isLoading} disabled={isLoading}>
              <DownloadIcon width={16} height={16} />
            </RButton>
          </span>
        </RTooltip>
      </div>
      <RModal
        open={openModal}
        onCancel={handleClose}
        title={t('pages_agent_recordExport_title')}
        footer={null}
      >
        {renderModalContent()}
      </RModal>
    </>
  );
};

export default ExportRecord;
