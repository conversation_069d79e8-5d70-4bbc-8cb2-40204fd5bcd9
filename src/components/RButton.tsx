import { Button } from 'antd';
import { ButtonProps } from 'antd/es/button';

import { cn } from '@/utils/classname';

type CustomButtonProps = {
  color?:
    | 'default'
    | 'primary'
    | 'danger'
    | 'purple'
    | 'green'
    | 'red'
    | 'cyan'
    | 'success'
    | 'orange';
};

interface RButtonProps extends Omit<ButtonProps, 'color'>, CustomButtonProps {
  children: React.ReactNode;
  type?: 'primary' | 'dashed' | 'link' | 'text' | 'default';
}

const successClass = 'btn-success bg-green-500 text-white hover:bg-green-600';
const dangerClass = 'btn-danger !bg-[#F86464] !text-white';

export const RButton = ({
  children,
  className,
  type = 'primary',
  color = 'primary',
  ...props
}: RButtonProps) => {
  const getColorClass = (color: CustomButtonProps['color']) => {
    switch (color) {
      case 'success':
        return successClass;
      case 'danger':
        return dangerClass;
      default:
        return '';
    }
  };

  const colorClass = getColorClass(color);

  return (
    <Button
      type={type}
      className={cn('w-fit', colorClass, className)}
      color={color as ButtonProps['color']}
      {...props}
    >
      {children}
    </Button>
  );
};

export default RButton;
