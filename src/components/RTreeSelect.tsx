import { Tree, type TreeDataNode, type TreeProps } from 'antd';
import { useCallback } from 'react';

interface RTreeProps extends TreeProps {
  data: TreeDataNode[];
  onChange?: (checkedKeys: string[]) => void;
}

const RTreeSelect = ({ data, onChange, ...props }: RTreeProps) => {
  const handleSelect: TreeProps['onCheck'] = useCallback(
    (checkedKeys: React.Key[] | { checked: React.Key[]; halfChecked: React.Key[] }) => {
      if (Array.isArray(checkedKeys)) {
        onChange?.(checkedKeys.map(String));
      } else {
        onChange?.(checkedKeys.checked.map(String));
      }
    },
    [onChange]
  );

  return <Tree checkable treeData={data} onCheck={handleSelect} {...props} />;
};

export default RTreeSelect;
