import { useTranslation } from 'react-i18next';

import RForm from './RForm';
import RSelect from './RSelect';

type CategoryAndChannelSelectProps = {
  categoryOptions: { label: string; value: string }[];
  channelOptions: { label: string; value: string }[];
  isChannelsLoading?: boolean;
  onCategoryChange?: (value: string) => void;
};

export const CategoryAndChannelSelect = ({
  categoryOptions,
  channelOptions,
  isChannelsLoading,
  onCategoryChange
}: CategoryAndChannelSelectProps) => {
  const { t } = useTranslation();
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const form = RForm.useFormInstance();

  // 重置後清空selectedCategory狀態 讓通道回覆禁用狀態
  useEffect(() => {
    const categoryValue = form.getFieldValue('categoryKey');
    setSelectedCategory(categoryValue || '');
  }, [form.getFieldValue('categoryKey')]);

  return (
    <>
      <RForm.Item name="categoryKey" label={t('pages_transaction_topupsetting_popular_category')}>
        <RSelect
          options={categoryOptions}
          onChange={(value) => {
            setSelectedCategory(value);
            onCategoryChange?.(value);
            form.setFieldsValue({ channelKey: 'all' });
          }}
          placeholder={t('common_all')}
        />
      </RForm.Item>
      <RForm.Item name="channelKey" label={t('pages_transaction_topupsetting_popular_channel')}>
        <RSelect
          options={channelOptions}
          disabled={!selectedCategory || isChannelsLoading}
          loading={isChannelsLoading}
        />
      </RForm.Item>
    </>
  );
};
