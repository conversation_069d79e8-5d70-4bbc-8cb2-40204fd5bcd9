import RButton from '@/components/RButton';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';

export type FormValue = {
  oldPassword?: string;
  newPassword: string;
  confirmPassword: string;
};

interface PasswordResetComponentProps {
  onFinish: (values: FormValue) => void;
  isLoading: boolean;
  confirmPasswordRules: React.ComponentProps<typeof RForm.Item>['rules'];
}

// 密碼驗證規則
const createPasswordRules = (t: (key: string) => string) => [
  { required: true, message: t('login_error_password_required') },
  {
    validator: (_: unknown, value: string) => {
      if (value && (value.length < 6 || value.length > 20)) {
        return Promise.reject(t('login_error_length'));
      }
      const pattern = /^[A-Za-z0-9]+$/;
      if (value && !pattern.test(value)) {
        return Promise.reject(t('login_error_pattern'));
      }

      return Promise.resolve();
    }
  }
];

export const PasswordResetComponent = ({
  onFinish,
  isLoading,
  confirmPasswordRules
}: PasswordResetComponentProps) => {
  const { t } = useTranslation();

  const passwordRules = useMemo(() => createPasswordRules(t), [t]);

  return (
    <>
      <div className="fixed inset-0 z-50 bg-[#00000080]" />
      <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[480px] bg-white rounded-sm py-7 px-5 z-50">
        <div className="flex justify-between">
          <h2 className="text-[14px] font-semibold mb-7">{t('login_reset_password')}</h2>
          <span className=" text-gray-500">Step 1/2</span>
        </div>
        <div className="mt-10">
          <RForm<FormValue> name="resetPassword" layout="vertical" onFinish={onFinish}>
            <RForm.Item
              label={t('login_reset_password_label_newPassword')}
              name="newPassword"
              rules={passwordRules}
            >
              <RInput
                type="password"
                placeholder={t('login_placeholder_password')}
                autoComplete="new-password"
                // iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
              />
            </RForm.Item>
            <RForm.Item
              label={t('login_reset_password_label_confirmPassword')}
              name="confirmPassword"
              dependencies={['newPassword']}
              rules={confirmPasswordRules}
            >
              <RInput
                type="password"
                placeholder={t('login_reset_password_placeholder_confirmPassword')}
                autoComplete="new-password"
                // iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
              />
            </RForm.Item>
            <div className="mt-48 flex justify-center">
              <RButton type="primary" className="!w-1/2" htmlType="submit" loading={isLoading}>
                {t('common_submit')}
              </RButton>
            </div>
          </RForm>
        </div>
      </div>
    </>
  );
};
