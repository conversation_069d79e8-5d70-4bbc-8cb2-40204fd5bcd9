import { EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

import RButton from '@/components/RButton';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
export interface LoginForm {
  username: string;
  password: string;
  confirmPassword?: string;
  otp?: string;
}

interface LoginFormComponentProps {
  onFinish: (values: LoginForm) => void;
  isLoading: boolean;
}

// 驗證規則函數
const createInputRules = (t: (key: string) => string) => [
  { required: true, message: t('login_error_username_required') },
  {
    validator: (_: unknown, value: string) => {
      if (value && (value.length < 6 || value.length > 20)) {
        return Promise.reject(t('login_error_length'));
      }
      const pattern = /^[A-Za-z0-9]+$/;
      if (value && !pattern.test(value)) {
        return Promise.reject(t('login_error_pattern'));
      }
      return Promise.resolve();
    }
  }
];

export const LoginFormComponent = ({ onFinish, isLoading }: LoginFormComponentProps) => {
  const { t } = useTranslation();

  const inputRules = useMemo(() => createInputRules(t), [t]);

  return (
    <div className="w-[480px] rounded-xl bg-white px-10 pt-7 pb-1 shadow-lg login-page-form">
      <RForm name="login" layout="vertical" onFinish={onFinish} autoComplete="on">
        <RForm.Item label={t('login_label_account')} name="username" rules={inputRules}>
          <RInput
            placeholder={t('login_placeholder_account')}
            autoComplete="username"
            style={{ fontSize: '14px' }}
          />
        </RForm.Item>

        <RForm.Item label={t('login_label_password')} name="password" rules={inputRules}>
          <RInput
            type="password"
            placeholder={t('login_placeholder_password')}
            autoComplete="current-password"
            style={{ fontSize: '14px' }}
            iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
          />
        </RForm.Item>

        <RForm.Item className="mb-0">
          <div className="!w-80 mx-auto mt-10">
            <RButton
              type="primary"
              htmlType="submit"
              className="!w-80 !text-[14px]"
              loading={isLoading}
            >
              {t('login')}
            </RButton>
          </div>
        </RForm.Item>
      </RForm>
    </div>
  );
};
