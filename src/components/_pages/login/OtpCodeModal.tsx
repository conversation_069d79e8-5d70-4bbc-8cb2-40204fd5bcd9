import RButton from '@/components/RButton';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';

interface OtpCodeModeProps {
  handleOtpInputSubmit: (values: { otp: string }) => void;
  isLoading: boolean;
  isPending: boolean;
}

export const OtpCodeMode = ({ handleOtpInputSubmit, isLoading, isPending }: OtpCodeModeProps) => {
  const { t } = useTranslation();

  return (
    <>
      <div className="fixed inset-0 z-50 bg-transparent" />
      <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[480px] bg-white rounded-xl py-7 px-5 z-50 shadow-md">
        <h2 className="text-[14px] font-semibold">{t('login_otp')}</h2>
        <div className="mt-2">
          <RForm name="otpForm" layout="vertical" onFinish={handleOtpInputSubmit}>
            <RForm.Item name="otp">
              <RInput placeholder={t('login_otp_title')} className="login-input" maxLength={6} />
            </RForm.Item>
            <div className="flex justify-center mt-12 gap-4">
              <RButton
                type="primary"
                className="!w-2/3 !text-[14px]"
                htmlType="submit"
                loading={isPending || isLoading}
              >
                {t('common_submit')}
              </RButton>
            </div>
          </RForm>
        </div>
      </div>
    </>
  );
};

export default OtpCodeMode;
