import { message } from 'antd';

import AudioUploader from '@/components/AudioUploader';
import RButton from '@/components/RButton';
import RTable from '@/components/RTable';
import { SoundEffectType } from '@/enums/platform';
import { AudioFile } from '@/types/platform';

interface TableData {
  key: SoundEffectType;
  name: string;
  description: string;
}

interface ExtendedAudioFile extends AudioFile {
  file?: File;
}

interface SoundEffectSettingsProps {
  onSave: (data: Record<SoundEffectType, ExtendedAudioFile | undefined>) => void;
  onChange: (data: Record<SoundEffectType, AudioFile | undefined>) => void;
  initialData?: Record<SoundEffectType, AudioFile | undefined>;
}

const SOUND_EFFECT_TYPES = [
  SoundEffectType.BACKGROUND_MUSIC,
  SoundEffectType.CLICK_OPEN_SOUND,
  SoundEffectType.CLICK_CLOSE_SOUND,
  SoundEffectType.CLICK_HINT_SOUND,
  SoundEffectType.CLICK_FAILED_SOUND
];

const SoundEffectSettings = ({ onSave, onChange, initialData }: SoundEffectSettingsProps) => {
  const { t } = useTranslation();

  const initialAudioFiles = SOUND_EFFECT_TYPES.reduce(
    (acc, type) => {
      acc[type] = undefined;
      return acc;
    },
    {} as Record<SoundEffectType, ExtendedAudioFile | undefined>
  );

  const [audioFiles, setAudioFiles] =
    useState<Record<SoundEffectType, ExtendedAudioFile | undefined>>(initialAudioFiles);

  // 初始化時載入初始資料
  useEffect(() => {
    if (initialData) {
      setAudioFiles(initialData as Record<SoundEffectType, ExtendedAudioFile | undefined>);
    }
  }, [initialData]);

  const handleUploadSuccess = (key: SoundEffectType, file: File, url: string) => {
    const newAudioFiles = {
      ...audioFiles,
      [key]: {
        url,
        name: file.name,
        file: file
      }
    };

    setAudioFiles(newAudioFiles);
    onChange(newAudioFiles);
    message.success(t('pages_platform_upload_success', { name: file.name }));
  };

  const columns = [
    {
      title: t('pages_platform_item'),
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (text: string, record: TableData) => (
        <div className="text-xs">
          <p>{text}</p>
          <p className="text-gray-400">{record.description}</p>
        </div>
      )
    },
    {
      title: t('pages_platform_content'),
      dataIndex: 'content',
      key: 'content',
      render: (_text: string, record: TableData) => (
        <AudioUploader
          audioFile={audioFiles[record.key]}
          onUploadSuccess={(file, url) => handleUploadSuccess(record.key, file, url)}
        />
      )
    }
  ];

  const dataSource: TableData[] = SOUND_EFFECT_TYPES.map((type) => ({
    key: type,
    name: t(`pages_platform_sound_${type}`),
    description: t(`pages_platform_sound_${type}_description`)
  }));

  const handleSave = () => {
    onSave(audioFiles);
  };

  return (
    <>
      <style>
        {`
          .sound-effect-table table-cell-padding-block {
            --ant-table-cell-padding-block: 6px !important;
          }
        `}
      </style>
      <div className="px-6 flex items-end gap-3 mt-9 mb-5">
        <h2 className="m-0 text-sm font-bold">{t('pages_platform_sound_title')}</h2>
        <p className="text-xs text-gray-500">{t('pages_platform_description')}</p>
      </div>

      <div className="m-6 px-2 py-1.5 w-56 bg-[#FF83171A] border border-[#FF8317] rounded flex items-center">
        <span className="flex items-center justify-center w-4.5 h-4.5 mr-2 bg-[#FF8317] text-white rounded-full text-xs">
          !
        </span>
        <p>{t('pages_platform_sound_limit')}</p>
      </div>

      <div className="mx-6 bg-gray-100 rounded">
        <RTable
          columns={columns}
          dataSource={dataSource}
          pagination={{
            hideOnSinglePage: true,
            pageSize: dataSource.length,
            current: 1,
            showSizeChanger: false
          }}
          className="bg-white sound-effect-table"
        />
      </div>

      <div className="m-6">
        <RButton type="primary" className="!w-30" onClick={handleSave}>
          {t('common_save')}
        </RButton>
      </div>
    </>
  );
};

export default SoundEffectSettings;
