import { FormInstance } from 'antd';

import RButton from '@/components/RButton';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RInputNumber from '@/components/RInputNumber';
import RSwitch from '@/components/RSwitch';
import { GeneralSettingsData } from '@/types/platform';

interface FormValue {
  customerServiceLink: string;
  frontendBindPhoneSetting: {
    changeable: 1 | 0;
    fee: string;
  };
  frontendChangeNameSetting: {
    isFree: 1 | 0;
    freeTimes: number;
    fee: string;
  };
}

const ItemWrap = ({ children }: { children: React.ReactNode }) => {
  return <div className="px-4 py-3 rounded-lg bg-bg-primary">{children}</div>;
};

const ChangeNameSetting = ({ form }: { form: FormInstance<FormValue> }) => {
  const { t } = useTranslation();
  const isChangeNameFree = RForm.useWatch(['frontendChangeNameSetting', 'isFree'], form);
  return (
    <ItemWrap>
      <RForm.Item
        name={['frontendChangeNameSetting', 'isFree']}
        label={t('pages_platform_general_freeEditName')}
        layout="horizontal"
        className="!mb-0"
        initialValue={isChangeNameFree}
        rules={[
          {
            required: true
          }
        ]}
      >
        <RSwitch isBoolean={false}></RSwitch>
      </RForm.Item>
      <span className="mb-4 text-xs text-text-secondary">
        {t('pages_platform_general_freeEditName_description')}
      </span>
      <RForm.Item
        name={['frontendChangeNameSetting', 'freeTimes']}
        label={t('pages_platform_general_freeEditName_time')}
        rules={[
          {
            required: isChangeNameFree === 0,
            message: t('pages_platform_input_error')
          }
        ]}
      >
        <RInputNumber className="!w-60" disabled={isChangeNameFree === 1} />
      </RForm.Item>
      <RForm.Item
        name={['frontendChangeNameSetting', 'fee']}
        label={t('pages_platform_general_freeEditName_cost')}
        rules={[
          {
            required: isChangeNameFree === 0,
            message: t('pages_platform_input_error')
          }
        ]}
      >
        <RInputNumber
          stringMode
          className="!w-60"
          min={1}
          disabled={isChangeNameFree === 1}
          step={0.01}
        />
      </RForm.Item>
    </ItemWrap>
  );
};

const BindPhoneSetting = ({ form }: { form: FormInstance<FormValue> }) => {
  const { t } = useTranslation();
  const isBindPhoneChangeable = RForm.useWatch(['frontendBindPhoneSetting', 'changeable'], form);

  return (
    <ItemWrap>
      <RForm.Item
        name={['frontendBindPhoneSetting', 'changeable']}
        label={t('pages_platform_general_freeEditPhone')}
        layout="horizontal"
        className="!mb-0"
        initialValue={isBindPhoneChangeable}
        rules={[
          {
            required: true
          }
        ]}
      >
        <RSwitch isBoolean={false}></RSwitch>
      </RForm.Item>
      <span className="mb-4 text-xs text-text-secondary">
        {t('pages_platform_general_freeEditPhone_description')}
      </span>
      <RForm.Item
        name={['frontendBindPhoneSetting', 'fee']}
        label={t('pages_platform_general_freeEditPhone_fee')}
        rules={[
          {
            required: isBindPhoneChangeable === 1,
            message: t('pages_platform_input_error')
          }
        ]}
      >
        <RInputNumber
          className="!w-60"
          disabled={isBindPhoneChangeable === 0}
          min={1}
          stringMode
          step={0.01}
        />
      </RForm.Item>
    </ItemWrap>
  );
};

interface GeneralSettingsProps {
  onSave: (data: GeneralSettingsData) => void;
  onChange: (data: GeneralSettingsData) => void; //非必要以後再來調整
  initialData?: GeneralSettingsData | null;
}

const GeneralSettings = ({ onSave, initialData, onChange }: GeneralSettingsProps) => {
  const { t } = useTranslation();
  const [form] = RForm.useForm();

  useEffect(() => {
    if (initialData) {
      form.setFieldsValue({ ...initialData });
    }
  }, [initialData, form]);

  const handleSave = () => {
    form.submit();
  };

  const handleSubmit = (values: FormValue) => {
    onChange(values);
    onSave(values);
  };

  return (
    <>
      <div className="flex items-end gap-3 px-6 mb-5 mt-9">
        <h2 className="m-0 text-sm font-bold">{t('pages_platform_general_title')}</h2>
        <p className="text-xs text-text-icon">{t('pages_platform_description')}</p>
      </div>
      <div className="mx-6 rounded">
        <RForm<FormValue>
          layout="vertical"
          className="space-y-2"
          form={form}
          onFinish={handleSubmit}
        >
          <ItemWrap>
            <RForm.Item
              label={t('pages_platform_general_description')}
              name="customerServiceLink"
              rules={[
                {
                  required: true,
                  message: t('pages_platform_input_error'),
                  whitespace: true
                }
              ]}
              className="!mb-0"
            >
              <RInput className="!w-60" placeholder={t('pages_platform_general_placeholder')} />
            </RForm.Item>
          </ItemWrap>
          <ChangeNameSetting form={form} />
          <BindPhoneSetting form={form} />
        </RForm>
      </div>
      <div className="m-6 mt-8">
        <RButton type="primary" className="!w-30" onClick={handleSave}>
          {t('common_save')}
        </RButton>
      </div>
    </>
  );
};

export default GeneralSettings;
