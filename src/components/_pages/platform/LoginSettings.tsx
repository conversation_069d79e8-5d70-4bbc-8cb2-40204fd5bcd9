import { CheckboxChangeEvent } from 'antd/es/checkbox';

import RButton from '@/components/RButton';
import RCheckbox from '@/components/RCheckbox';
import RForm from '@/components/RForm';
import { LoginSettingsData } from '@/types/platform';

interface LoginSettingsProps {
  onSave: (data: LoginSettingsData) => void;
  onChange: (data: LoginSettingsData) => void;
  initialData?: LoginSettingsData | null;
}

const LoginSettings = ({ onSave, onChange, initialData }: LoginSettingsProps) => {
  const { t } = useTranslation();
  const [settings, setSettings] = useState<LoginSettingsData>({
    line: initialData?.line || 0,
    phone: initialData?.phone || 0,
    account: initialData?.account || 0
  });

  useEffect(() => {
    if (initialData) {
      setSettings(initialData);
    }
  }, [initialData]);

  const handleCheckboxChange = (key: keyof LoginSettingsData, e: CheckboxChangeEvent) => {
    const newSettings = {
      ...settings,
      [key]: e.target.checked ? 1 : 0
    };
    setSettings(newSettings);
    onChange(newSettings);
  };

  return (
    <>
      <div className="flex items-end gap-3 px-6 mb-5 mt-9">
        <h2 className="m-0 text-sm font-bold">{t('pages_platform_login_title')}</h2>
        <p className="text-xs text-gray-500">{t('pages_platform_description')}</p>
      </div>
      <div className="mx-6">
        <RForm>
          <RForm.Item label={t('pages_platform_lineLogin')}>
            <RCheckbox
              checked={settings.line === 1}
              onChange={(e) => handleCheckboxChange('line', e)}
            />
          </RForm.Item>
          <RForm.Item label={t('pages_platform_phoneLogin')}>
            <RCheckbox
              checked={settings.phone === 1}
              onChange={(e) => handleCheckboxChange('phone', e)}
            />
          </RForm.Item>
          <RForm.Item label={t('pages_platform_accountLogin')}>
            <RCheckbox
              checked={settings.account === 1}
              onChange={(e) => handleCheckboxChange('account', e)}
            />
          </RForm.Item>
        </RForm>
      </div>
      <div className="m-6 mt-8">
        <RButton type="primary" className="!w-30" onClick={() => onSave(settings)}>
          {t('common_save')}
        </RButton>
      </div>
    </>
  );
};

export default LoginSettings;
