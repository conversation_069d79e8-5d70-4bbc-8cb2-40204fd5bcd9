import { RightOutlined } from '@ant-design/icons';
import { Menu } from 'antd';

import { MenuKey } from '@/pages/agent/platform/index';
interface MenuItem {
  key: string;
  label: React.ReactNode;
}

interface SideMenuProps {
  items: MenuItem[];
  selectedKey: string;
  onMenuClick: (key: <PERSON>u<PERSON><PERSON>) => void;
}

const SideMenu = ({ items, selectedKey, onMenuClick }: SideMenuProps) => {
  const menuItems = items.map((item) => ({
    key: item.key,
    label: (
      <div className="flex justify-between items-center w-full">
        <span className={`${selectedKey === item.key ? 'text-text' : 'text-text-icon'}`}>
          {item.label}
        </span>
        {selectedKey === item.key && <RightOutlined style={{ fontSize: '10px' }} />}
      </div>
    )
  }));

  return (
    <Menu
      mode="vertical"
      selectedKeys={[selectedKey]}
      onClick={({ key }) => onMenuClick(key as <PERSON><PERSON><PERSON><PERSON>)}
      items={menuItems}
      className="platform-side-menu"
    />
  );
};

export default SideMenu;
