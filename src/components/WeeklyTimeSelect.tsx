import { TimePicker } from 'antd';
import dayjs from 'dayjs';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import RForm from '@/components/RForm';
import RSelect from '@/components/RSelect';

const WEEK_OPTIONS = [
  { label: 'pages_activity_mondays', value: 1 },
  { label: 'pages_activity_tuesdays', value: 2 },
  { label: 'pages_activity_wednesdays', value: 3 },
  { label: 'pages_activity_thursdays', value: 4 },
  { label: 'pages_activity_fridays', value: 5 },
  { label: 'pages_activity_saturdays', value: 6 },
  { label: 'pages_activity_sundays', value: 0 }
];

interface WeeklyTimeSelectProps {
  weekStartField?: string;
  weekEndField?: string;
  timeStartField?: string;
  timeEndField?: string;
  form?: any; // 添加 form 參數
}

const WeeklyTimeSelect = ({
  weekStartField = 'weekStart',
  weekEndField = 'weekEnd',
  timeStartField = 'timeStart',
  timeEndField = 'timeEnd',
  form
}: WeeklyTimeSelectProps) => {
  const { t } = useTranslation();
  const requiredRule = {
    required: true,
    message: t('pages_platform_input_error')
  };

  // 監聽表單初始值，確保預設值能正確顯示
  useEffect(() => {
    if (form) {
      const currentValues = form.getFieldsValue();
      const updates: any = {};

      if (!currentValues[timeStartField]) {
        updates[timeStartField] = '00:00';
      }
      if (!currentValues[timeEndField]) {
        updates[timeEndField] = '23:59';
      }

      // 如果有需要更新的字段，則批量更新
      if (Object.keys(updates).length > 0) {
        form.setFieldsValue(updates);
      }
    }
  }, [form, weekStartField, weekEndField, timeStartField, timeEndField]);

  return (
    <div className="flex gap-4">
      <RForm.Item name={weekStartField} rules={[requiredRule]}>
        <RSelect
          options={WEEK_OPTIONS.map((option) => ({
            label: t(option.label),
            value: option.value
          }))}
        />
      </RForm.Item>
      <RForm.Item
        name={timeStartField}
        getValueProps={(value) => ({ value: value ? dayjs(`2000-01-01 ${value}`) : null })}
        normalize={(value) => (value ? dayjs(value).format('HH:mm') : undefined)}
        rules={[requiredRule]}
      >
        <TimePicker showNow={false} format="HH:mm" placeholder={t('placeholder_select')} />
      </RForm.Item>
      <div className="text-text pt-2">{t('common_until')}</div>
      <RForm.Item name={weekEndField} rules={[requiredRule]}>
        <RSelect
          options={WEEK_OPTIONS.map((option) => ({
            label: t(option.label),
            value: option.value
          }))}
        />
      </RForm.Item>
      <RForm.Item
        name={timeEndField}
        getValueProps={(value) => ({ value: value ? dayjs(`2000-01-01 ${value}`) : null })}
        normalize={(value) => (value ? dayjs(value).format('HH:mm') : undefined)}
        rules={[requiredRule]}
      >
        <TimePicker showNow={false} format="HH:mm" placeholder={t('placeholder_select')} />
      </RForm.Item>
    </div>
  );
};

export default WeeklyTimeSelect;
