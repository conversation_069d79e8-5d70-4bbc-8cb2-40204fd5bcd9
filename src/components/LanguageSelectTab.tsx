import clsx from 'clsx';

import ActionAddIcon from '@/assets/img/icon/action-add.svg?react';
import RDropdown from '@/components/RDropdown';
import useFrontendLanguage from '@/hooks/useFrontendLanguage';

const LanguageSelectTab = ({
  onChange,
  languageList,
  activeLanguage,
  setActiveLanguage,
  isReadOnly = false
}: {
  languageList: string[];
  onChange: (value: string[]) => void;
  activeLanguage: string;
  setActiveLanguage: (value: string) => void;
  isReadOnly?: boolean;
}) => {
  const { defaultFrontendLanguage, languageListOptions } = useFrontendLanguage();

  const showList = languageListOptions.filter((language) => languageList?.includes(language.value));

  const handleAddLanguage = useCallback(
    (language: string) => {
      onChange?.([...languageList, language]);
    },
    [onChange, languageList]
  );

  const items = languageListOptions
    .filter((language) => !languageList?.includes(language.value))
    .map((language) => ({
      key: language.value,
      label: language.label,
      onClick: () => handleAddLanguage(language.value)
    }));

  useEffect(() => {
    if (languageList?.length === 0 && defaultFrontendLanguage) {
      handleAddLanguage(defaultFrontendLanguage);
      setActiveLanguage(defaultFrontendLanguage);
    }
  }, [languageList, handleAddLanguage, defaultFrontendLanguage, setActiveLanguage]);

  return (
    <div className="flex items-center text-xs gap-x-1">
      {showList.map((language) => (
        <span
          className={clsx(
            'cursor-pointer w-fit border border-solid border-primary text-primary rounded-lg px-2.5 py-1',
            language.value === activeLanguage && 'bg-primary text-white'
          )}
          key={language.value}
          onClick={() => setActiveLanguage(language.value)}
        >
          {language.label}
        </span>
      ))}
      {items.length > 0 && !isReadOnly && (
        <RDropdown
          items={items}
          buttonProps={{ type: 'text' }}
          icon={
            <ActionAddIcon
              width={16}
              height={16}
              fill="currentColor"
              className="!text-text-secondary"
            />
          }
        />
      )}
    </div>
  );
};

export default LanguageSelectTab;
