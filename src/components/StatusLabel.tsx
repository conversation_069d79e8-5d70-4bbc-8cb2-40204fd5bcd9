import { useTranslation } from 'react-i18next';

import RTag from './RTag';
import type { TagColor } from './RTag/types';

// 預設的狀態配置
const defaultStatusMap = {
  1: {
    label: 'common_active',
    color: 'var(--tag-bg-enabled)' as TagColor,
    textColor: 'var(--color-success)' as TagColor
  },
  0: {
    label: 'common_inactive',
    color: 'var(--tag-bg-disabled)' as TagColor,
    textColor: 'var(--color-warning)' as TagColor
  }
};

interface StatusLabelProps {
  status: number;
  label?: string;
  color?: TagColor;
  textColor?: TagColor;
  customStatusMap?: Record<number, { label: string; color: TagColor; textColor?: TagColor }>;
}

const StatusLabel = ({ status, label, color, textColor, customStatusMap }: StatusLabelProps) => {
  const { t } = useTranslation();

  // 使用自定義狀態
  const statusConfig =
    customStatusMap?.[status] || defaultStatusMap[status as keyof typeof defaultStatusMap];

  const displayLabel = label || statusConfig?.label;
  const displayColor = color || statusConfig?.color;
  const displayTextColor = textColor || statusConfig?.textColor;

  return (
    <RTag color={displayColor} textColor={displayTextColor}>
      {t(displayLabel)}
    </RTag>
  );
};

export default StatusLabel;
