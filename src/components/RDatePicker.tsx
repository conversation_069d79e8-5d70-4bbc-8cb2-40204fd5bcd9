import { DatePicker, DatePickerProps, TimeRangePickerProps } from 'antd';

export interface RDatePickerProps extends DatePickerProps {
  showTime?: boolean;
}

export interface RDatePickerRangePickerProps extends TimeRangePickerProps {
  showTime?: boolean;
}

export const RDatePicker = ({ ...props }: RDatePickerProps) => {
  return <DatePicker {...props} style={{ width: '100%' }} />;
};

RDatePicker.RangePicker = DatePicker.RangePicker;

export default RDatePicker;
