import { useQuery } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';

import { getPlayerTagListOption } from '@/api/player';
import RForm from '@/components/RForm';
import RMultipleSelect from '@/components/RMultipleSelect';
import { PlayerTag } from '@/types/playerlist';

interface TagSelectProps {
  name?: string;
  label?: string;
  showLabel?: boolean;
  initialValue?: string[];
  onChange?: (values: (string | number)[] | undefined) => void;
  showNoneOption?: boolean;
  noneOptionLabel?: string;
}

const TagSelect = ({
  name = 'tags',
  label,
  showLabel = true,
  initialValue,
  onChange,
  showNoneOption = true,
  noneOptionLabel = 'pages_playerTag_Tag'
}: TagSelectProps) => {
  const { t } = useTranslation();
  const { data: tagList } = useQuery({
    queryKey: ['tagList'],
    queryFn: getPlayerTagListOption
  });
  const tagListOptions =
    tagList?.data?.map((tag: PlayerTag) => ({ label: tag.name, value: tag.id })) || [];

  return (
    <RForm.Item
      name={name}
      label={showLabel ? label || t('pages_player_playerTag') : undefined}
      initialValue={initialValue}
    >
      <RMultipleSelect
        options={tagListOptions}
        onChange={onChange}
        showNoneOption={showNoneOption}
        noneOptionLabel={noneOptionLabel}
      />
    </RForm.Item>
  );
};

export default TagSelect;
