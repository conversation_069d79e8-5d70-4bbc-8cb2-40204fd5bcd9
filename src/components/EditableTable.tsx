import { FormInstance } from 'antd';
import { Rule } from 'antd/es/form';
import { ReactNode } from 'react';
import { useTranslation } from 'react-i18next';

import { ActionButtons } from '@/components/ActionButtons';
import RButton from '@/components/RButton';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RInputNumber from '@/components/RInputNumber';
import RSelect from '@/components/RSelect';
import RTable, { RTableColumnsType } from '@/components/RTable';

// Column configuration interface
export interface EditableTableColumn<T> {
  key: keyof T;
  title: string;
  inputType?: 'text' | 'select' | 'number' | 'textarea';
  placeholder?: string;
  rules?: Rule[];
  options?: { label: string; value: string | number }[];
  render?: (value: unknown, record: T) => ReactNode;
  width?: string | number;
}

// Loading states interface
export interface EditableTableLoading {
  create?: boolean;
  update?: boolean;
  delete?: boolean;
}

// Main component props interface
export interface EditableTableProps<T extends Record<string, unknown>, F = Partial<T>> {
  data: T[];
  columns: EditableTableColumn<T>[];
  onSave: (values: Partial<F>, isEdit: boolean, editingId?: string | number) => void;
  onDelete: (record: T) => void;
  onAdd?: () => void;
  loading?: EditableTableLoading;
  rowKey: keyof T;
  addButtonText?: string;
  form: FormInstance<F>;
  editingId: string | number | null;
  isAdding: boolean;
  onEdit: (record: T) => void;
  onCancel: () => void;
  onStartAdd: () => void;
}

const EditableTable = <T extends Record<string, unknown>, F extends object = Partial<T>>({
  data,
  columns,
  onSave,
  onDelete,
  loading = {},
  rowKey,
  addButtonText,
  form,
  editingId,
  isAdding,
  onEdit,
  onCancel,
  onStartAdd
}: EditableTableProps<T, F>) => {
  const { t } = useTranslation();

  const handleSubmit = (values: F) => {
    const isEdit = editingId !== null;
    onSave(values, isEdit, editingId || undefined);
  };

  const renderInput = (column: EditableTableColumn<T>) => {
    const { inputType = 'text', placeholder, options } = column;

    switch (inputType) {
      case 'select':
        return <RSelect options={options} placeholder={placeholder} />;
      case 'number':
        return <RInputNumber placeholder={placeholder} />;
      case 'textarea':
        return <RInput.TextArea placeholder={placeholder} rows={2} />;
      default:
        return <RInput placeholder={placeholder} />;
    }
  };

  const renderCell = (column: EditableTableColumn<T>, record: T, isEditing: boolean) => {
    if (isEditing) {
      return (
        <RForm.Item name={column.key as string} rules={column.rules}>
          {renderInput(column)}
        </RForm.Item>
      );
    }

    const value = record[column.key];
    if (column.render) {
      return column.render(value, record);
    }
    return String(value ?? '');
  };

  // Convert EditableTable columns to RTable columns format
  const tableColumns: RTableColumnsType<T> = [
    ...columns.map((column) => ({
      title: column.title,
      dataIndex: column.key as string,
      key: column.key as string,
      width: column.width,
      render: (_: unknown, record: T) => {
        const recordKey = record[rowKey];
        const isEditing = editingId === recordKey || (isAdding && recordKey === '__adding__');
        return renderCell(column, record, isEditing);
      }
    })),
    {
      title: t('common_action'),
      key: 'action',
      render: (_: unknown, record: T) => {
        const recordKey = record[rowKey];
        const isEditing = editingId === recordKey || (isAdding && recordKey === '__adding__');

        if (isEditing) {
          return (
            <div className="flex gap-1 items-start">
              <RButton
                size="small"
                variant="outlined"
                color="green"
                loading={loading.update}
                onClick={() => form.submit()}
              >
                {t('common_save')}
              </RButton>
              <RButton size="small" variant="outlined" color="red" onClick={onCancel}>
                {t('common_cancel')}
              </RButton>
            </div>
          );
        }

        return (
          <ActionButtons
            data={{ ...record, status: undefined }}
            buttons={['edit', 'delete']}
            onEdit={() => onEdit(record)}
            onDelete={() => onDelete(record)}
          />
        );
      }
    }
  ];

  // Prepare data source including the adding row if needed
  const tableDataSource = [...data];
  if (isAdding) {
    // Create a temporary record for the adding row
    const addingRecord = {} as T;
    // Set the rowKey to a temporary value to distinguish it
    (addingRecord as Record<string, unknown>)[rowKey as string] = '__adding__';
    tableDataSource.push(addingRecord);
  }

  return (
    <div>
      <div className="mb-4">
        <RButton onClick={onStartAdd}>{addButtonText || t('common_add')}</RButton>
      </div>
      <RForm<F> form={form} onFinish={handleSubmit} layout="vertical">
        <RTable<T>
          rowKey={rowKey as string}
          dataSource={tableDataSource}
          columns={tableColumns}
          pagination={false}
          loading={loading.create || loading.update || loading.delete}
        />
      </RForm>
    </div>
  );
};

export default EditableTable;
