import { FormInstance } from 'antd/es/form';
import dayjs from 'dayjs';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import RDatePicker from '@/components/RDatePicker';
import RForm from '@/components/RForm';
import RSelect from '@/components/RSelect';
import { PublishType, PublishTypeEnum } from '@/types/common';
const { RangePicker } = RDatePicker;

type FormTimePeriodSelectProps = {
  form: FormInstance;
  value?: PublishType;
  onChange?: (value: PublishType) => void;
  // 新增配置選項
  fieldPrefix?: string; // 字段前綴，如 'shelf' 或 'conduct'
  label?: string; // 自定義標籤
  showPermanent?: boolean; // 是否顯示永久選項
  showWeekly?: boolean; // 是否顯示每週重複選項
  includeTimeRange?: boolean; // 是否包含時間範圍選項
  customOptions?: { label: string; value: number }[]; // 自定義選項
  classNames?: string;
};

const FormTimePeriodSelect = ({
  form,
  fieldPrefix = '',
  showPermanent = true,
  showWeekly = false,
  includeTimeRange = true,
  customOptions,
  classNames
}: FormTimePeriodSelectProps) => {
  const { t } = useTranslation();

  const getFieldName = (field: string) => {
    if (fieldPrefix) {
      return `${fieldPrefix}${field.charAt(0).toUpperCase() + field.slice(1)}`;
    }
    if (field === 'type') {
      return 'publishType';
    }
    return field;
  };

  const publishTypeField = getFieldName('type');
  const startTimeField = getFieldName('startTime');
  const endTimeField = getFieldName('endTime');
  const timeRangeField = getFieldName('timeRange');

  // 構建選項
  const timeTypeOptions = customOptions || [
    ...(showPermanent
      ? [
          {
            label: t('components_formTimePeriodSelect_publishType_permanent'),
            value: PublishTypeEnum.PERMANENT
          }
        ]
      : []),
    {
      label: t('components_formTimePeriodSelect_publishType_fixedTime'),
      value: PublishTypeEnum.FIXED_TIME
    },
    ...(includeTimeRange
      ? [
          {
            label: t('components_formTimePeriodSelect_publishType_timeRange'),
            value: PublishTypeEnum.TIME_RANGE
          }
        ]
      : []),
    ...(showWeekly
      ? [
          {
            label: t('components_formTimePeriodSelect_publishType_weekly'),
            value: PublishTypeEnum.WEEKLY
          }
        ]
      : [])
  ];

  const publishTypeValue = RForm.useWatch(publishTypeField, form);
  const timeRangeValue = RForm.useWatch(timeRangeField, form);

  // 當 publishType 改變時，重置相關字段
  useEffect(() => {
    if (publishTypeValue === PublishTypeEnum.PERMANENT) {
      form.setFieldsValue({
        [startTimeField]: null,
        [endTimeField]: null,
        [timeRangeField]: undefined
      });
    } else if (publishTypeValue === PublishTypeEnum.FIXED_TIME) {
      form.setFieldsValue({
        [endTimeField]: null,
        [timeRangeField]: undefined
      });
    } else if (publishTypeValue === PublishTypeEnum.TIME_RANGE) {
      form.setFieldsValue({
        [startTimeField]: null,
        [endTimeField]: null
      });
    }
  }, [publishTypeValue, form, startTimeField, endTimeField, timeRangeField]);

  // 當 timeRange 改變時，同步到 startTime 和 endTime
  useEffect(() => {
    if (publishTypeValue === PublishTypeEnum.TIME_RANGE && timeRangeValue) {
      form.setFieldsValue({
        [startTimeField]: timeRangeValue[0],
        [endTimeField]: timeRangeValue[1]
      });
    }
  }, [publishTypeValue, form, timeRangeValue, startTimeField, endTimeField]);

  return (
    <div className={classNames}>
      <div className="grid items-end grid-cols-2 gap-x-4">
        <RForm.Item name={publishTypeField} initialValue={PublishTypeEnum.PERMANENT}>
          <RSelect options={timeTypeOptions} />
        </RForm.Item>
        {publishTypeValue !== PublishTypeEnum.TIME_RANGE &&
          publishTypeValue !== PublishTypeEnum.WEEKLY && (
            <RForm.Item
              name={startTimeField}
              label={null}
              rules={[
                {
                  required: publishTypeValue === PublishTypeEnum.FIXED_TIME,
                  message: `${t('placeholder_input')}${t('common_startTime')}`
                }
              ]}
              getValueProps={(value) => ({ value: value && dayjs(Number(value)) })}
              normalize={(value) => value && `${dayjs(value).valueOf()}`}
            >
              <RDatePicker
                showTime
                disabled={publishTypeValue === PublishTypeEnum.PERMANENT}
                format="YYYY-MM-DD HH:mm"
              />
            </RForm.Item>
          )}
      </div>
      {publishTypeValue === PublishTypeEnum.TIME_RANGE && (
        <RForm.Item
          name={timeRangeField}
          rules={[
            {
              required: publishTypeValue === PublishTypeEnum.TIME_RANGE,
              message: t('pages_platform_input_error')
            }
          ]}
          getValueProps={(value) => ({
            value: value && [dayjs(Number(value[0])), dayjs(Number(value[1]))]
          })}
          normalize={(value) => value && [value[0].valueOf(), value[1].valueOf()]}
        >
          <RangePicker showTime />
        </RForm.Item>
      )}
      {/* 隱藏的字段，用於存儲最終的 startTime 和 endTime */}
      <RForm.Item name={startTimeField} noStyle></RForm.Item>
      <RForm.Item name={endTimeField} noStyle></RForm.Item>
    </div>
  );
};

export default FormTimePeriodSelect;
