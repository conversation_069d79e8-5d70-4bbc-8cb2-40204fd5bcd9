import { Switch, SwitchProps } from 'antd';

interface antdSwitchProps extends SwitchProps {
  defaultChecked?: boolean;
}

interface RSwitchProps extends antdSwitchProps {
  defaultCheckValue?: boolean | 0 | 1;
  isBoolean?: boolean;
  onChange?: (checked: boolean | 0 | 1) => void;
}

export const RSwitch = ({
  defaultCheckValue = true,
  isBoolean = true,
  checked,
  onChange,
  ...props
}: RSwitchProps) => {
  const defaultCheckedValue: boolean | undefined = Boolean(defaultCheckValue);

  const onSwitchChange = (checked: boolean) => {
    if (isBoolean) {
      onChange?.(checked);
    } else {
      onChange?.(checked ? 1 : 0);
    }
  };

  return (
    <Switch
      checked={checked}
      defaultChecked={defaultCheckedValue}
      onChange={onSwitchChange}
      {...props}
    />
  );
};

export default RSwitch;
