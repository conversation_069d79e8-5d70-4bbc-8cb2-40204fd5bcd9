import QRCode from 'qrcode';
import { Fragment, useEffect, useRef, useState } from 'react';

import { RButton } from '@/components/RButton';

interface QRCodeComponentProps {
  url: string;
}

const QRCodeComponent: React.FC<QRCodeComponentProps> = ({ url }) => {
  const [qrCodeUrl, setQrCodeUrl] = useState('');
  const generateQRCode = async (url: string) => {
    try {
      if (!url) return '';

      const options = {
        margin: 1,
        errorCorrectionLevel: 'L' as const
      };

      // 生成QR碼的data URL
      const dataUrl = await QRCode.toDataURL(url, options);
      return dataUrl;
    } catch (error) {
      console.error('生成QR碼時出錯:', error);
      return '';
    }
  };

  useEffect(() => {
    const updateQRCode = async () => {
      const dataUrl = await generateQRCode(url);
      setQrCodeUrl(dataUrl);
    };
    updateQRCode();
  }, [url]);

  return (
    <div>
      <img className="shadow-lg bg-white" src={qrCodeUrl} alt="QR Code" width="96" height="96" />
    </div>
  );
};

interface OTPVerificationComponentProps {
  qrcodeUrl?: string;
  setOtpVerification: (value: boolean) => void;
  setOtp?: (value: string) => void;
}

// 主OTP驗證組件
const OTPVerificationComponent: React.FC<OTPVerificationComponentProps> = ({
  qrcodeUrl,
  setOtpVerification,
  setOtp
}) => {
  const [values, setValues] = useState(['', '', '', '', '', '']);
  const inputRefs = useRef<(HTMLInputElement | null)[]>(Array(6).fill(null));
  const { t } = useTranslation();

  useEffect(() => {
    // 自動聚焦於第一個輸入框
    const firstInput = inputRefs.current[0];
    if (firstInput) {
      firstInput.focus();
    }
  }, []);

  // 數字分配邏輯
  const fillValues = (startIndex: number, digits: string) => {
    const newValues = [...values];
    for (let i = 0; i < digits.length; i++) {
      const targetIndex = startIndex + i;
      if (targetIndex < values.length) {
        newValues[targetIndex] = digits[i];
      }
    }
    setValues(newValues);

    // 自動聚焦到下一個
    const nextIndex = Math.min(startIndex + digits.length, values.length - 1);
    const nextInput = inputRefs.current[nextIndex];
    if (nextInput) nextInput.focus();
  };

  const handleChange = (index: number, e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/[^\d]/g, ''); // 移除非數字

    if (value === '') {
      // 處理刪除
      const newValues = [...values];
      newValues[index] = '';
      setValues(newValues);
      return;
    }

    if (value.length > 1) {
      // 多位數（可能來自自動填入、滑鼠右鍵貼上）
      fillValues(index, value);
    } else {
      // 單一數字
      const newValues = [...values];
      newValues[index] = value;
      setValues(newValues);

      // 聚焦到下一格
      if (index < values.length - 1) {
        const nextInput = inputRefs.current[index + 1];
        if (nextInput) nextInput.focus();
      }
    }
  };
  // clipboard 事件 複製貼上
  const handlePaste = (index: number, e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pasteData = e.clipboardData.getData('Text').replace(/[^\d]/g, '');
    if (pasteData) {
      fillValues(index, pasteData);
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    // 處理退格鍵
    if (e.key === 'Backspace') {
      if (values[index] === '' && index > 0) {
        // 如果當前框為空且不是第一個，聚焦到前一個輸入框
        const prevInput = inputRefs.current[index - 1];
        if (prevInput) {
          prevInput.focus();
        }
      }
    }

    // 處理左右箭頭
    if (e.key === 'ArrowLeft' && index > 0) {
      const prevInput = inputRefs.current[index - 1];
      if (prevInput) {
        prevInput.focus();
      }
    }

    if (e.key === 'ArrowRight' && index < values.length - 1) {
      const nextInput = inputRefs.current[index + 1];
      if (nextInput) {
        nextInput.focus();
      }
    }
  };

  const handleLoginWithOtp = () => {
    const otpValue = values.join('');
    setOtpVerification(true);
    if (setOtp) {
      setOtp(otpValue);
    }
  };

  const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    // 聚焦時選中內容
    e.target.select();
  };

  return (
    <div className="fixed inset-0 bg-[#00000080] z-50">
      <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[480px] bg-white rounded-sm py-7 px-5 z-50 shadow-md">
        <div className="flex flex-col items-center">
          <div className="flex justify-between w-full">
            <span className="text-sm font-medium text-gray-700">{t('login_otp_title')}</span>
            <span className="text-gray-500">Step 2/2</span>
          </div>

          <div className="mt-10 mb-8 flex justify-center">
            <QRCodeComponent url={qrcodeUrl || ''} />
          </div>

          <div className="flex gap-2">
            {values.map((value, index) => (
              <Fragment key={index}>
                <input
                  ref={(el) => (inputRefs.current[index] = el)}
                  className="w-8 h-12 text-center border border-gray-300 rounded-md text-lg font-bold focus:border-primary focus:ring-0.5 focus:ring-primary outline-none"
                  type="text"
                  maxLength={1}
                  value={value}
                  onChange={(e) => handleChange(index, e)}
                  onKeyDown={(e) => handleKeyDown(index, e)}
                  onFocus={handleFocus}
                  onPaste={(e) => handlePaste(index, e)}
                />
                {index === 2 && <span className="flex items-center text-gray-400 mx-1">-</span>}
              </Fragment>
            ))}
          </div>

          <div className="text-text-placeholder my-4 space-y-1 w-full ml-40">
            <p>{`1. ${t('login_otp_step1')}`}</p>
            <p>{`2. ${t('login_otp_step2')}`}</p>
            <p>{`3. ${t('login_otp_step3')}`}</p>
          </div>

          <RButton
            className="!mt-20 !w-1/2 text-white py-2 px-4 rounded-md transition-colors"
            onClick={handleLoginWithOtp}
          >
            {t('common_submit')}
          </RButton>
        </div>
      </div>
    </div>
  );
};

export default OTPVerificationComponent;
