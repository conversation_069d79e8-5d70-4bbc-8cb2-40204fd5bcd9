import clsx from 'clsx';
import { useTranslation } from 'react-i18next';

// import TimeZoneCell from './TimeZoneCell';
import CloseCircleIcon from '@/assets/img/icon/close-circle.svg?react';
import { defaultRoutes } from '@/router/defaultRoutes';
import { useBookmarkStore } from '@/store/bookmark';
import { usePermissionStore } from '@/store/permissionStore';
import { getRouteName } from '@/utils/routeUtils';

interface RouteItem {
  url: string;
  name: string;
  children?: RouteItem[];
}

const staticRoutes: RouteItem[] = [];
Object.entries(defaultRoutes).forEach(([key, value]) => {
  value.routes.forEach((route) => {
    staticRoutes.push({
      url: `/${key.toLowerCase()}/${route.pageName}`,
      name: `${key}_${route.pageName}`
    });
  });
});

const bookmarkItemClasses = {
  base: 'flex items-center justify-between rounded-t-lg border-1 border-b-0 border-solid border-text-placeholder py-2.5 pl-4 pr-1.5 flex-[0_0_120px] max-w-[120px] text-xs  cursor-pointer',
  default: 'bg-bg-secondary text-text',
  active: 'bg-primary text-white'
};

const bookmarkTextClasses = {
  base: 'flex-[1_0_0] text-ellipsis overflow-hidden whitespace-nowrap text-[13px]'
};

const Bookmark: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const pathname = location.pathname;
  const bookmarkStore = useBookmarkStore();
  const permissionStore = usePermissionStore();
  const { t } = useTranslation();

  const bookmarkList = useMemo(() => {
    return (bookmarkStore.bookmarks || []).filter((url) => permissionStore.hasRoutePermission(url));
  }, [bookmarkStore.bookmarks, permissionStore]);

  const activePath = (url: string): boolean => {
    return pathname === url;
  };

  const redirect = (item: RouteItem) => {
    navigate(item.url);
    if (!bookmarkList.includes(item.url)) {
      bookmarkStore.setBookmarks([item.url, ...bookmarkList]);
    }
  };

  const removeBookmark = (item: RouteItem) => {
    bookmarkStore.removeBookmark(item.url);
  };

  useEffect(() => {
    const firstRoute = staticRoutes.find(
      (e: RouteItem) =>
        e.url === pathname ||
        (e.children?.length && e.children.find((el: RouteItem) => el.url === pathname))
    );

    let urlToAdd = '';

    if (firstRoute && firstRoute?.children?.length) {
      const findChildRoute = firstRoute.children.find((e: RouteItem) => e.url === pathname);
      urlToAdd = findChildRoute?.url || '';
    } else if (firstRoute) {
      urlToAdd = firstRoute.url;
    }

    if (
      urlToAdd &&
      !bookmarkList.includes(urlToAdd) &&
      permissionStore.hasRoutePermission(urlToAdd)
    ) {
      bookmarkStore.setBookmarks([urlToAdd, ...bookmarkList]);
    }
  }, [pathname, bookmarkList, bookmarkStore, permissionStore]);

  return (
    <>
      <div className=" p-[20px_16px_0]">
        <div className="flex justify-between">
          <div className="flex items-end">
            <div className="flex">
              {bookmarkList
                .filter((_, index) => index >= bookmarkList.length - 10)
                .map((url: string) => {
                  const item = { url, name: getRouteName(url, t) };
                  return (
                    <div
                      key={url}
                      className={clsx(
                        bookmarkItemClasses.base,
                        activePath(url) ? bookmarkItemClasses.active : bookmarkItemClasses.default
                      )}
                      onClick={() => redirect(item)}
                    >
                      <div
                        className={clsx(bookmarkTextClasses.base, activePath(url) && 'text-white')}
                      >
                        {getRouteName(url, t)}
                      </div>
                      {!activePath(url) && (
                        <CloseCircleIcon
                          className="w-4 h-4 ml-1 cursor-pointer fill-text-secondary"
                          onClick={(e: React.MouseEvent) => {
                            e.stopPropagation();
                            removeBookmark(item);
                          }}
                        />
                      )}
                    </div>
                  );
                })}
            </div>
          </div>
          {/* <TimeZoneCell /> */}
        </div>
      </div>
    </>
  );
};

export default Bookmark;
