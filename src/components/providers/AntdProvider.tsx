import 'dayjs/locale/zh-tw';

import { App, ConfigProvider } from 'antd';
import zhTW from 'antd/locale/zh_TW';
import dayjs from 'dayjs';
import updateLocale from 'dayjs/plugin/updateLocale';

dayjs.extend(updateLocale);
dayjs.updateLocale('zh-tw', {
  weekStart: 1
});

export const AntdProvider = ({ children }: { children: React.ReactNode }) => {
  return (
    <ConfigProvider
      theme={{
        cssVar: {
          key: 'app-theme'
        },
        hashed: false
      }}
      locale={zhTW}
    >
      <App>{children}</App>
    </ConfigProvider>
  );
};

export default AntdProvider;
