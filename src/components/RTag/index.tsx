import './rtag.scss';

import type { TagProps } from 'antd';
import { Tag } from 'antd';
import clsx from 'clsx';

import type { TagColor } from './types';

const colorMap: Record<TagColor, string> = {
  red: 'rtag-red',
  gray: 'rtag-gray',
  orange: 'rtag-orange',
  yellow: 'rtag-yellow'
};

interface RTagProps extends TagProps {
  children?: React.ReactNode;
  color?: TagColor;
  bordered?: boolean;
  textColor?: string;
  className?: string;
}

const RTag = ({
  children,
  color = 'default',
  bordered = false,
  textColor,
  className,
  ...props
}: RTagProps) => {
  return (
    <Tag
      color={color}
      bordered={bordered}
      style={textColor ? { color: textColor } : undefined}
      className={clsx(colorMap[color], className)}
      {...props}
    >
      {children}
    </Tag>
  );
};

export default RTag;
