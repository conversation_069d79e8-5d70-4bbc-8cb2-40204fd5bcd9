import { useTranslation } from 'react-i18next';

import RButton from '@/components/RButton';

import StatusButton from './StatusButton';

interface ActionButtonsProps<T> {
  buttons?: ('edit' | 'delete' | 'status' | 'add' | 'cancel' | 'read')[];
  data: T;
  onEdit?: (data: T) => void;
  onEditStatus?: (data: T) => void;
  isEditStatusPending?: boolean;
  onDelete?: (data: T) => void;
  onAdd?: (data: T) => void;
  onCancel?: () => void;
  onRead?: (data: T) => void;
}

const EditButton = ({ onEdit }: { onEdit: () => void }) => {
  const { t } = useTranslation();
  return (
    <RButton size="small" variant="outlined" color="primary" type="link" onClick={onEdit}>
      {t('common_edit')}
    </RButton>
  );
};

const ReadButton = ({ onRead }: { onRead: () => void }) => {
  const { t } = useTranslation();
  return (
    <RButton size="small" variant="outlined" color="green" type="link" onClick={onRead}>
      {t('common_read')}
    </RButton>
  );
};

const DeleteButton = ({ onDelete }: { onDelete: () => void }) => {
  const { t } = useTranslation();
  return (
    <RButton size="small" variant="outlined" color="red" type="link" onClick={onDelete}>
      {t('common_delete')}
    </RButton>
  );
};

const AddButton = ({ onAdd }: { onAdd: () => void }) => {
  const { t } = useTranslation();
  return (
    <RButton size="small" variant="outlined" color="green" type="link" onClick={onAdd}>
      {t('common_save')}
    </RButton>
  );
};

const CancelButton = ({ onCancel }: { onCancel: () => void }) => {
  const { t } = useTranslation();
  return (
    <RButton size="small" variant="outlined" color="red" type="link" onClick={onCancel}>
      {t('common_cancel')}
    </RButton>
  );
};

export const ActionButtons = <T extends { status?: number }>({
  buttons = ['edit', 'delete', 'status', 'add', 'cancel', 'read'],
  data,
  onEdit,
  onEditStatus,
  isEditStatusPending,
  onDelete,
  onAdd,
  onCancel,
  onRead
}: ActionButtonsProps<T>) => {
  const renderButton = (button: 'edit' | 'delete' | 'status' | 'add' | 'cancel' | 'read') => {
    switch (button) {
      case 'edit':
        return <EditButton key={button} onEdit={() => onEdit?.(data)} />;
      case 'delete':
        return <DeleteButton key={button} onDelete={() => onDelete?.(data)} />;
      case 'add':
        return <AddButton key={button} onAdd={() => onAdd?.(data)} />;
      case 'cancel':
        return <CancelButton key={button} onCancel={() => onCancel?.()} />;
      case 'read':
        return <ReadButton key={button} onRead={() => onRead?.(data)} />;
      case 'status':
        if (data.status === undefined) return null;
        return (
          <StatusButton
            key={button}
            status={data.status}
            isEditStatusPending={isEditStatusPending ?? false}
            onEditStatus={() => onEditStatus?.(data)}
          />
        );
    }
  };

  return <div className="flex gap-1">{buttons.map(renderButton)}</div>;
};
