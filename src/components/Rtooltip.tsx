import { Tooltip } from 'antd';
import { forwardRef } from 'react';

interface RTooltipProps {
  title: string | React.ReactNode;
  children: React.ReactNode;
  trigger?: 'hover' | 'click' | 'focus';
  placement?:
    | 'top'
    | 'left'
    | 'right'
    | 'bottom'
    | 'topLeft'
    | 'topRight'
    | 'bottomLeft'
    | 'bottomRight';
  color?: string;
  arrow?: boolean;
}

export const RTooltip = forwardRef<HTMLDivElement, RTooltipProps>(
  (
    {
      title,
      children,
      placement = 'bottomLeft',
      trigger = 'hover',
      color = 'default',
      arrow = false
    },
    ref
  ) => {
    // 如果顏色是 gray，使用實際的顏色值
    const tooltipColor = color === 'gray' ? '#444444' : color;

    return (
      <Tooltip
        title={title}
        placement={placement}
        trigger={trigger}
        color={tooltipColor}
        arrow={arrow}
      >
        <div ref={ref}>{children}</div>
      </Tooltip>
    );
  }
);

RTooltip.displayName = 'RTooltip';

export default RTooltip;
