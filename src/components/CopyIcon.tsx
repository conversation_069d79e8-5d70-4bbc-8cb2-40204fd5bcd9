import SvgIcon from '@/assets/img/icon/copy.svg?react';
import { useCopy } from '@/hooks/useCopy';

type CopyIconProps = {
  text: string;
  width?: number;
  height?: number;
  className?: string;
};

const CopyIcon = ({ text, width = 20, height = 20, className = '' }: CopyIconProps) => {
  const copy = useCopy(text);
  return (
    <SvgIcon
      onClick={copy}
      className={`cursor-pointer fill-text-secondary ${className}`}
      width={width}
      height={height}
    />
  );
};

export default CopyIcon;
