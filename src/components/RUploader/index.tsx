// src/components/AudioUploader.tsx
import { message, Upload } from 'antd';
import type { RcFile, UploadProps } from 'antd/es/upload/interface';
import type { UploadRequestOption } from 'rc-upload/lib/interface';

import UploadIcon from '@/assets/img/icon/upload.svg?react';

interface RUploaderProps {
  accept?: string;
  beforeUpload?: (file: RcFile) => void;
  onChange?: (file: File) => void;
  value?: File;
  note?: React.ReactNode;
  width?: number | string;
  showPlaceholder?: boolean;
}

const RUploader = ({
  accept,
  beforeUpload,
  onChange,
  value,
  note,
  width,
  showPlaceholder = true,
  ...props
}: RUploaderProps) => {
  const { t } = useTranslation();

  const handleChange = (file: File) => {
    onChange?.(file);
  };

  const uploadProps: UploadProps = {
    name: 'file',
    accept,
    multiple: false,
    showUploadList: false,
    beforeUpload: beforeUpload,
    onChange: (info) => {
      if (info.file.status === 'error') {
        message.error(`${info.file.name} 上傳失敗`);
        return;
      }
    },
    customRequest: (options: UploadRequestOption) => {
      const { file, onSuccess, onError } = options;

      const handleUpload = async () => {
        try {
          if (file instanceof File) {
            const url = URL.createObjectURL(file);
            onSuccess?.({ url });
            handleChange?.(file);
          } else {
            throw new Error('Invalid file type');
          }
        } catch (err) {
          onError?.(err instanceof Error ? err : new Error('Upload failed'));
        }
      };

      handleUpload();
    },
    ...props
  };

  return (
    <Upload {...uploadProps}>
      <div
        className="flex gap-4 items-center text-xs transition-colors"
        style={width ? { width } : undefined}
      >
        <div className="flex items-center gap-1 px-2 py-0.5 bg-bg-secondary border border-gray-200 rounded-sm cursor-pointer text-text hover:text-primary">
          <UploadIcon className="w-6 h-6" />
          <span className="whitespace-nowrap">{t('common_upload')}</span>
        </div>
        {value ? (
          <div className="flex gap-4 items-center">
            <span className="block overflow-hidden truncate whitespace-nowrap text-text max-w-60">
              {value.name}
            </span>
          </div>
        ) : (
          showPlaceholder && (
            <div className="py-2 pl-2 w-72 rounded-sm border-2 border-dotted text-text-secondary min-w-45 border-component-border">
              {t('components_audioUploader_upload')}
            </div>
          )
        )}
      </div>
      <span className="mt-1 text-xs text-warning">{note}</span>
    </Upload>
  );
};

export default RUploader;
