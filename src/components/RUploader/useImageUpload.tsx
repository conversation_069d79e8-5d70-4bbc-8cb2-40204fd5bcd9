import { Upload } from 'antd';
import type { RcFile } from 'antd/es/upload/interface';

import useAntdApp from '@/hooks/useAntdApp';

type UseImageUploadProps = {
  fileType?: string;
  maxSize?: number;
};

const useImageUpload = ({ fileType = 'jpg,jpeg,png,webp', maxSize = 1 }: UseImageUploadProps) => {
  const { t } = useTranslation();
  const { message } = useAntdApp();

  const accept = `image/${fileType.split(',').join(',image/')}`;

  const checkIsImage = (file: RcFile) => {
    return file.type.startsWith('image/');
  };

  const checkImageFileType = (file: RcFile) => {
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    if (!checkIsImage(file) || !fileType.split(',').includes(fileExtension || '')) {
      message.error(t('components_imageUploader_description', { fileType }));
      return Upload.LIST_IGNORE;
    }
  };

  const checkImageFileSize = (file: RcFile) => {
    if (file.size > maxSize * 1024 * 1024) {
      message.error(t('components_imageUploader_limit', { size: maxSize }));
      return Upload.LIST_IGNORE;
    }
  };

  const beforeUpload = (file: RcFile) => {
    const typeCheck = checkImageFileType(file);
    const sizeCheck = checkImageFileSize(file);
    if (typeCheck === Upload.LIST_IGNORE || sizeCheck === Upload.LIST_IGNORE) {
      return Upload.LIST_IGNORE;
    }
    return true;
  };

  return { accept, checkIsImage, beforeUpload };
};

export default useImageUpload;
