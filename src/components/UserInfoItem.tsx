import { useMutation } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';

import { logout } from '@/api/auth';
import useClickOutside from '@/hooks/useClickOutside';
import { useUserStore } from '@/store/userStore';

const UserInfoItem: React.FC = () => {
  const { t } = useTranslation();
  const [isHovered, setIsHovered] = useState(false);
  const [isClicked, setIsClicked] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { userInfo, clearUser } = useUserStore();
  const navigate = useNavigate();
  const logoutMutation = useMutation({
    mutationFn: logout,
    onSuccess: () => {
      clearUser();
      navigate('/');
    }
  });

  useClickOutside({
    ref: dropdownRef,
    excludeSelector: '.header-dropdown-wrapper',
    onClickOutside: () => {
      setShowDropdown(false);
      setIsClicked(false);
    },
    enabled: showDropdown
  });

  return (
    <div className="header-dropdown-wrapper">
      <div
        className={`px-2 py-1 rounded-full bg-white text-right text-xs transition-all ease-in-out origin-right duration-500 cursor-pointer ${isHovered ? 'max-w-[300px]' : 'max-w-[30px]'}`}
        onMouseOver={() => {
          setIsHovered(true);
        }}
        onMouseLeave={() => {
          setIsHovered(false);
        }}
        onClick={() => {
          setIsClicked(!isClicked);
          setShowDropdown(!showDropdown);
        }}
      >
        <div className="overflow-hidden whitespace-nowrap">{userInfo?.account}</div>
      </div>

      {showDropdown && (
        <div
          ref={dropdownRef}
          className="header-dropdown dropdown text-xs overflow-hidden text-text"
        >
          <div className="py-[10px] text-center font-bold  hover:bg-gray-100   cursor-pointer">
            {t('components_userInfoItem_changePassword')}
          </div>
          <div
            className="py-[10px] text-center font-bold hover:bg-gray-100  cursor-pointer border-t border-gray-200"
            onClick={() => logoutMutation.mutate()}
          >
            {t('components_userInfoItem_logout')}
          </div>
          <div className="px-[15px] py-[10px] flex flex-col items-center text-text-secondary font-semibold cursor-default border-t border-gray-200">
            <div className="mb-1">{t('components_userInfoItem_lastLogin')}</div>
            <div>60.250.77.235</div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserInfoItem;
