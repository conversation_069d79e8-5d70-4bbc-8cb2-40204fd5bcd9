import { useTranslation } from 'react-i18next';

import RSelect, { RSelectProps } from '@/components/RSelect';
const options = [
  { key: 'all', value: 'all' },
  { key: 'active', value: 1 },
  { key: 'inactive', value: 0 }
];

export const StatusSelect = ({ value, onChange }: RSelectProps) => {
  const { t } = useTranslation();

  const formatValue = value === undefined ? 'all' : value;

  const handleChange = (value: string | number | null | undefined) => {
    if (value === 'all') {
      onChange?.(undefined);
    } else {
      onChange?.(value);
    }
  };

  return (
    <RSelect
      value={formatValue}
      options={options.map((option) => ({
        label: t(`common_${option.key}`),
        value: option.value
      }))}
      onChange={handleChange}
    />
  );
};

export default StatusSelect;
