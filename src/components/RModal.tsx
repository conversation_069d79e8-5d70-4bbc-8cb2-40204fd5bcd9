import { Modal, ModalProps, Spin } from 'antd';

import RButton from '@/components/RButton';

type ButtonProps = {
  disabled?: boolean;
  show?: boolean;
  text?: string;
};

interface antdModalProps extends ModalProps {
  title?: React.ReactNode;
  open?: boolean;
  onOk?: () => void;
  onCancel?: () => void;
  okButtonProps?: ButtonProps;
  cancelButtonProps?: ButtonProps;
}

export interface RModalProps extends antdModalProps {
  children?: React.ReactNode;
}

const ModalFooter = ({
  onOk,
  onCancel,
  loading,
  okButtonProps = {
    show: true
  },
  cancelButtonProps = {
    show: true
  }
}: {
  onOk?: () => void;
  onCancel?: () => void;
  okButtonProps?: ButtonProps;
  cancelButtonProps?: ButtonProps;
  loading?: boolean;
}) => {
  const { t } = useTranslation();

  const handleOk = useCallback(() => {
    onOk?.();
  }, [onOk]);

  const handleCancel = useCallback(() => {
    onCancel?.();
  }, [onCancel]);

  return (
    <div className="flex justify-center gap-2">
      {okButtonProps?.show && (
        <RButton
          type="primary"
          onClick={handleOk}
          className="!px-12"
          disabled={okButtonProps?.disabled ?? false}
          loading={loading}
        >
          {okButtonProps?.text ?? t('common_submit')}
        </RButton>
      )}
      {cancelButtonProps?.show && (
        <RButton type="default" onClick={handleCancel} className="!px-12" loading={loading}>
          {cancelButtonProps.text ?? t('common_cancel')}
        </RButton>
      )}
    </div>
  );
};

export const RModal = ({ title, loading = false, ...props }: RModalProps) => {
  return (
    <Modal
      title={title}
      footer={
        <ModalFooter
          onOk={props.onOk}
          onCancel={props.onCancel}
          okButtonProps={props.okButtonProps}
          cancelButtonProps={props.cancelButtonProps}
          loading={loading}
        />
      }
      {...props}
    >
      <Spin spinning={loading}>{props.children}</Spin>
    </Modal>
  );
};

RModal.useModal = Modal.useModal;
RModal.confirm = Modal.confirm;

export default RModal;
