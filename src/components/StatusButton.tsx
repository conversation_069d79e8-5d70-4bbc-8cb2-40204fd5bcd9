import RButton from './RButton';

const StatusButton = ({
  status,
  isEditStatusPending,
  onEditStatus
}: {
  status: number;
  isEditStatusPending: boolean;
  onEditStatus: () => void;
}) => {
  const { t } = useTranslation();
  return (
    <RButton
      size="small"
      variant="outlined"
      color={status === 1 ? 'red' : 'success'}
      type="link"
      loading={isEditStatusPending}
      onClick={onEditStatus}
    >
      {status === 1 ? t('common_inactive') : t('common_active')}
    </RButton>
  );
};

export default StatusButton;
