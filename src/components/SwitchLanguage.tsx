import i18n from 'i18next';
import { useTranslation } from 'react-i18next';

import LanguageIcon from '@/assets/img/icon/language.svg?react';
import useClickOutside from '@/hooks/useClickOutside';

const languageMap = {
  zh_TW: 'components_switchLanguage_chinese',
  en: 'components_switchLanguage_english'
};

const SwitchLanguage: React.FC = () => {
  const { t } = useTranslation();
  const [isClicked, setIsClicked] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
    setShowDropdown(false);
    localStorage.setItem('language', JSON.stringify(lng));
  };

  useClickOutside({
    ref: dropdownRef,
    excludeSelector: '.header-dropdown-wrapper',
    onClickOutside: () => {
      setShowDropdown(false);
      setIsClicked(false);
    },
    enabled: showDropdown
  });

  return (
    <div className="header-dropdown-wrapper">
      <div
        className="cursor-pointer flex items-center justify-center"
        onClick={() => {
          setIsClicked(!isClicked);
          setShowDropdown(!showDropdown);
        }}
      >
        <div className="text-text-icon">
          <LanguageIcon className="w-6" />
        </div>
      </div>

      {showDropdown && (
        <div ref={dropdownRef} className="header-dropdown dropdown text-xs overflow-hidden">
          {Object.entries(languageMap).map(([lang, translationKey], index) => (
            <div
              key={lang}
              className={`py-[10px] text-text-secondary text-center font-bold hover:text-text hover:bg-gray-100 cursor-pointer ${index > 0 ? 'border-t border-gray-200' : ''}`}
              onClick={() => changeLanguage(lang)}
            >
              {t(translationKey)}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default SwitchLanguage;
