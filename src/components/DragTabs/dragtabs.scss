.drag-tabs {
  &.ant-tabs {
    --ant-tabs-vertical-item-padding: 12px;
    --ant-tabs-vertical-item-margin: 0;
  }
  .ant-tabs-left > .ant-tabs-nav .ant-tabs-tab,
  .ant-tabs-right > .ant-tabs-nav .ant-tabs-tab,
  .ant-tabs-left > div > .ant-tabs-nav .ant-tabs-tab,
  .ant-tabs-right > div > .ant-tabs-nav .ant-tabs-tab {
    padding: 0;
  }

  .ant-tabs-nav {
    width: 100%;
  }
  .ant-tabs-tab:first-child .ant-tabs-tab-btn {
    margin-left: 0px;
  }
  .ant-tabs-tab {
    border-bottom: 1px solid var(--color-component-border);
  }
  .ant-tabs-tab-btn {
    width: 100%;
  }
  &.ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
    color: var(--color-text-primary);
  }
  .ant-tabs-tab:hover,
  .ant-tabs-tab:active {
    color: var(--color-text-primary);
  }
}
