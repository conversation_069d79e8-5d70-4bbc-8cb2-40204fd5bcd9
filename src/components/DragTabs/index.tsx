import './dragtabs.scss';

import { HolderOutlined } from '@ant-design/icons';
import type { DragEndEvent } from '@dnd-kit/core';
import { DndContext } from '@dnd-kit/core';
import type { SyntheticListenerMap } from '@dnd-kit/core/dist/hooks/utilities';
import { restrictToHorizontalAxis, restrictToVerticalAxis } from '@dnd-kit/modifiers';
import {
  arrayMove,
  horizontalListSortingStrategy,
  SortableContext,
  useSortable,
  verticalListSortingStrategy
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Button } from 'antd';
import React, { useContext, useMemo } from 'react';

import ArrowRightIcon from '@/assets/img/icon/arrow-right.svg?react';
import { RTabs } from '@/components/RTabs';

// Tab 拖拽上下文
interface TabContextProps {
  setActivatorNodeRef?: (element: HTMLElement | null) => void;
  listeners?: SyntheticListenerMap;
}

const TabContext = React.createContext<TabContextProps>({});

// 拖拽手柄組件
const DragHandle: React.FC = () => {
  const { setActivatorNodeRef, listeners } = useContext(TabContext);
  return (
    <Button
      type="text"
      size="small"
      icon={<HolderOutlined />}
      style={{ cursor: 'move' }}
      ref={setActivatorNodeRef}
      {...listeners}
    />
  );
};

// 可拖拽的 Tab 項目
interface DragTabProps {
  id: string;
  label: React.ReactNode;
  showArrow?: boolean;
  children?: React.ReactNode;
  activeKey?: string;
}

const DragTab: React.FC<DragTabProps> = ({ id, label, showArrow, activeKey, children }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({ id });

  const style: React.CSSProperties = {
    transform: CSS.Translate.toString(transform),
    transition,
    ...(isDragging ? { position: 'relative', zIndex: 9999 } : {})
  };

  const contextValue = useMemo<TabContextProps>(
    () => ({ setActivatorNodeRef, listeners }),
    [setActivatorNodeRef, listeners]
  );

  return (
    <TabContext.Provider value={contextValue}>
      <div ref={setNodeRef} style={style} {...attributes}>
        <div className="flex items-center gap-1">
          <DragHandle />
          <div className="flex-1">{label}</div>
          {showArrow && id === activeKey ? (
            <ArrowRightIcon className="w-4 h-4 fill-text-secondary" />
          ) : null}
        </div>
        {children}
      </div>
    </TabContext.Provider>
  );
};

// 主要的 DragTabs 組件
interface DragTabsProps<
  T extends {
    key: string;
    label: React.ReactNode;
  }
> {
  items: T[];
  onChange: (key: string) => void;
  onSortChange: (items: T[]) => void;
  tabPosition?: 'left' | 'right' | 'top' | 'bottom';
  activeKey?: string;
  dragDirection?: 'horizontal' | 'vertical';
  showArrow?: boolean;
}

const DragTabs = <
  T extends {
    key: string;
    label: React.ReactNode;
    arrow?: React.ReactNode;
  }
>({
  items,
  onChange,
  onSortChange,
  tabPosition = 'top',
  activeKey,
  dragDirection = 'horizontal',
  showArrow = false
}: DragTabsProps<T>) => {
  const onDragEnd = ({ active, over }: DragEndEvent) => {
    if (active.id !== over?.id) {
      const activeIndex = items.findIndex((item) => item.key === active.id);
      const overIndex = items.findIndex((item) => item.key === over?.id);
      const newItems = arrayMove(items, activeIndex, overIndex);
      onSortChange(newItems);
    }
  };

  // 為每個 tab 項目添加拖拽功能
  const customItems = items.map((item) => ({
    ...item,
    label: <DragTab id={item.key} label={item.label} activeKey={activeKey} showArrow={showArrow} />
  }));

  return (
    <DndContext
      modifiers={[dragDirection === 'vertical' ? restrictToVerticalAxis : restrictToHorizontalAxis]}
      onDragEnd={onDragEnd}
    >
      <SortableContext
        items={items.map((item) => item.key)}
        strategy={
          dragDirection === 'vertical' ? verticalListSortingStrategy : horizontalListSortingStrategy
        }
      >
        <RTabs
          className="drag-tabs"
          items={customItems}
          onChange={onChange}
          tabPosition={tabPosition}
          activeKey={activeKey}
        />
      </SortableContext>
    </DndContext>
  );
};

export default DragTabs;
