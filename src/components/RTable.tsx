import type { GetProp, TableColumnsType, TablePaginationConfig, TableProps } from 'antd';
import { Table } from 'antd';

interface AtndTableProps<T> extends TableProps<T> {
  dataSource: T[];
  columns: GetProp<TableProps<T>, 'columns'>;
}

export interface RTableProps<T> extends AtndTableProps<T> {
  pagination?: TablePaginationConfig | false;
  rowKey?: string;
}

export type RTableColumnsType<T> = TableColumnsType<T>;

export const RTable = <T extends object>({
  rowKey,
  dataSource,
  columns,
  pagination,
  loading,
  ...props
}: RTableProps<T>) => {
  return (
    <Table<T>
      rowKey={rowKey}
      dataSource={dataSource}
      columns={columns}
      pagination={pagination}
      loading={loading}
      {...props}
    />
  );
};

export default RTable;
