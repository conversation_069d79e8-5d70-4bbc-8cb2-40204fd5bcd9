import clsx from 'clsx';

import SearchIcon from '@/assets/img/icon/search.svg?react';

import RInput, { RInputProps } from './RInput';
interface SearchInputProps extends RInputProps {
  className?: string;
  onSearch?: (value: string) => void;
}
export const SearchInput = ({ className, onSearch }: SearchInputProps) => {
  const { t } = useTranslation();
  const [value, setValue] = useState('');

  const handleSearch = () => {
    onSearch?.(value);
  };

  const handleClear = () => {
    setValue('');
    onSearch?.('');
  };

  return (
    <RInput
      className={clsx('h-[32px] !rounded-full pl-10 pr-3 bg-gray-200', className)}
      placeholder={t('baseLayout_searchInput_placeholder')}
      variant="filled"
      allowClear
      value={value}
      onChange={(e) => setValue(e.target.value)}
      onClear={handleClear}
      onPressEnter={() => handleSearch()}
      suffix={
        <SearchIcon
          className="w-4 h-4 text-gray-400 cursor-pointer"
          onClick={() => handleSearch()}
        />
      }
    />
  );
};

export default SearchInput;
