interface SvgIconProps {
  name: string;
  color?: string;
  size?: number | string;
  className?: string;
  isShowHover?: boolean;
}

const SvgIcon: React.FC<SvgIconProps> = ({
  name,
  color,
  size = 20,
  className = '',
  isShowHover = true
}) => {
  const style = {
    height: typeof size === 'number' ? `${size}px` : size,
    width: typeof size === 'number' ? `${size}px` : size
  };

  // 使用mask來改颜色
  const svgStyle = color
    ? {
        width: '100%',
        height: '100%',
        WebkitMaskImage: `url(/src/assets/img/icon/${name}.svg)`,
        maskImage: `url(/src/assets/img/icon/${name}.svg)`,
        WebkitMaskSize: 'contain',
        maskSize: 'contain',
        WebkitMaskRepeat: 'no-repeat',
        maskRepeat: 'no-repeat',
        WebkitMaskPosition: 'center',
        maskPosition: 'center',
        backgroundColor: color
      }
    : {
        width: '100%',
        height: '100%'
      };

  return (
    <div
      className={`inline-flex items-center justify-center outline-none ${isShowHover ? 'hover:color-primary-default' : ''} ${className}`}
      style={style}
    >
      {color ? (
        <div style={svgStyle} />
      ) : (
        <img
          src={`/src/assets/img/icon/${name}.svg`}
          alt={name}
          style={{ width: '100%', height: '100%' }}
        />
      )}
    </div>
  );
};

export default SvgIcon;
