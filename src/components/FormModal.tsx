import { FormInstance } from 'antd';

import RForm, { type RFormProps } from './RForm';
import RModal from './RModal';
// 請避免同一個頁面有兩個formModal

type RecursivePartial<T> =
  NonNullable<T> extends object
    ? {
        [P in keyof T]?: NonNullable<T[P]> extends (infer U)[]
          ? RecursivePartial<U>[]
          : NonNullable<T[P]> extends object
            ? RecursivePartial<T[P]>
            : T[P];
      }
    : T;

type FormModalProps<T extends object> = {
  children: React.ReactNode;
  title: string;
  form: FormInstance<T>;
  initialValues?: RecursivePartial<T>;
  open: boolean;
  onClose: () => void;
  onSubmit: (values: T) => void;
  isLoading?: boolean;
  width?: number | string;
  formProps?: Omit<RFormProps<T>, 'form' | 'children'>;
  isReadOnly?: boolean;
};

const FormModal = <T extends object>({
  children,
  title,
  form,
  initialValues,
  open,
  onClose,
  onSubmit,
  isLoading = false,
  width = 980,
  formProps,
  isReadOnly = false
}: FormModalProps<T>) => {
  const handleClose = () => {
    form.resetFields();
    onClose();
  };

  const submit = () => {
    if (!isReadOnly) {
      form.submit();
    }
  };

  const handleSubmit = (values: T) => {
    onSubmit(values);
  };

  useEffect(() => {
    if (open) {
      if (initialValues) {
        form.setFieldsValue(structuredClone(initialValues));
      } else {
        form.resetFields();
      }
    } else {
      form.resetFields();
    }
  }, [open, form, initialValues]);

  return (
    <RModal
      centered
      maskClosable={false}
      title={title}
      open={open}
      onCancel={handleClose}
      onOk={submit}
      loading={isLoading}
      width={width}
      destroyOnClose={true}
      forceRender={true}
      okButtonProps={{
        show: !isReadOnly
      }}
    >
      <RForm<T>
        form={form}
        onFinish={handleSubmit}
        layout="vertical"
        preserve={false}
        {...formProps}
      >
        {children}
      </RForm>
    </RModal>
  );
};

export default FormModal;
