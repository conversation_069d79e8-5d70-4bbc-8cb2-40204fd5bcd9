import { type ColProps, Form, type FormItemProps, type FormProps, Spin } from 'antd';
import type { Rule } from 'antd/es/form';

// antd的props
interface AtndFormProps<T> extends FormProps<T> {
  children: React.ReactNode;
  layout?: 'horizontal' | 'vertical' | 'inline';
  name?: string;
  labelCol?: ColProps;
  wrapperCol?: ColProps;
  initialValues?: Partial<T>;
  onFinish?: (values: T) => void;
  autoComplete?: 'on' | 'off';
}

// 自定義的props
export interface RFormProps<T> extends AtndFormProps<T> {
  children: React.ReactNode;
  isLoading?: boolean;
  preserve?: boolean;
}

export const RForm = <T extends object>({
  children,
  preserve = false,
  isLoading = false,
  ...props
}: RFormProps<T>) => {
  return (
    <Spin spinning={isLoading}>
      <Form preserve={preserve} {...props}>
        {children}
      </Form>
    </Spin>
  );
};

interface AtndFormItemProps extends FormItemProps {
  children?: React.ReactNode;
  label?: React.ReactNode;
  // name?: NamePath;
  rules?: Rule[];
}

interface RFormItemProps extends AtndFormItemProps {
  children?: React.ReactNode;
}

// T = FieldTypes
export const RFormItem = <T extends object>({ children, ...props }: RFormItemProps) => {
  return <Form.Item<T> {...props}>{children}</Form.Item>;
};

RForm.Item = RFormItem;
RForm.List = Form.List;
RForm.useForm = Form.useForm;
RForm.useWatch = Form.useWatch;
RForm.useFormInstance = Form.useFormInstance;
export default RForm;
