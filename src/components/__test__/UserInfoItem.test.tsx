import '@testing-library/jest-dom';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { describe, expect, it } from 'vitest';

import UserInfoItem from '../UserInfoItem';

describe('UserInfoItem', () => {
  it('should render without crashing', () => {
    const queryClient = new QueryClient();
    const { container } = render(
      <QueryClientProvider client={queryClient}>
        <MemoryRouter>
          <UserInfoItem />
        </MemoryRouter>
      </QueryClientProvider>
    );
    expect(container).toBeInTheDocument();
  });
});
