import '@testing-library/jest-dom';

import { render } from '@testing-library/react';
import { describe, expect, it } from 'vitest';

import StatusButton from '../StatusButton';

const defaultProps = {
  status: 1,
  isEditStatusPending: false,
  onEditStatus: () => {}
};

describe('StatusButton', () => {
  it('should render without crashing', () => {
    const { container } = render(<StatusButton {...defaultProps} />);
    expect(container).toBeInTheDocument();
  });
});
