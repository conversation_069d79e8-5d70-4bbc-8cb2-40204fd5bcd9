import '@testing-library/jest-dom';

import { render } from '@testing-library/react';
import { describe, expect, it } from 'vitest';

import VerificationComponent from '../VerificationComponent';

describe('VerificationComponent', () => {
  it('should render without crashing', () => {
    const { container } = render(<VerificationComponent setOtpVerification={() => {}} />);
    expect(container).toBeInTheDocument();
  });
});
