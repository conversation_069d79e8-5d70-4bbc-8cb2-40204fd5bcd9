import { useTranslation } from 'react-i18next';

import RButton from '@/components/RButton';
import RForm from '@/components/RForm';

interface SearchFormProps<T> {
  className?: string;
  children: React.ReactNode;
  onSearch: (values: T) => void;
  onReset: () => void;
  initialValues?: Partial<T>;
}

const SearchForm = <T,>({ children, onSearch, onReset, initialValues }: SearchFormProps<T>) => {
  const { t } = useTranslation();

  const [form] = RForm.useForm();
  const handleSearch = useCallback(() => {
    const formValues = form.getFieldsValue();
    // date處理
    if (formValues?.date) {
      formValues.start = formValues.date[0];
      formValues.end = formValues.date[1];
      delete formValues.date;
    }
    const values = Object.entries(formValues).filter(
      ([, value]) => value !== undefined || value !== ''
    );
    onSearch(Object.fromEntries(values) as T);
  }, [form, onSearch]);

  const handleReset = useCallback(() => {
    form.resetFields();
    onReset();
  }, [form, onReset]);

  return (
    <RForm
      form={form}
      layout="inline"
      className="gap-y-2"
      onFinish={handleSearch}
      initialValues={initialValues}
    >
      {children}
      <RForm.Item label={null}>
        <RButton type="primary" htmlType="submit">
          {t('common_search')}
        </RButton>
        <RButton className="!ml-2" type="default" onClick={handleReset}>
          {t('common_reset')}
        </RButton>
      </RForm.Item>
    </RForm>
  );
};

export default SearchForm;
