import { Select, SelectProps } from 'antd';

interface AntdSelectProps extends SelectProps {
  placeholder?: string;
  prefix?: React.ReactNode;
}

export interface RSelectProps extends AntdSelectProps {
  options?: { label: string; value: string | number | null | undefined }[];
}

export const RSelect = ({ placeholder, options, ...props }: RSelectProps) => {
  return (
    <Select className="min-w-[140px]" placeholder={placeholder} options={options} {...props} />
  );
};

export default RSelect;
