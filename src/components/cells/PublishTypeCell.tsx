import { PublishTypeEnum } from '@/types/common';
import { formatTime } from '@/utils/time';

const PublishTypeCell = <T extends { publishType: number; startTime: number; endTime: number }>({
  record
}: {
  value: number;
  record: T;
}) => {
  const { t } = useTranslation();
  return (
    <>
      <p>
        {record.publishType === PublishTypeEnum.PERMANENT
          ? t('components_formTimePeriodSelect_publishType_permanent')
          : `${formatTime(record.startTime)}${record.endTime ? `~` : ''}`}
      </p>
      {record.endTime > 0 && <p>{`${formatTime(record.endTime)}`}</p>}
    </>
  );
};

export default PublishTypeCell;
