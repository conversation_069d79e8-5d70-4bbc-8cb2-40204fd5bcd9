import clsx from 'clsx';
import { useTranslation } from 'react-i18next';

import { MAX_CAROUSEL_AMOUNT } from '@/constants/carousel';

interface AmountCellProps {
  amount: number;
}

export const AmountCell = ({ amount }: AmountCellProps) => {
  const { t } = useTranslation();
  return (
    <p>
      <span>{t('pages_carousel_amount')}: </span>
      <span className={clsx(amount >= MAX_CAROUSEL_AMOUNT && 'text-warning')}>{amount}</span>
      <span> / {MAX_CAROUSEL_AMOUNT}</span>
    </p>
  );
};
