import { Select, type SelectProps } from 'antd';
import { useTranslation } from 'react-i18next';

interface Option {
  value: string | number;
  label: string;
}

interface MultipleSelectProps extends Omit<SelectProps, 'onChange'> {
  options: Option[];
  onChange?: (values: (string | number)[] | undefined) => void;
  width?: string | number;
  value?: (string | number)[];
  showNoneOption?: boolean;
  noneOptionLabel?: string;
}

const RMultipleSelect = ({
  options = [],
  onChange,
  width = 320,
  value,
  showNoneOption = false,
  noneOptionLabel,
  ...props
}: MultipleSelectProps) => {
  const { t } = useTranslation();
  const allOption = { value: 'all', label: t('common_all') };
  const noneOption = {
    value: 'none',
    label: noneOptionLabel ? t(noneOptionLabel) : t('common_none')
  };
  const allOptions = [allOption, ...(showNoneOption ? [noneOption] : []), ...options];

  // 將 API 的 [0] 轉換為 'none' 用於顯示
  const displayValue = value?.map((v) => (v === 0 ? 'none' : v)) || ['all'];
  const [selectedValues, setSelectedValues] = useState<(string | number)[]>(displayValue);

  useEffect(() => {
    if (value !== undefined) {
      setSelectedValues(value.map((v) => (v === 0 ? 'none' : v)));
    }
  }, [value]);

  const handleChange = useCallback(
    (values: (string | number)[]) => {
      let newValues = [...values];

      // 處理"無"選項
      if (values.includes('none')) {
        if (selectedValues.includes('none') && selectedValues.length === 1) {
          // 如果之前只選了"無"，現在選了其他選項，則移除"無"
          newValues = values.filter((v) => v !== 'none');
        } else {
          newValues = ['none'];
        }
      } else if (values.includes('all') && values.length > 1) {
        // 處理"全部"選項
        if (selectedValues.includes('all') && selectedValues.length === 1) {
          newValues = values.filter((v) => v !== 'all');
        } else {
          newValues = ['all'];
        }
      } else if (values.length === 0) {
        newValues = ['all'];
      } else if (values.length === options.length && !values.includes('all')) {
        newValues = ['all'];
      }

      setSelectedValues(newValues);

      // 處理 API payload
      if (newValues.includes('none')) {
        // 如果選中"無"，則返回 [0]
        onChange?.([0]);
      } else if (newValues.includes('all')) {
        // 如果選中"全部"，則返回 undefined
        onChange?.(undefined);
      } else {
        onChange?.(newValues);
      }
    },
    [onChange, options.length, selectedValues]
  );

  return (
    <Select
      mode="multiple"
      placeholder={t('placeholder_select')}
      style={{ width }}
      value={selectedValues}
      onChange={handleChange}
      options={allOptions}
      {...props}
    />
  );
};

export default RMultipleSelect;
