import clsx from 'clsx';
import { useMemo, useState } from 'react';

import ArrowDownIcon from '@/assets/img/icon/arrow-down.svg?react';
import RCheckbox, { type RCheckboxChangeEvent, type RCheckboxProps } from '@/components/RCheckbox';
import SearchInput from '@/components/SearchInput';
import { type Permission } from '@/types/admin';

type PermissionSelectProps = {
  value?: number[];
  onChange?: (value: number[]) => void;
  disabled?: boolean;
  data: Permission[];
};

const getLeafPermission = (data: Permission): number[] => {
  if (data?.permissions?.length) {
    return data?.permissions?.map((el) => getLeafPermission(el)).flat();
  }
  return [data.id];
};

const styleList = [
  {
    itemClass: 'py-3 border-b-2 border-dotted border-gray-200',
    labelClass: 'flex items-center justify-start gap-2.5',
    childrenClass: 'px-5  grid grid-cols-4 gap-x-2.5',
    checkboxColor: 'checkbox-info'
  },
  {
    itemClass: 'py-3',
    labelClass: 'flex gap-2.5',
    childrenClass: 'flex flex-col',
    checkboxColor: 'checkbox-primary'
  },
  {
    itemClass: 'py-1',
    labelClass: 'flex gap-2.5',
    childrenClass: 'flex flex-col',
    checkboxColor: 'checkbox-success'
  }
];

type PermissionItemProps = {
  data: Permission;
  selected: number[];
  onChange: (value: number[]) => void;
  level?: number;
  itemClass?: string;
  labelClass?: string;
  childrenClass?: string;
  hasCollapse?: boolean;
  disabled?: boolean;
};

const PermissionItem = ({
  data,
  selected,
  onChange,
  level = 1,
  hasCollapse = false,
  disabled = false
}: PermissionItemProps) => {
  const [collapse, setCollapse] = useState(false);
  const allPermissions = getLeafPermission(data);
  const checkAll = allPermissions.length > 0 && allPermissions.every((el) => selected.includes(el));
  const indeterminate =
    allPermissions.length > 0 && allPermissions.some((el) => selected.includes(el)) && !checkAll;

  const handleChange: RCheckboxProps['onChange'] = (e: RCheckboxChangeEvent) => {
    const checked = e.target.checked;
    if (checked) {
      onChange([...selected, ...allPermissions]);
    } else {
      onChange(selected.filter((el) => !allPermissions.includes(el)));
    }
  };

  const childrenBlock = data?.permissions?.map((el) => (
    <PermissionItem
      key={el.id}
      data={el}
      selected={selected}
      onChange={onChange}
      level={level + 1}
      disabled={disabled}
    />
  ));

  // const checkAll;
  return (
    <div className={styleList[level - 1].itemClass} style={{ paddingLeft: `${level * 10}px` }}>
      <div className={styleList[level - 1].labelClass}>
        <RCheckbox
          className={styleList[level - 1].checkboxColor}
          checked={checkAll}
          indeterminate={indeterminate}
          onChange={handleChange}
          disabled={disabled}
        />
        <div>{data.label}</div>
        {hasCollapse && (
          <div className="cursor-pointer" onClick={() => setCollapse(!collapse)}>
            <ArrowDownIcon
              width={12}
              height={12}
              className={clsx('fill-text-secondary', collapse && 'rotate-180')}
            />
          </div>
        )}
      </div>
      {data?.permissions && (
        <div
          className={clsx(
            'overflow-hidden transition-all duration-200',
            collapse ? 'max-h-0' : 'max-h-[1000px]',
            styleList[level - 1].childrenClass
          )}
        >
          {childrenBlock}
        </div>
      )}
    </div>
  );
};

const filterTreeData = (data: Permission[], searchValue: string) => {
  const searchValueLower = searchValue.toLowerCase();

  const filterNode = (node: Permission): Permission | null => {
    if (node.label.toLowerCase().includes(searchValueLower)) {
      return node;
    }

    if (node.permissions?.length) {
      const filteredPermissions = node.permissions
        .map(filterNode)
        .filter((child): child is Permission => child !== null);

      if (filteredPermissions.length > 0) {
        return {
          ...node,
          permissions: filteredPermissions
        };
      }
    }

    return null;
  };

  return data.map(filterNode).filter((node): node is Permission => node !== null);
};

export const PermissionSelect = ({
  value,
  onChange,
  disabled = false,
  data
}: PermissionSelectProps) => {
  const [searchValue, setSearchValue] = useState('');

  const handleChange = (value: number[]) => {
    onChange?.(value);
  };

  const filteredTreeData = useMemo(() => filterTreeData(data, searchValue), [searchValue, data]);

  return (
    <div className="h-[420px] border border-component-border rounded-xs p-4 overflow-y-auto">
      <div className="flex items-center gap-2">
        <SearchInput
          className="!w-[320px]"
          value={searchValue}
          onSearch={(value) => setSearchValue(value)}
          disabled={disabled}
        />
      </div>
      {filteredTreeData.map((el) => (
        <PermissionItem
          key={el?.id}
          data={el}
          selected={value ?? []}
          onChange={handleChange}
          hasCollapse={true}
          disabled={disabled}
        />
      ))}
    </div>
  );
};

export default PermissionSelect;
