import { Input } from 'antd';
import { InputProps } from 'antd/es/input';
import { useTranslation } from 'react-i18next';

interface AntdInputProps extends InputProps {
  size?: 'small' | 'middle' | 'large';
}

export interface RInputProps extends AntdInputProps {
  placeholder?: string;
  prefix?: React.ReactNode;
  iconRender?: (visible: boolean) => React.ReactNode;
  type?: string;
}

export const RInput = ({ placeholder, iconRender, type, ...props }: RInputProps) => {
  const { t } = useTranslation();

  if (type === 'password') {
    return (
      <Input.Password
        placeholder={placeholder || t('placeholder_input')}
        iconRender={iconRender}
        className="ant-input-password"
        {...props}
      />
    );
  }

  return <Input placeholder={placeholder || t('placeholder_input')} type={type} {...props} />;
};

RInput.TextArea = Input.TextArea;
RInput.Search = Input.Search;
export default RInput;
