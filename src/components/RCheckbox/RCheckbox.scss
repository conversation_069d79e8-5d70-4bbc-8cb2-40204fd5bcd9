.checkbox-primary {
  .ant-checkbox-checked .ant-checkbox-inner,
  &.ant-checkbox-wrapper:not(.ant-checkbox-wrapper-disabled):hover
    .ant-checkbox-checked:not(.ant-checkbox-disabled)
    .ant-checkbox-inner {
    background-color: var(--color-primary);
  }
  .ant-checkbox-indeterminate .ant-checkbox-inner:after {
    background-color: var(--color-primary);
  }
  .ant-checkbox-disabled.ant-checkbox-checked .ant-checkbox-inner:after {
    border-color: white;
  }
}
.checkbox-success {
  .ant-checkbox-checked .ant-checkbox-inner,
  &.ant-checkbox-wrapper:not(.ant-checkbox-wrapper-disabled):hover
    .ant-checkbox-checked:not(.ant-checkbox-disabled)
    .ant-checkbox-inner {
    background-color: #7dc98c;
  }
  .ant-checkbox-indeterminate .ant-checkbox-inner:after {
    background-color: #7dc98c;
  }
  .ant-checkbox-disabled.ant-checkbox-checked .ant-checkbox-inner:after {
    border-color: white;
  }
}
