import './RCheckbox.scss';

import { Checkbox, CheckboxChangeEvent, CheckboxProps } from 'antd';
type antdCheckboxProps = CheckboxProps;

export type RCheckboxProps = antdCheckboxProps;
export type RCheckboxChangeEvent = CheckboxChangeEvent;

export const RCheckbox = ({ ...props }: RCheckboxProps) => {
  return <Checkbox {...props}>{props.children}</Checkbox>;
};

RCheckbox.Group = Checkbox.Group;

export default RCheckbox;
