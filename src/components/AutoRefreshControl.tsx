import { useTranslation } from 'react-i18next';

import { AutoRefreshOptions, useAutoRefresh } from '@/hooks/useAutoRefresh';
import { cn } from '@/utils/classname';

import RButton from './RButton';

export interface AutoRefreshControlProps extends Omit<AutoRefreshOptions, 'onRefresh'> {
  onRefresh: () => void;
  className?: string;
  intervalOptions?: { key: string; label: string; value: number }[];
  isShowManualRefresh?: boolean;
}

type QuickSelectItem = {
  key: string;
  label: string;
  value: number; // milliseconds
};

const QuickSelectItemList: QuickSelectItem[] = [
  {
    key: '10_seconds',
    label: 'components_autoRefresh_interval_10s',
    value: 10000
  },
  {
    key: '30_seconds',
    label: 'components_autoRefresh_interval_30s',
    value: 30000
  },
  {
    key: '60_seconds',
    label: 'components_autoRefresh_interval_60s',
    value: 60000
  },
  {
    key: '300_seconds',
    label: 'components_autoRefresh_interval_300s',
    value: 300000
  },
  {
    key: 'none',
    label: 'components_autoRefresh_interval_none',
    value: -1
  }
];

const QuickTimeButtonList = ({
  activeKey,
  onChange,
  intervalOptions
}: {
  activeKey: string;
  onChange: (key: string, value: number) => void;
  intervalOptions: QuickSelectItem[];
}) => {
  const { t } = useTranslation();
  const handleQuickTimeClick = useCallback(
    (key: string, value: number) => {
      onChange(key, value);
    },
    [onChange]
  );
  const button = (item: QuickSelectItem) => {
    return (
      <div
        className={cn(
          'px-3 py-2 cursor-pointer text-text-secondary hover:bg-bg-info text-xs text-nowrap',
          activeKey === item.key &&
            'bg-text-secondary text-white hover:bg-text-secondary rounded-sm'
        )}
        key={item.key}
        onClick={() => handleQuickTimeClick(item.key, item.value)}
      >
        {t(item.label)}
      </div>
    );
  };
  return (
    <div className="flex border rounded-sm border-component-border">
      {intervalOptions.map((item) => button(item))}
    </div>
  );
};

const defaultOption = QuickSelectItemList[0];

export const AutoRefreshControl = ({
  onRefresh,
  enabled = false,
  interval = defaultOption.value,
  className = '',
  intervalOptions = QuickSelectItemList,
  isShowManualRefresh = false
}: AutoRefreshControlProps) => {
  const { t } = useTranslation();

  const [activeKey, setActiveKey] = useState<string>(defaultOption.key);

  const autoRefresh = useAutoRefresh({
    enabled: enabled && activeKey !== 'none',
    interval,
    onRefresh
  });

  const handleQuickChange = useCallback((key: string, value: number) => {
    setActiveKey(key);
    if (value === -1) {
      return;
    }
    autoRefresh.setInterval(value);
  }, []);

  useEffect(() => {
    handleQuickChange(defaultOption.key, defaultOption.value);
  }, [handleQuickChange]);

  const formatTimeRemaining = (timeMs: number) => {
    const seconds = Math.ceil(timeMs / 1000);
    return `${seconds}${t('components_autoRefresh_second')}`;
  };

  return (
    <div className={`flex items-center gap-3 ${className}`}>
      <QuickTimeButtonList
        activeKey={activeKey}
        onChange={handleQuickChange}
        intervalOptions={intervalOptions}
      />
      <span className="text-xs text-gray-500">
        {formatTimeRemaining(autoRefresh.timeRemaining)}
      </span>

      {isShowManualRefresh && (
        <RButton color="primary" onClick={autoRefresh.refresh}>
          {t('components_autoRefresh_manual_refresh')}
        </RButton>
      )}
    </div>
  );
};

export default AutoRefreshControl;
