import './RDropdown.scss';

import { Button, ButtonProps, Dropdown, MenuProps } from 'antd';
export interface DropdownItem {
  key: string;
  label: string;
  onClick?: () => void;
  disabled?: boolean;
}

interface ButtonDropdownProps extends ButtonProps {
  items: DropdownItem[];
  buttonText?: string;
  className?: string;
  icon?: React.ReactNode;
  isShowIcon?: boolean;
  style?: React.CSSProperties;
  buttonProps?: ButtonProps;
  placement?: 'bottomLeft' | 'bottomCenter' | 'bottomRight' | 'topLeft' | 'topCenter' | 'topRight';
}

const ButtonDropdown: React.FC<ButtonDropdownProps> = ({
  items,
  buttonText,
  className = '',
  icon,
  isShowIcon = true,
  buttonProps,
  placement = 'bottomLeft'
}) => {
  const menuItems: MenuProps['items'] = items;

  return (
    <Dropdown rootClassName="my-custom-dropdown" menu={{ items: menuItems }} placement={placement}>
      <Button
        className={className}
        icon={isShowIcon ? icon : undefined}
        style={buttonProps?.style}
        {...buttonProps}
      >
        {buttonText}
      </Button>
    </Dropdown>
  );
};

export default ButtonDropdown;
