import tailwindcss from '@tailwindcss/vite';
import react from '@vitejs/plugin-react';
import AutoImport from 'unplugin-auto-import/vite';
import { defineConfig } from 'vite';
import { configDefaults } from 'vitest/config';
import Pages from 'vite-plugin-pages';
import svgr from 'vite-plugin-svgr';
// https://vite.dev/config/
let prefixCounter = 0;
export default defineConfig({
  server: {
    port: 5174
  },
  test: {
    environment: 'jsdom',
    globals: true,
    setupFiles: ['./src/setupTests.ts'],
    exclude: [...configDefaults.exclude, 'dist/**', 'node_modules/**'],
    coverage: {
      reporter: ['text', 'json', 'html']
    }
  },
  plugins: [
    react(),
    svgr({
      svgrOptions: {
        plugins: ['@svgr/plugin-svgo', '@svgr/plugin-jsx'],
        // exportType: 'named',
        // ref: true,
        svgo: true,
        svgoConfig: {
          plugins: [
            {
              name: 'preset-default',
              params: {
                overrides: {
                  removeViewBox: false
                }
              }
            },
            {
              name: 'prefixIds',
              params: {
                delim: '',
                prefix: () => `${prefixCounter++}`
              }
            }
          ]
        }
      },
      include: '**/*.svg?react'
    }),
    Pages({
      dirs: [
        { dir: 'src/pages', baseRoute: '', filePattern: '**/index.tsx' },
        { dir: 'src/pages', baseRoute: '', filePattern: '**/[...all].tsx' }
      ],
      extensions: ['tsx'],
      exclude: ['**/components/**'],
      importMode: 'async'
    }),
    tailwindcss(),
    AutoImport({
      imports: ['react', 'react-router-dom', 'react-i18next'],
      dts: './auto-imports.d.ts'
    })
  ],
  css: {
    postcss: './postcss.config.js'
  },
  resolve: {
    alias: {
      '@': '/src'
    }
  }
});
